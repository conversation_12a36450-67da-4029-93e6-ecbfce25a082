"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 重复第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n    }\n    /**\n   * 第三级提醒：确认是否已服药\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 询问是否已服药\n        const confirmationText = \"您是否已经服用了\".concat(notification.medicineName, \"？\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示确认对话框\n        this.notifyUI('confirmation-dialog', notification);\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ playReminderSound() {\n        try {\n            const audio = new Audio('/sounds/reminder.mp3');\n            audio.volume = 0.7;\n            audio.play().catch((error)=>{\n                console.error('播放提醒声音失败:', error);\n            });\n        } catch (error) {\n            console.error('创建音频对象失败:', error);\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        if (true) {\n            this.initializeNotifications();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\nconst notificationService = getNotificationService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});