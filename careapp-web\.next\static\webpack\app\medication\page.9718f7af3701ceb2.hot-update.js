"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx":
/*!*********************************************************!*\
  !*** ./src/components/medication/MedicineInputForm.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MedicineInputForm: () => (/* binding */ MedicineInputForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.mjs\");\n/* harmony import */ var _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n/* harmony import */ var _store_medication__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/medication */ \"(app-pages-browser)/./src/store/medication.ts\");\n/* __next_internal_client_entry_do_not_use__ MedicineInputForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MedicineInputForm(param) {\n    let { userId, onSuccess, onCancel } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        medicineName: '',\n        dosage: '',\n        usage: '',\n        frequency: '',\n        duration: ''\n    });\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [speechSupported, setSpeechSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceTranscript, setVoiceTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [confidence, setConfidence] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addReminder, calculateMedicationTimes, loading, error, clearError } = (0,_store_medication__WEBPACK_IMPORTED_MODULE_3__.useMedication)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicineInputForm.useEffect\": ()=>{\n            setSpeechSupported(_lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.isSpeechRecognitionSupported());\n        }\n    }[\"MedicineInputForm.useEffect\"], []);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const handleVoiceInput = async ()=>{\n        if (!speechSupported) {\n            alert('您的浏览器不支持语音识别功能');\n            return;\n        }\n        if (isListening) {\n            _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.stopListening();\n            setIsListening(false);\n            return;\n        }\n        try {\n            // 请求麦克风权限\n            const hasPermission = await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.requestMicrophonePermission();\n            if (!hasPermission) {\n                alert('需要麦克风权限才能使用语音输入功能');\n                return;\n            }\n            setIsListening(true);\n            setVoiceTranscript('');\n            await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.startListening((result)=>{\n                setVoiceTranscript(result.transcript);\n                if (result.isFinal) {\n                    // 解析语音输入（增强版）\n                    const parsed = _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.parseMedicineInput(result.transcript);\n                    // 设置置信度和建议\n                    setConfidence(parsed.confidence || 0);\n                    setSuggestions(parsed.suggestions || []);\n                    setShowSuggestions((parsed.confidence || 0) < 0.8 && (parsed.suggestions || []).length > 0);\n                    setFormData((prev)=>({\n                            ...prev,\n                            medicineName: parsed.medicineName || prev.medicineName,\n                            dosage: parsed.dosage || prev.dosage,\n                            usage: parsed.usage || prev.usage,\n                            frequency: parsed.frequency || prev.frequency\n                        }));\n                    setIsListening(false);\n                }\n            }, (error)=>{\n                console.error('语音识别错误:', error);\n                setIsListening(false);\n                alert(\"语音识别失败: \".concat(error));\n            });\n        } catch (error) {\n            console.error('启动语音识别失败:', error);\n            setIsListening(false);\n            alert('启动语音识别失败，请检查麦克风权限');\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.medicineName.trim()) {\n            newErrors.medicineName = '请输入药品名称';\n        }\n        if (!formData.dosage.trim()) {\n            newErrors.dosage = '请输入用药剂量';\n        }\n        if (!formData.usage.trim()) {\n            newErrors.usage = '请输入用药规则';\n        }\n        if (!formData.frequency.trim()) {\n            newErrors.frequency = '请输入用药频次';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        try {\n            clearError();\n            // 计算用药时间\n            const scheduledTimes = calculateMedicationTimes(formData.usage, formData.frequency);\n            await addReminder({\n                medicineName: formData.medicineName.trim(),\n                dosage: formData.dosage.trim(),\n                usage: formData.usage.trim(),\n                frequency: formData.frequency.trim(),\n                duration: formData.duration ? parseInt(formData.duration) : undefined,\n                scheduledTimes,\n                isEnabled: true,\n                userId\n            });\n            // 重置表单\n            setFormData({\n                medicineName: '',\n                dosage: '',\n                usage: '',\n                frequency: '',\n                duration: ''\n            });\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            console.error('添加用药提醒失败:', error);\n        }\n    };\n    const usageOptions = [\n        '餐前30分钟',\n        '餐后1小时',\n        '随餐服用',\n        '睡前30分钟',\n        '晨起服用',\n        '空腹服用'\n    ];\n    const frequencyOptions = [\n        '每日1次',\n        '每日2次',\n        '每日3次',\n        '每日4次',\n        '每周2次',\n        '每周3次'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800\",\n                        children: \"添加用药提醒\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    speechSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleVoiceInput,\n                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors \".concat(isListening ? 'bg-red-500 text-white' : 'bg-blue-500 text-white hover:bg-blue-600'),\n                        children: [\n                            isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 66\n                            }, this),\n                            isListening ? '停止录音' : '语音输入'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            voiceTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"语音识别结果：\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        voiceTranscript\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"药品名称 *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.medicineName,\n                                onChange: (e)=>handleInputChange('medicineName', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.medicineName ? 'border-red-300' : 'border-gray-300'),\n                                placeholder: \"例如：阿司匹林肠溶片\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            errors.medicineName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.medicineName\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"用药剂量 *\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.dosage,\n                                onChange: (e)=>handleInputChange('dosage', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.dosage ? 'border-red-300' : 'border-gray-300'),\n                                placeholder: \"例如：1片、2ml、100mg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            errors.dosage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.dosage\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"用药规则 *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.usage,\n                                onChange: (e)=>handleInputChange('usage', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.usage ? 'border-red-300' : 'border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"请选择用药规则\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    usageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.usage,\n                                onChange: (e)=>handleInputChange('usage', e.target.value),\n                                className: \"w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"或自定义用药规则\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            errors.usage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.usage\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"用药频次 *\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.frequency,\n                                onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.frequency ? 'border-red-300' : 'border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"请选择用药频次\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    frequencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.frequency,\n                                onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                className: \"w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"或自定义用药频次\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            errors.frequency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.frequency\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"疗程时长（可选）\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                value: formData.duration,\n                                onChange: (e)=>handleInputChange('duration', e.target.value),\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"请输入疗程天数\",\n                                min: \"1\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"添加提醒\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            speechSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-800 mb-2\",\n                        children: \"语音输入提示：\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: '您可以说：\"阿司匹林肠溶片，每次1片，餐后1小时服用，每日3次\"'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicineInputForm, \"QEmjyBvFEGpQ1/QEMGqYO17MO+Y=\", false, function() {\n    return [\n        _store_medication__WEBPACK_IMPORTED_MODULE_3__.useMedication\n    ];\n});\n_c = MedicineInputForm;\nvar _c;\n$RefreshReg$(_c, \"MedicineInputForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx\n"));

/***/ })

});