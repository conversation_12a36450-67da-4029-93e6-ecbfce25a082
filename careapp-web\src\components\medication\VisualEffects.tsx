'use client'

import { useEffect, useState } from 'react'

interface VisualEffectsProps {
  isActive: boolean
  level: 1 | 2 | 3
  onComplete?: () => void
}

export function VisualEffects({ isActive, level, onComplete }: VisualEffectsProps) {
  const [showFlash, setShowFlash] = useState(false)
  const [flashCount, setFlashCount] = useState(0)

  useEffect(() => {
    if (!isActive) {
      setShowFlash(false)
      setFlashCount(0)
      return
    }

    // 根据级别设置闪烁次数
    const maxFlashes = level * 2
    let currentFlash = 0

    const flashInterval = setInterval(() => {
      if (currentFlash >= maxFlashes) {
        clearInterval(flashInterval)
        setShowFlash(false)
        onComplete?.()
        return
      }

      setShowFlash(prev => !prev)
      setFlashCount(currentFlash)
      currentFlash++
    }, 300)

    return () => clearInterval(flashInterval)
  }, [isActive, level, onComplete])

  if (!isActive) return null

  const getFlashColor = () => {
    switch (level) {
      case 1: return 'bg-blue-500'
      case 2: return 'bg-orange-500'
      case 3: return 'bg-red-500'
      default: return 'bg-blue-500'
    }
  }

  const getAnimationClass = () => {
    switch (level) {
      case 1: return 'animate-pulse'
      case 2: return 'animate-bounce'
      case 3: return 'animate-ping'
      default: return 'animate-pulse'
    }
  }

  return (
    <>
      {/* 全屏闪烁效果 */}
      {showFlash && (
        <div 
          className={`fixed inset-0 z-50 pointer-events-none ${getFlashColor()} opacity-20 transition-opacity duration-150`}
        />
      )}

      {/* 边框闪烁效果 */}
      {isActive && (
        <div 
          className={`fixed inset-0 z-40 pointer-events-none border-8 ${
            level === 3 ? 'border-red-500' : 
            level === 2 ? 'border-orange-500' : 
            'border-blue-500'
          } ${getAnimationClass()}`}
        />
      )}

      {/* 角落指示器 */}
      {isActive && (
        <>
          {/* 左上角 */}
          <div className={`fixed top-4 left-4 z-50 w-4 h-4 rounded-full ${getFlashColor()} ${getAnimationClass()}`} />
          
          {/* 右上角 */}
          <div className={`fixed top-4 right-4 z-50 w-4 h-4 rounded-full ${getFlashColor()} ${getAnimationClass()}`} />
          
          {/* 左下角 */}
          <div className={`fixed bottom-4 left-4 z-50 w-4 h-4 rounded-full ${getFlashColor()} ${getAnimationClass()}`} />
          
          {/* 右下角 */}
          <div className={`fixed bottom-4 right-4 z-50 w-4 h-4 rounded-full ${getFlashColor()} ${getAnimationClass()}`} />
        </>
      )}

      {/* 中心脉冲效果 */}
      {isActive && level >= 2 && (
        <div className="fixed inset-0 z-30 pointer-events-none flex items-center justify-center">
          <div className={`w-32 h-32 rounded-full ${getFlashColor()} opacity-30 ${getAnimationClass()}`} />
        </div>
      )}

      {/* 波纹效果 */}
      {isActive && level === 3 && (
        <div className="fixed inset-0 z-20 pointer-events-none flex items-center justify-center">
          <div className="relative">
            <div className="absolute w-64 h-64 rounded-full border-4 border-red-500 opacity-50 animate-ping" />
            <div className="absolute w-48 h-48 rounded-full border-4 border-red-400 opacity-40 animate-ping animation-delay-200" />
            <div className="absolute w-32 h-32 rounded-full border-4 border-red-300 opacity-30 animate-ping animation-delay-400" />
          </div>
        </div>
      )}
    </>
  )
}

// 添加自定义CSS动画延迟类
const style = `
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  .animation-delay-400 {
    animation-delay: 400ms;
  }
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = style
  document.head.appendChild(styleElement)
}
