'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function TestAuthPage() {
  const [message, setMessage] = useState('')
  const router = useRouter()

  const testRegister = () => {
    setMessage('模拟注册成功！')
    setTimeout(() => {
      router.replace('/test-dashboard')
    }, 1000)
  }

  const testLogin = () => {
    setMessage('模拟登录成功！')
    setTimeout(() => {
      router.replace('/test-dashboard')
    }, 1000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6">认证测试页面</h1>
        
        {message && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <p className="text-green-600 text-sm">{message}</p>
          </div>
        )}

        <div className="space-y-4">
          <button
            type="button"
            onClick={testRegister}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
          >
            测试注册
          </button>
          
          <button
            type="button"
            onClick={testLogin}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700"
          >
            测试登录
          </button>

          <button
            type="button"
            onClick={() => router.replace('/')}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
          >
            返回首页
          </button>
        </div>
      </div>
    </div>
  )
}
