'use client'

import { useState, useEffect } from 'react'
import { Calendar, Clock, CheckCircle, XCircle, AlertCircle, Filter, Search } from 'lucide-react'
import { useMedicationStore } from '@/store/medication'
import type { MedicationRecord } from '@/types'

interface MedicationHistoryProps {
  userId: string
}

export function MedicationHistory({ userId }: MedicationHistoryProps) {
  const { medicationRecords, getMedicationRecords } = useMedicationStore()
  const [filteredRecords, setFilteredRecords] = useState<MedicationRecord[]>([])
  const [statusFilter, setStatusFilter] = useState<'all' | 'taken' | 'missed' | 'skipped'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'week' | 'month'>('all')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadHistory()
  }, [userId])

  useEffect(() => {
    applyFilters()
  }, [medicationRecords, statusFilter, searchTerm, dateFilter])

  const loadHistory = async () => {
    setLoading(true)
    try {
      await getMedicationRecords(userId)
    } catch (error) {
      console.error('加载用药历史失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...medicationRecords]

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(record => record.status === statusFilter)
    }

    // 搜索过滤（这里需要根据reminderId获取药品名称）
    if (searchTerm) {
      filtered = filtered.filter(record => 
        record.reminderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.notes?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // 日期过滤
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
      }
      
      filtered = filtered.filter(record => 
        new Date(record.scheduledTime) >= filterDate
      )
    }

    // 按时间倒序排列
    filtered.sort((a, b) => 
      new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime()
    )

    setFilteredRecords(filtered)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'taken':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'missed':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'skipped':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'taken':
        return '已服药'
      case 'missed':
        return '错过'
      case 'skipped':
        return '跳过'
      case 'pending':
        return '待服药'
      default:
        return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'taken':
        return 'bg-green-100 text-green-800'
      case 'missed':
        return 'bg-red-100 text-red-800'
      case 'skipped':
        return 'bg-yellow-100 text-yellow-800'
      case 'pending':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <Calendar className="w-6 h-6 mr-2" />
          用药历史
        </h2>
        <div className="text-sm text-gray-600">
          共 {filteredRecords.length} 条记录
        </div>
      </div>

      {/* 过滤器 */}
      <div className="mb-6 space-y-4">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="搜索药品名称或备注..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* 过滤选项 */}
        <div className="flex flex-wrap gap-4">
          {/* 状态过滤 */}
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">状态:</span>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value="all">全部</option>
              <option value="taken">已服药</option>
              <option value="missed">错过</option>
              <option value="skipped">跳过</option>
            </select>
          </div>

          {/* 日期过滤 */}
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">时间:</span>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value="all">全部</option>
              <option value="today">今天</option>
              <option value="week">近7天</option>
              <option value="month">近30天</option>
            </select>
          </div>
        </div>
      </div>

      {/* 记录列表 */}
      {filteredRecords.length === 0 ? (
        <div className="text-center py-12">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">暂无用药记录</p>
          <p className="text-sm text-gray-500 mt-2">
            {medicationRecords.length === 0 ? '还没有任何用药记录' : '没有符合条件的记录'}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredRecords.map((record) => (
            <div key={record.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getStatusIcon(record.status)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-medium text-gray-800">
                        {record.reminderId} {/* 这里应该显示药品名称 */}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                        {getStatusText(record.status)}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          计划时间: {new Date(record.scheduledTime).toLocaleString('zh-CN')}
                        </span>
                        {record.actualTime && (
                          <span className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            实际时间: {new Date(record.actualTime).toLocaleString('zh-CN')}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <span>确认方式: {
                          record.confirmationMethod === 'manual' ? '手动确认' :
                          record.confirmationMethod === 'auto' ? '自动确认' :
                          record.confirmationMethod === 'guardian' ? '监护人确认' : '未知'
                        }</span>
                      </div>
                      
                      {record.notes && (
                        <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                          <strong>备注:</strong> {record.notes}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="text-xs text-gray-500">
                  {new Date(record.createdAt).toLocaleDateString('zh-CN')}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 分页（如果记录很多的话） */}
      {filteredRecords.length > 20 && (
        <div className="mt-6 flex justify-center">
          <button
            type="button"
            className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800"
          >
            加载更多
          </button>
        </div>
      )}
    </div>
  )
}
