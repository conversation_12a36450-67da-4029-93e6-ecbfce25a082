"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/play.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Play)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Play = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Play\", [\n    [\n        \"polygon\",\n        {\n            points: \"5 3 19 12 5 21 5 3\",\n            key: \"191637\"\n        }\n    ]\n]);\n //# sourceMappingURL=play.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGxheS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxhQUFPLGlFQUFnQixDQUFDLE1BQVE7SUFDcEM7UUFBQyxTQUFXO1FBQUE7WUFBRSxRQUFRLENBQXNCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUM1RCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcaWNvbnNcXHBsYXkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBQbGF5XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVdkdmJpQndiMmx1ZEhNOUlqVWdNeUF4T1NBeE1pQTFJREl4SURVZ015SWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9wbGF5XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUGxheSA9IGNyZWF0ZUx1Y2lkZUljb24oJ1BsYXknLCBbXG4gIFsncG9seWdvbicsIHsgcG9pbnRzOiAnNSAzIDE5IDEyIDUgMjEgNSAzJywga2V5OiAnMTkxNjM3JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBQbGF5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/smartphone.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Smartphone)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Smartphone = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Smartphone\", [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"20\",\n            x: \"5\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1yt0o3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 18h.01\",\n            key: \"mhygvu\"\n        }\n    ]\n]);\n //# sourceMappingURL=smartphone.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-x.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VolumeX)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst VolumeX = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"VolumeX\", [\n    [\n        \"polygon\",\n        {\n            points: \"11 5 6 9 2 9 2 15 6 15 11 19 11 5\",\n            key: \"16drj5\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"22\",\n            x2: \"16\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"1ewh16\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"22\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"5ykzw1\"\n        }\n    ]\n]);\n //# sourceMappingURL=volume-x.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Zap = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Zap\", [\n    [\n        \"polygon\",\n        {\n            points: \"13 2 3 14 12 14 11 22 21 10 12 10 13 2\",\n            key: \"45s27k\"\n        }\n    ]\n]);\n //# sourceMappingURL=zap.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvemFwLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWFNLFlBQU0saUVBQWdCLENBQUMsS0FBTztJQUNsQztRQUNFO1FBQ0E7WUFBRSxPQUFRLHlDQUEwQztZQUFBLEtBQUssUUFBUztRQUFBO0tBQ3BFO0NBQ0QiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGljb25zXFx6YXAudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBaYXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHOXNlV2R2YmlCd2IybHVkSE05SWpFeklESWdNeUF4TkNBeE1pQXhOQ0F4TVNBeU1pQXlNU0F4TUNBeE1pQXhNQ0F4TXlBeUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3phcFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFphcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ1phcCcsIFtcbiAgW1xuICAgICdwb2x5Z29uJyxcbiAgICB7IHBvaW50czogJzEzIDIgMyAxNCAxMiAxNCAxMSAyMiAyMSAxMCAxMiAxMCAxMyAyJywga2V5OiAnNDVzMjdrJyB9LFxuICBdLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IFphcDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/medication/page.tsx":
/*!*************************************!*\
  !*** ./src/app/medication/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MedicationPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _store_medication__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/medication */ \"(app-pages-browser)/./src/store/medication.ts\");\n/* harmony import */ var _lib_notification_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/notification-service */ \"(app-pages-browser)/./src/lib/notification-service.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell-off.mjs\");\n/* harmony import */ var _components_medication_MedicineInputForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/medication/MedicineInputForm */ \"(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx\");\n/* harmony import */ var _components_medication_DailyScheduleForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/medication/DailyScheduleForm */ \"(app-pages-browser)/./src/components/medication/DailyScheduleForm.tsx\");\n/* harmony import */ var _components_medication_MedicineReminderList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/medication/MedicineReminderList */ \"(app-pages-browser)/./src/components/medication/MedicineReminderList.tsx\");\n/* harmony import */ var _components_medication_ReminderManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/medication/ReminderManager */ \"(app-pages-browser)/./src/components/medication/ReminderManager.tsx\");\n/* harmony import */ var _components_medication_MedicationStatistics__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/medication/MedicationStatistics */ \"(app-pages-browser)/./src/components/medication/MedicationStatistics.tsx\");\n/* harmony import */ var _components_medication_MedicationHistory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/medication/MedicationHistory */ \"(app-pages-browser)/./src/components/medication/MedicationHistory.tsx\");\n/* harmony import */ var _components_medication_AudioSettings__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/medication/AudioSettings */ \"(app-pages-browser)/./src/components/medication/AudioSettings.tsx\");\n/* harmony import */ var _components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/medication/ReminderPopup */ \"(app-pages-browser)/./src/components/medication/ReminderPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 强制动态渲染\nconst dynamic = 'force-dynamic';\nfunction MedicationPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [showReminderPopup, setShowReminderPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConfirmationDialog, setShowConfirmationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [reminderSystemActive, setReminderSystemActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingReminder, setEditingReminder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, initialized, initialize } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { reminders, dailySchedule, reminderSettings, startReminderSystem, stopReminderSystem, loadDailySchedule, loadReminderSettings } = (0,_store_medication__WEBPACK_IMPORTED_MODULE_4__.useMedication)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            setMounted(true);\n            initialize();\n        }\n    }[\"MedicationPage.useEffect\"], [\n        initialize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            if (mounted && initialized && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"MedicationPage.useEffect\"], [\n        user,\n        initialized,\n        router,\n        mounted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            if (user) {\n                loadDailySchedule(user.id);\n                loadReminderSettings(user.id);\n            }\n        }\n    }[\"MedicationPage.useEffect\"], [\n        user,\n        loadDailySchedule,\n        loadReminderSettings\n    ]);\n    // 监听提醒事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            const handleReminderPopup = {\n                \"MedicationPage.useEffect.handleReminderPopup\": (event)=>{\n                    setShowReminderPopup(event.detail);\n                }\n            }[\"MedicationPage.useEffect.handleReminderPopup\"];\n            const handleConfirmationDialog = {\n                \"MedicationPage.useEffect.handleConfirmationDialog\": (event)=>{\n                    setShowConfirmationDialog(event.detail);\n                }\n            }[\"MedicationPage.useEffect.handleConfirmationDialog\"];\n            const handleReminderConfirmed = {\n                \"MedicationPage.useEffect.handleReminderConfirmed\": (event)=>{\n                    setShowReminderPopup(null);\n                    setShowConfirmationDialog(null);\n                }\n            }[\"MedicationPage.useEffect.handleReminderConfirmed\"];\n            window.addEventListener('medication-reminder-popup', handleReminderPopup);\n            window.addEventListener('medication-confirmation-dialog', handleConfirmationDialog);\n            window.addEventListener('medication-reminder-confirmed', handleReminderConfirmed);\n            return ({\n                \"MedicationPage.useEffect\": ()=>{\n                    window.removeEventListener('medication-reminder-popup', handleReminderPopup);\n                    window.removeEventListener('medication-confirmation-dialog', handleConfirmationDialog);\n                    window.removeEventListener('medication-reminder-confirmed', handleReminderConfirmed);\n                }\n            })[\"MedicationPage.useEffect\"];\n        }\n    }[\"MedicationPage.useEffect\"], []);\n    const handleStartReminderSystem = async ()=>{\n        if (!user) return;\n        try {\n            // 请求通知权限\n            const notificationService = (0,_lib_notification_service__WEBPACK_IMPORTED_MODULE_5__.getNotificationService)();\n            const hasPermission = await notificationService.requestNotificationPermission();\n            if (!hasPermission) {\n                alert('需要通知权限才能启用提醒系统');\n                return;\n            }\n            await startReminderSystem(user.id);\n            setReminderSystemActive(true);\n        } catch (error) {\n            console.error('启动提醒系统失败:', error);\n            alert('启动提醒系统失败，请检查设置');\n        }\n    };\n    const handleStopReminderSystem = ()=>{\n        stopReminderSystem();\n        setReminderSystemActive(false);\n    };\n    const handleReminderConfirm = (confirmed)=>{\n        if (showReminderPopup && user) {\n            // 这里可以记录用药状态\n            console.log(\"用药确认: \".concat(showReminderPopup.medicineName, \" - \").concat(confirmed ? '已服药' : '未服药'));\n        }\n        setShowReminderPopup(null);\n    };\n    const handleConfirmationSubmit = (confirmed)=>{\n        if (showConfirmationDialog && user) {\n            // 记录用药状态\n            console.log(\"用药确认: \".concat(showConfirmationDialog.medicineName, \" - \").concat(confirmed ? '已服药' : '错过'));\n        }\n        setShowConfirmationDialog(null);\n    };\n    // 测试提醒功能\n    const testReminder = (level)=>{\n        const testNotification = {\n            id: \"test-\".concat(Date.now()),\n            reminderId: 'test-reminder',\n            medicineName: '测试药物',\n            dosage: '1片',\n            usage: '餐后服用',\n            scheduledTime: new Date().toLocaleTimeString('zh-CN', {\n                hour: '2-digit',\n                minute: '2-digit'\n            }),\n            level,\n            isActive: true,\n            createdAt: new Date()\n        };\n        // 触发相应级别的提醒\n        const eventName = level === 3 ? 'confirmation-dialog-urgent' : level === 2 ? 'reminder-popup-urgent' : 'reminder-popup';\n        window.dispatchEvent(new CustomEvent(\"medication-\".concat(eventName), {\n            detail: testNotification\n        }));\n    };\n    if (!mounted || !initialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载用药提醒系统...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    const tabs = [\n        {\n            id: 'list',\n            label: '提醒列表',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: 'add',\n            label: '添加提醒',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: 'schedule',\n            label: '作息设置',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'statistics',\n            label: '用药统计',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        },\n        {\n            id: 'history',\n            label: '用药历史',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n        },\n        {\n            id: 'settings',\n            label: '提醒设置',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-500 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                children: \"用药提醒系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"智能用药管理，健康生活助手\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"提醒系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: reminderSystemActive ? handleStopReminderSystem : handleStartReminderSystem,\n                                                className: \"flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors \".concat(reminderSystemActive ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'),\n                                                children: reminderSystemActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"已启用\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"已禁用\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/dashboard'),\n                                        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: \"返回主页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-8\",\n                        children: tabs.map((tab)=>{\n                            const IconComponent = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center px-4 py-4 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    activeTab === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicineReminderList__WEBPACK_IMPORTED_MODULE_8__.MedicineReminderList, {\n                        userId: user.id,\n                        onEdit: (reminder)=>{\n                            setEditingReminder(reminder);\n                            setActiveTab('add');\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicineInputForm__WEBPACK_IMPORTED_MODULE_6__.MedicineInputForm, {\n                        userId: user.id,\n                        onSuccess: ()=>{\n                            setActiveTab('list');\n                            setEditingReminder(null);\n                        },\n                        onCancel: ()=>{\n                            setActiveTab('list');\n                            setEditingReminder(null);\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'schedule' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_DailyScheduleForm__WEBPACK_IMPORTED_MODULE_7__.DailyScheduleForm, {\n                        userId: user.id,\n                        onSuccess: ()=>{\n                        // 可以选择切换到其他标签页或显示成功消息\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'statistics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicationStatistics__WEBPACK_IMPORTED_MODULE_10__.MedicationStatistics, {\n                        userId: user.id\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicationHistory__WEBPACK_IMPORTED_MODULE_11__.MedicationHistory, {\n                        userId: user.id\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_AudioSettings__WEBPACK_IMPORTED_MODULE_12__.AudioSettings, {}, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"快速测试提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(1),\n                                                className: \"flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: \"测试第一级提醒\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(2),\n                                                className: \"flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                                                children: \"测试第二级提醒\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(3),\n                                                className: \"flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                                children: \"测试第三级提醒\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"\\uD83D\\uDCA1 这些测试按钮会触发相应级别的提醒效果，包括音效、震动和闪烁。\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            showReminderPopup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_13__.ReminderPopup, {\n                notification: showReminderPopup,\n                onConfirm: handleReminderConfirm,\n                onClose: ()=>setShowReminderPopup(null)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, this),\n            showConfirmationDialog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_13__.ConfirmationDialog, {\n                notification: showConfirmationDialog,\n                onConfirm: handleConfirmationSubmit,\n                onClose: ()=>setShowConfirmationDialog(null)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderManager__WEBPACK_IMPORTED_MODULE_9__.ReminderManager, {}, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicationPage, \"4HKtr2YmjS0dqLXuR7pouWYelUw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _store_medication__WEBPACK_IMPORTED_MODULE_4__.useMedication,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = MedicationPage;\nvar _c;\n$RefreshReg$(_c, \"MedicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/medication/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/medication/AudioSettings.tsx":
/*!*****************************************************!*\
  !*** ./src/components/medication/AudioSettings.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioSettings: () => (/* binding */ AudioSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Smartphone,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Smartphone,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.mjs\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Smartphone,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Smartphone,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.mjs\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Smartphone,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.mjs\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Smartphone,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.mjs\");\n/* harmony import */ var _lib_audio_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio-manager */ \"(app-pages-browser)/./src/lib/audio-manager.ts\");\n/* __next_internal_client_entry_do_not_use__ AudioSettings auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AudioSettings() {\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_audio_manager__WEBPACK_IMPORTED_MODULE_2__.audioManager.getSettings());\n    const [availableSounds, setAvailableSounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioSettings.useEffect\": ()=>{\n            setAvailableSounds(_lib_audio_manager__WEBPACK_IMPORTED_MODULE_2__.audioManager.getAvailableSounds());\n        }\n    }[\"AudioSettings.useEffect\"], []);\n    const handleSettingChange = (key, value)=>{\n        const newSettings = {\n            ...settings,\n            [key]: value\n        };\n        setSettings(newSettings);\n        _lib_audio_manager__WEBPACK_IMPORTED_MODULE_2__.audioManager.updateSettings(newSettings);\n    };\n    const testSound = async (soundId)=>{\n        if (isPlaying) return;\n        setIsPlaying(soundId);\n        try {\n            await _lib_audio_manager__WEBPACK_IMPORTED_MODULE_2__.audioManager.testSound(soundId);\n        } catch (error) {\n            console.error('测试音效失败:', error);\n        } finally{\n            setIsPlaying(null);\n        }\n    };\n    const testReminderLevel = async (level)=>{\n        if (isPlaying) return;\n        setIsPlaying(\"level-\".concat(level));\n        try {\n            await _lib_audio_manager__WEBPACK_IMPORTED_MODULE_2__.audioManager.playReminderSound(level);\n        } catch (error) {\n            console.error('测试提醒失败:', error);\n        } finally{\n            setIsPlaying(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            \"音量设置\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"提醒音量: \",\n                                        Math.round(settings.volume * 100),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"range\",\n                                            min: \"0\",\n                                            max: \"1\",\n                                            step: \"0.1\",\n                                            value: settings.volume,\n                                            onChange: (e)=>handleSettingChange('volume', parseFloat(e.target.value)),\n                                            className: \"flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            \"提醒音效\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: availableSounds.map((sound)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    id: sound.id,\n                                                    name: \"soundType\",\n                                                    value: sound.id,\n                                                    checked: settings.soundType === sound.id,\n                                                    onChange: (e)=>handleSettingChange('soundType', e.target.value),\n                                                    className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: sound.id,\n                                                    className: \"ml-3 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-800\",\n                                                            children: sound.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: sound.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>testSound(sound.id),\n                                        disabled: isPlaying === sound.id,\n                                        className: \"ml-3 p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50\",\n                                        title: \"试听音效\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, sound.id, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            \"视觉和触觉效果\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-500 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-800\",\n                                                        children: \"震动提醒\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"在支持的设备上启用震动\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.enableVibration,\n                                                onChange: (e)=>handleSettingChange('enableVibration', e.target.checked),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-500 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-800\",\n                                                        children: \"屏幕闪烁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"重要提醒时屏幕闪烁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.enableFlash,\n                                                onChange: (e)=>handleSettingChange('enableFlash', e.target.checked),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                        children: \"提醒级别测试\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>testReminderLevel(1),\n                                disabled: isPlaying === 'level-1',\n                                className: \"flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"第一级提醒\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>testReminderLevel(2),\n                                disabled: isPlaying === 'level-2',\n                                className: \"flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"第二级提醒\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>testReminderLevel(3),\n                                disabled: isPlaying === 'level-3',\n                                className: \"flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Smartphone_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"第三级提醒\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                \"\\uD83D\\uDCA1 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"说明：\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 16\n                                }, this),\n                                \"第一级为普通提醒，第二级增加震动和闪烁，第三级为最高级别的紧急提醒。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\AudioSettings.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioSettings, \"iIz2kIY3rtVFY53fihEDovnoztM=\");\n_c = AudioSettings;\nvar _c;\n$RefreshReg$(_c, \"AudioSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21lZGljYXRpb24vQXVkaW9TZXR0aW5ncy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNxQztBQUM5QjtBQUczQyxTQUFTUzs7SUFDZCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1gsK0NBQVFBLENBQWdCUSw0REFBWUEsQ0FBQ0ksV0FBVztJQUNoRixNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdkLCtDQUFRQSxDQUFnQixFQUFFO0lBQ3hFLE1BQU0sQ0FBQ2UsV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQWdCO0lBRTFEQyxnREFBU0E7bUNBQUM7WUFDUmEsbUJBQW1CTiw0REFBWUEsQ0FBQ1Msa0JBQWtCO1FBQ3BEO2tDQUFHLEVBQUU7SUFFTCxNQUFNQyxzQkFBc0IsQ0FBQ0MsS0FBMEJDO1FBQ3JELE1BQU1DLGNBQWM7WUFBRSxHQUFHWCxRQUFRO1lBQUUsQ0FBQ1MsSUFBSSxFQUFFQztRQUFNO1FBQ2hEVCxZQUFZVTtRQUNaYiw0REFBWUEsQ0FBQ2MsY0FBYyxDQUFDRDtJQUM5QjtJQUVBLE1BQU1FLFlBQVksT0FBT0M7UUFDdkIsSUFBSVQsV0FBVztRQUVmQyxhQUFhUTtRQUNiLElBQUk7WUFDRixNQUFNaEIsNERBQVlBLENBQUNlLFNBQVMsQ0FBQ0M7UUFDL0IsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxXQUFXQTtRQUMzQixTQUFVO1lBQ1JULGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTVcsb0JBQW9CLE9BQU9DO1FBQy9CLElBQUliLFdBQVc7UUFFZkMsYUFBYSxTQUFlLE9BQU5ZO1FBQ3RCLElBQUk7WUFDRixNQUFNcEIsNERBQVlBLENBQUNxQixpQkFBaUIsQ0FBQ0Q7UUFDdkMsRUFBRSxPQUFPSCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxXQUFXQTtRQUMzQixTQUFVO1lBQ1JULGFBQWE7UUFDZjtJQUNGO0lBRUEscUJBQ0UsOERBQUNjO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVOzswQ0FDWiw4REFBQzdCLHdIQUFPQTtnQ0FBQzZCLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBSXRDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7OzhDQUNDLDhEQUFDRztvQ0FBTUYsV0FBVTs7d0NBQStDO3dDQUN2REcsS0FBS0MsS0FBSyxDQUFDekIsU0FBUzBCLE1BQU0sR0FBRzt3Q0FBSzs7Ozs7Ozs4Q0FFM0MsOERBQUNOO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzVCLHdIQUFPQTs0Q0FBQzRCLFdBQVU7Ozs7OztzREFDbkIsOERBQUNNOzRDQUNDQyxNQUFLOzRDQUNMQyxLQUFJOzRDQUNKQyxLQUFJOzRDQUNKQyxNQUFLOzRDQUNMckIsT0FBT1YsU0FBUzBCLE1BQU07NENBQ3RCTSxVQUFVLENBQUNDLElBQU16QixvQkFBb0IsVUFBVTBCLFdBQVdELEVBQUVFLE1BQU0sQ0FBQ3pCLEtBQUs7NENBQ3hFVyxXQUFVOzs7Ozs7c0RBRVosOERBQUM3Qix3SEFBT0E7NENBQUM2QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPM0IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7OzBDQUNaLDhEQUFDeEIsd0hBQVFBO2dDQUFDd0IsV0FBVTs7Ozs7OzRCQUFpQjs7Ozs7OztrQ0FJdkMsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNabEIsZ0JBQWdCaUMsR0FBRyxDQUFDLENBQUNDLHNCQUNwQiw4REFBQ2pCO2dDQUFtQkMsV0FBVTs7a0RBQzVCLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTtvREFDQ0MsTUFBSztvREFDTFUsSUFBSUQsTUFBTUMsRUFBRTtvREFDWkMsTUFBSztvREFDTDdCLE9BQU8yQixNQUFNQyxFQUFFO29EQUNmRSxTQUFTeEMsU0FBU3lDLFNBQVMsS0FBS0osTUFBTUMsRUFBRTtvREFDeENOLFVBQVUsQ0FBQ0MsSUFBTXpCLG9CQUFvQixhQUFheUIsRUFBRUUsTUFBTSxDQUFDekIsS0FBSztvREFDaEVXLFdBQVU7Ozs7Ozs4REFFWiw4REFBQ0U7b0RBQU1tQixTQUFTTCxNQUFNQyxFQUFFO29EQUFFakIsV0FBVTs7c0VBQ2xDLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBNkJnQixNQUFNRSxJQUFJOzs7Ozs7c0VBQ3RELDhEQUFDbkI7NERBQUlDLFdBQVU7c0VBQXlCZ0IsTUFBTU0sV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSS9ELDhEQUFDQzt3Q0FDQ2hCLE1BQUs7d0NBQ0xpQixTQUFTLElBQU1oQyxVQUFVd0IsTUFBTUMsRUFBRTt3Q0FDakNRLFVBQVV6QyxjQUFjZ0MsTUFBTUMsRUFBRTt3Q0FDaENqQixXQUFVO3dDQUNWMEIsT0FBTTtrREFFTiw0RUFBQ25ELHdIQUFJQTs0Q0FBQ3lCLFdBQVU7Ozs7Ozs7Ozs7OzsrQkF6QlZnQixNQUFNQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWlDeEIsOERBQUNsQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVOzswQ0FDWiw4REFBQzFCLHdIQUFHQTtnQ0FBQzBCLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBSWxDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDM0Isd0hBQVVBO2dEQUFDMkIsV0FBVTs7Ozs7OzBEQUN0Qiw4REFBQ0Q7O2tFQUNDLDhEQUFDQTt3REFBSUMsV0FBVTtrRUFBNEI7Ozs7OztrRUFDM0MsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczQyw4REFBQ0U7d0NBQU1GLFdBQVU7OzBEQUNmLDhEQUFDTTtnREFDQ0MsTUFBSztnREFDTFksU0FBU3hDLFNBQVNnRCxlQUFlO2dEQUNqQ2hCLFVBQVUsQ0FBQ0MsSUFBTXpCLG9CQUFvQixtQkFBbUJ5QixFQUFFRSxNQUFNLENBQUNLLE9BQU87Z0RBQ3hFbkIsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUluQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMxQix3SEFBR0E7Z0RBQUMwQixXQUFVOzs7Ozs7MERBQ2YsOERBQUNEOztrRUFDQyw4REFBQ0E7d0RBQUlDLFdBQVU7a0VBQTRCOzs7Ozs7a0VBQzNDLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFHM0MsOERBQUNFO3dDQUFNRixXQUFVOzswREFDZiw4REFBQ007Z0RBQ0NDLE1BQUs7Z0RBQ0xZLFNBQVN4QyxTQUFTaUQsV0FBVztnREFDN0JqQixVQUFVLENBQUNDLElBQU16QixvQkFBb0IsZUFBZXlCLEVBQUVFLE1BQU0sQ0FBQ0ssT0FBTztnREFDcEVuQixXQUFVOzs7Ozs7MERBRVosOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3ZCLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUEyQzs7Ozs7O2tDQUV6RCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDdUI7Z0NBQ0NoQixNQUFLO2dDQUNMaUIsU0FBUyxJQUFNNUIsa0JBQWtCO2dDQUNqQzZCLFVBQVV6QyxjQUFjO2dDQUN4QmdCLFdBQVU7O2tEQUVWLDhEQUFDekIsd0hBQUlBO3dDQUFDeUIsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FJbkMsOERBQUN1QjtnQ0FDQ2hCLE1BQUs7Z0NBQ0xpQixTQUFTLElBQU01QixrQkFBa0I7Z0NBQ2pDNkIsVUFBVXpDLGNBQWM7Z0NBQ3hCZ0IsV0FBVTs7a0RBRVYsOERBQUN6Qix3SEFBSUE7d0NBQUN5QixXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUluQyw4REFBQ3VCO2dDQUNDaEIsTUFBSztnQ0FDTGlCLFNBQVMsSUFBTTVCLGtCQUFrQjtnQ0FDakM2QixVQUFVekMsY0FBYztnQ0FDeEJnQixXQUFVOztrREFFViw4REFBQ3pCLHdIQUFJQTt3Q0FBQ3lCLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7a0NBS3JDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQzZCOzRCQUFFN0IsV0FBVTs7Z0NBQXdCOzhDQUNoQyw4REFBQzhCOzhDQUFPOzs7Ozs7Z0NBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9uQztHQTlNZ0JwRDtLQUFBQSIsInNvdXJjZXMiOlsiRTpcXGNhcmViYW9cXGNhcmVhcHAtd2ViXFxzcmNcXGNvbXBvbmVudHNcXG1lZGljYXRpb25cXEF1ZGlvU2V0dGluZ3MudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBWb2x1bWUyLCBWb2x1bWVYLCBTbWFydHBob25lLCBaYXAsIFBsYXksIFNldHRpbmdzIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgYXVkaW9NYW5hZ2VyIH0gZnJvbSAnQC9saWIvYXVkaW8tbWFuYWdlcidcbmltcG9ydCB0eXBlIHsgQXVkaW9TZXR0aW5ncywgU291bmRFZmZlY3QgfSBmcm9tICdAL2xpYi9hdWRpby1tYW5hZ2VyJ1xuXG5leHBvcnQgZnVuY3Rpb24gQXVkaW9TZXR0aW5ncygpIHtcbiAgY29uc3QgW3NldHRpbmdzLCBzZXRTZXR0aW5nc10gPSB1c2VTdGF0ZTxBdWRpb1NldHRpbmdzPihhdWRpb01hbmFnZXIuZ2V0U2V0dGluZ3MoKSlcbiAgY29uc3QgW2F2YWlsYWJsZVNvdW5kcywgc2V0QXZhaWxhYmxlU291bmRzXSA9IHVzZVN0YXRlPFNvdW5kRWZmZWN0W10+KFtdKVxuICBjb25zdCBbaXNQbGF5aW5nLCBzZXRJc1BsYXlpbmddID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldEF2YWlsYWJsZVNvdW5kcyhhdWRpb01hbmFnZXIuZ2V0QXZhaWxhYmxlU291bmRzKCkpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGhhbmRsZVNldHRpbmdDaGFuZ2UgPSAoa2V5OiBrZXlvZiBBdWRpb1NldHRpbmdzLCB2YWx1ZTogYW55KSA9PiB7XG4gICAgY29uc3QgbmV3U2V0dGluZ3MgPSB7IC4uLnNldHRpbmdzLCBba2V5XTogdmFsdWUgfVxuICAgIHNldFNldHRpbmdzKG5ld1NldHRpbmdzKVxuICAgIGF1ZGlvTWFuYWdlci51cGRhdGVTZXR0aW5ncyhuZXdTZXR0aW5ncylcbiAgfVxuXG4gIGNvbnN0IHRlc3RTb3VuZCA9IGFzeW5jIChzb3VuZElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoaXNQbGF5aW5nKSByZXR1cm5cblxuICAgIHNldElzUGxheWluZyhzb3VuZElkKVxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBhdWRpb01hbmFnZXIudGVzdFNvdW5kKHNvdW5kSWQpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+a1i+ivlemfs+aViOWksei0pTonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNQbGF5aW5nKG51bGwpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdGVzdFJlbWluZGVyTGV2ZWwgPSBhc3luYyAobGV2ZWw6IDEgfCAyIHwgMykgPT4ge1xuICAgIGlmIChpc1BsYXlpbmcpIHJldHVyblxuXG4gICAgc2V0SXNQbGF5aW5nKGBsZXZlbC0ke2xldmVsfWApXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGF1ZGlvTWFuYWdlci5wbGF5UmVtaW5kZXJTb3VuZChsZXZlbClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5rWL6K+V5o+Q6YaS5aSx6LSlOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1BsYXlpbmcobnVsbClcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7Lyog6Z+z6YeP6K6+572uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8Vm9sdW1lMiBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgIOmfs+mHj+iuvue9rlxuICAgICAgICA8L2gzPlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIOaPkOmGkumfs+mHjzoge01hdGgucm91bmQoc2V0dGluZ3Mudm9sdW1lICogMTAwKX0lXG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPFZvbHVtZVggY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICBtYXg9XCIxXCJcbiAgICAgICAgICAgICAgICBzdGVwPVwiMC4xXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3Mudm9sdW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgndm9sdW1lJywgcGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBoLTIgYmctZ3JheS0yMDAgcm91bmRlZC1sZyBhcHBlYXJhbmNlLW5vbmUgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8Vm9sdW1lMiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDpn7PmlYjpgInmi6kgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgIOaPkOmGkumfs+aViFxuICAgICAgICA8L2gzPlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICB7YXZhaWxhYmxlU291bmRzLm1hcCgoc291bmQpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtzb3VuZC5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcbiAgICAgICAgICAgICAgICAgICAgaWQ9e3NvdW5kLmlkfVxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwic291bmRUeXBlXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NvdW5kLmlkfVxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZXR0aW5ncy5zb3VuZFR5cGUgPT09IHNvdW5kLmlkfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ3NvdW5kVHlwZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwIGJnLWdyYXktMTAwIGJvcmRlci1ncmF5LTMwMCBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj17c291bmQuaWR9IGNsYXNzTmFtZT1cIm1sLTMgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+e3NvdW5kLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3NvdW5kLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0ZXN0U291bmQoc291bmQuaWQpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1BsYXlpbmcgPT09IHNvdW5kLmlkfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTMgcC0yIHRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS01MCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgIHRpdGxlPVwi6K+V5ZCs6Z+z5pWIXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6KeG6KeJ5ZKM6Kem6KeJ5pWI5p6cICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAg6KeG6KeJ5ZKM6Kem6KeJ5pWI5p6cXG4gICAgICAgIDwvaDM+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxTbWFydHBob25lIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTUwMCBtci0zXCIgLz5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDBcIj7pnIfliqjmj5DphpI8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPuWcqOaUr+aMgeeahOiuvuWkh+S4iuWQr+eUqOmch+WKqDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NldHRpbmdzLmVuYWJsZVZpYnJhdGlvbn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ2VuYWJsZVZpYnJhdGlvbicsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHkgcGVlclwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMSBoLTYgYmctZ3JheS0yMDAgcGVlci1mb2N1czpvdXRsaW5lLW5vbmUgcGVlci1mb2N1czpyaW5nLTQgcGVlci1mb2N1czpyaW5nLWJsdWUtMzAwIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOmJvcmRlci1ncmF5LTMwMCBhZnRlcjpib3JkZXIgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JheS01MDAgbXItM1wiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+5bGP5bmV6Zeq54OBPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7ph43opoHmj5DphpLml7blsY/luZXpl6rng4E8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJyZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtzZXR0aW5ncy5lbmFibGVGbGFzaH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ2VuYWJsZUZsYXNoJywgZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTExIGgtNiBiZy1ncmF5LTIwMCBwZWVyLWZvY3VzOm91dGxpbmUtbm9uZSBwZWVyLWZvY3VzOnJpbmctNCBwZWVyLWZvY3VzOnJpbmctYmx1ZS0zMDAgcm91bmRlZC1mdWxsIHBlZXIgcGVlci1jaGVja2VkOmFmdGVyOnRyYW5zbGF0ZS14LWZ1bGwgcGVlci1jaGVja2VkOmFmdGVyOmJvcmRlci13aGl0ZSBhZnRlcjpjb250ZW50LVsnJ10gYWZ0ZXI6YWJzb2x1dGUgYWZ0ZXI6dG9wLVsycHhdIGFmdGVyOmxlZnQtWzJweF0gYWZ0ZXI6Ymctd2hpdGUgYWZ0ZXI6Ym9yZGVyLWdyYXktMzAwIGFmdGVyOmJvcmRlciBhZnRlcjpyb3VuZGVkLWZ1bGwgYWZ0ZXI6aC01IGFmdGVyOnctNSBhZnRlcjp0cmFuc2l0aW9uLWFsbCBwZWVyLWNoZWNrZWQ6YmctYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDmj5DphpLnuqfliKvmtYvor5UgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItNFwiPuaPkOmGkue6p+WIq+a1i+ivlTwvaDM+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTNcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRlc3RSZW1pbmRlckxldmVsKDEpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzUGxheWluZyA9PT0gJ2xldmVsLTEnfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHgtNCBweS0zIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAg56ys5LiA57qn5o+Q6YaSXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0ZXN0UmVtaW5kZXJMZXZlbCgyKX1cbiAgICAgICAgICAgIGRpc2FibGVkPXtpc1BsYXlpbmcgPT09ICdsZXZlbC0yJ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTQgcHktMyBiZy1vcmFuZ2UtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1vcmFuZ2UtNzAwIGRpc2FibGVkOm9wYWNpdHktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICDnrKzkuoznuqfmj5DphpJcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICBcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRlc3RSZW1pbmRlckxldmVsKDMpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzUGxheWluZyA9PT0gJ2xldmVsLTMnfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHgtNCBweS0zIGJnLXJlZC02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLXJlZC03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFBsYXkgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgIOesrOS4iee6p+aPkOmGklxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICDwn5KhIDxzdHJvbmc+6K+05piO77yaPC9zdHJvbmc+XG4gICAgICAgICAgICDnrKzkuIDnuqfkuLrmma7pgJrmj5DphpLvvIznrKzkuoznuqflop7liqDpnIfliqjlkozpl6rng4HvvIznrKzkuInnuqfkuLrmnIDpq5jnuqfliKvnmoTntKfmgKXmj5DphpLjgIJcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlZvbHVtZTIiLCJWb2x1bWVYIiwiU21hcnRwaG9uZSIsIlphcCIsIlBsYXkiLCJTZXR0aW5ncyIsImF1ZGlvTWFuYWdlciIsIkF1ZGlvU2V0dGluZ3MiLCJzZXR0aW5ncyIsInNldFNldHRpbmdzIiwiZ2V0U2V0dGluZ3MiLCJhdmFpbGFibGVTb3VuZHMiLCJzZXRBdmFpbGFibGVTb3VuZHMiLCJpc1BsYXlpbmciLCJzZXRJc1BsYXlpbmciLCJnZXRBdmFpbGFibGVTb3VuZHMiLCJoYW5kbGVTZXR0aW5nQ2hhbmdlIiwia2V5IiwidmFsdWUiLCJuZXdTZXR0aW5ncyIsInVwZGF0ZVNldHRpbmdzIiwidGVzdFNvdW5kIiwic291bmRJZCIsImVycm9yIiwiY29uc29sZSIsInRlc3RSZW1pbmRlckxldmVsIiwibGV2ZWwiLCJwbGF5UmVtaW5kZXJTb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwibGFiZWwiLCJNYXRoIiwicm91bmQiLCJ2b2x1bWUiLCJpbnB1dCIsInR5cGUiLCJtaW4iLCJtYXgiLCJzdGVwIiwib25DaGFuZ2UiLCJlIiwicGFyc2VGbG9hdCIsInRhcmdldCIsIm1hcCIsInNvdW5kIiwiaWQiLCJuYW1lIiwiY2hlY2tlZCIsInNvdW5kVHlwZSIsImh0bWxGb3IiLCJkZXNjcmlwdGlvbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInRpdGxlIiwiZW5hYmxlVmlicmF0aW9uIiwiZW5hYmxlRmxhc2giLCJwIiwic3Ryb25nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/medication/AudioSettings.tsx\n"));

/***/ })

});