'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Pill, Clock, Check, Timer, Volume2 } from 'lucide-react'
import { speechService } from '@/lib/speech-service'
import { VisualEffects } from './VisualEffects'
import type { ReminderNotification } from '@/lib/notification-service'

interface ReminderPopupProps {
  notification: ReminderNotification
  onConfirm: (confirmed: boolean) => void
  onClose: () => void
}

export function ReminderPopup({ notification, onConfirm, onClose }: ReminderPopupProps) {
  const [timeLeft, setTimeLeft] = useState(20) // 20秒自动关闭
  const [isPlaying, setIsPlaying] = useState(false)
  const [showVisualEffects, setShowVisualEffects] = useState(true)

  useEffect(() => {
    // 倒计时
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          onClose() // 自动关闭
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [onClose])

  useEffect(() => {
    // 播放语音提醒
    if (notification.level >= 2) {
      playVoiceReminder()
    }
  }, [notification])

  const playVoiceReminder = async () => {
    if (!speechService.isSpeechSynthesisSupported()) return

    setIsPlaying(true)
    try {
      const speechText = speechService.generateReminderSpeech(
        notification.medicineName,
        notification.dosage,
        notification.usage
      )
      await speechService.speak({ text: speechText })
    } catch (error) {
      console.error('语音播报失败:', error)
    } finally {
      setIsPlaying(false)
    }
  }

  const handleConfirm = (confirmed: boolean) => {
    onConfirm(confirmed)
    onClose()
  }

  const getLevelColor = () => {
    switch (notification.level) {
      case 1: return 'bg-blue-500'
      case 2: return 'bg-orange-500'
      case 3: return 'bg-red-500'
      default: return 'bg-blue-500'
    }
  }

  const getLevelText = () => {
    switch (notification.level) {
      case 1: return '第一次提醒'
      case 2: return '重要提醒'
      case 3: return '紧急提醒'
      default: return '用药提醒'
    }
  }

  const getAnimationClass = () => {
    switch (notification.level) {
      case 1: return 'animate-bounce'
      case 2: return 'animate-pulse'
      case 3: return 'animate-ping'
      default: return 'animate-bounce'
    }
  }

  const getBackgroundClass = () => {
    switch (notification.level) {
      case 1: return 'bg-black bg-opacity-50'
      case 2: return 'bg-orange-900 bg-opacity-60'
      case 3: return 'bg-red-900 bg-opacity-70'
      default: return 'bg-black bg-opacity-50'
    }
  }

  return (
    <>
      {/* 视觉效果 */}
      <VisualEffects
        isActive={showVisualEffects && notification.level >= 2}
        level={notification.level}
        onComplete={() => setShowVisualEffects(false)}
      />

      <div className={`fixed inset-0 ${getBackgroundClass()} flex items-center justify-center z-50 p-4`}>
        <div className={`bg-white rounded-lg shadow-2xl max-w-md w-full ${getAnimationClass()}`}>
        {/* 头部 */}
        <div className={`${getLevelColor()} text-white p-4 rounded-t-lg`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Pill className="w-6 h-6 mr-2" />
              <h2 className="text-lg font-semibold">{getLevelText()}</h2>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-sm bg-white bg-opacity-20 px-2 py-1 rounded">
                {timeLeft}s
              </div>
              <button
                type="button"
                onClick={onClose}
                className="text-white hover:bg-white hover:bg-opacity-20 p-1 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Pill className="w-8 h-8 text-blue-600" />
            </div>
            
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              {notification.medicineName}
            </h3>
            
            <div className="space-y-1 text-gray-600">
              <p><strong>剂量：</strong>{notification.dosage}</p>
              {notification.usage && (
                <p><strong>用法：</strong>{notification.usage}</p>
              )}
              <div className="flex items-center justify-center mt-2">
                <Clock className="w-4 h-4 mr-1" />
                <span className="text-sm">计划时间：{notification.scheduledTime}</span>
              </div>
            </div>
          </div>

          {/* 语音播报按钮 */}
          {speechService.isSpeechSynthesisSupported() && (
            <div className="text-center mb-6">
              <button
                type="button"
                onClick={playVoiceReminder}
                disabled={isPlaying}
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50"
              >
                <Volume2 className="w-4 h-4 mr-2" />
                {isPlaying ? '播放中...' : '重新播放'}
              </button>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="space-y-3">
            <button
              type="button"
              onClick={() => handleConfirm(true)}
              className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 flex items-center justify-center font-medium"
            >
              <Check className="w-5 h-5 mr-2" />
              已服药
            </button>

            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => handleConfirm(false)}
                className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 flex items-center justify-center"
              >
                <X className="w-4 h-4 mr-1" />
                暂不服药
              </button>

              <button
                type="button"
                onClick={onClose}
                className="bg-blue-100 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-200 flex items-center justify-center"
              >
                <Timer className="w-4 h-4 mr-1" />
                稍后提醒
              </button>
            </div>
          </div>

          {/* 提醒级别说明 */}
          {notification.level >= 2 && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm text-center">
                {notification.level === 2 
                  ? '⚠️ 这是第二次提醒，请及时服药'
                  : '🚨 多次提醒未响应，请确认是否已服药'
                }
              </p>
            </div>
          )}
        </div>

        {/* 进度条 */}
        <div className="h-1 bg-gray-200 rounded-b-lg overflow-hidden">
          <div 
            className={`h-full ${getLevelColor()} transition-all duration-1000 ease-linear`}
            style={{ width: `${(timeLeft / 20) * 100}%` }}
          />
        </div>
      </div>
    </>
  )
}

// 确认对话框组件
interface ConfirmationDialogProps {
  notification: ReminderNotification
  onConfirm: (confirmed: boolean) => void
  onClose: () => void
}

export function ConfirmationDialog({ notification, onConfirm, onClose }: ConfirmationDialogProps) {
  const [selectedOption, setSelectedOption] = useState<'taken' | 'missed' | null>(null)

  const handleSubmit = () => {
    if (selectedOption) {
      onConfirm(selectedOption === 'taken')
      onClose()
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-md w-full">
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Pill className="w-8 h-8 text-orange-600" />
            </div>
            
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              用药确认
            </h3>
            
            <p className="text-gray-600">
              您是否已经服用了 <strong>{notification.medicineName}</strong>？
            </p>
          </div>

          <div className="space-y-3 mb-6">
            <button
              type="button"
              onClick={() => setSelectedOption('taken')}
              className={`w-full p-3 border-2 rounded-lg text-left transition-colors ${
                selectedOption === 'taken'
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <Check className="w-5 h-5 mr-3" />
                <div>
                  <div className="font-medium">是的，已经服药</div>
                  <div className="text-sm text-gray-500">我已按时服用了药物</div>
                </div>
              </div>
            </button>

            <button
              type="button"
              onClick={() => setSelectedOption('missed')}
              className={`w-full p-3 border-2 rounded-lg text-left transition-colors ${
                selectedOption === 'missed'
                  ? 'border-red-500 bg-red-50 text-red-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <X className="w-5 h-5 mr-3" />
                <div>
                  <div className="font-medium">没有，忘记服药</div>
                  <div className="text-sm text-gray-500">我错过了这次用药时间</div>
                </div>
              </div>
            </button>
          </div>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={!selectedOption}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              确认
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
