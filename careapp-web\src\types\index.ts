// 基础类型定义，对应Android版本的数据模型

// 用药提醒相关类型
export interface MedicineReminder {
  id: string
  medicineName: string
  dosage: string
  usage: string // 用法说明（餐前30分钟、餐后1小时等）
  frequency: string // 用药频次（每日1次、每日3次等）
  duration?: number // 疗程时长（天数）
  scheduledTimes: string[] // 计算出的用药时间列表
  isEnabled: boolean
  createdAt: string
  updatedAt: string
  userId: string
}

// 个人作息时间配置
export interface DailySchedule {
  id: string
  userId: string
  wakeUpTime: string // 起床时间
  breakfastTime: string // 早餐时间
  lunchTime: string // 午餐时间
  dinnerTime: string // 晚餐时间
  bedTime: string // 就寝时间
  createdAt: string
  updatedAt: string
}

// 用药记录
export interface MedicationRecord {
  id: string
  reminderId: string
  scheduledTime: string // 计划用药时间
  actualTime?: string // 实际用药时间
  status: 'pending' | 'taken' | 'missed' | 'skipped'
  confirmationMethod: 'manual' | 'auto' | 'guardian'
  notes?: string
  createdAt: string
  userId: string
}

// 提醒设置
export interface ReminderSettings {
  id: string
  userId: string
  firstReminderMinutes: number // 第一级提醒提前时间（分钟）
  reminderInterval: number // 重复提醒间隔（分钟）
  maxReminders: number // 最大提醒次数
  soundEnabled: boolean // 声音提醒
  voiceEnabled: boolean // 语音播报
  notificationEnabled: boolean // 浏览器通知
  guardianNotificationDelay: number // 监护人通知延迟（分钟）
  createdAt: string
  updatedAt: string
}

// 监护人联系人
export interface GuardianContact {
  id: string
  userId: string // 患者ID
  guardianName: string
  guardianEmail?: string
  guardianPhone?: string
  relationship: string // 关系（子女、配偶等）
  priority: number // 优先级
  notificationMethods: ('email' | 'sms' | 'app')[]
  isEnabled: boolean
  createdAt: string
  updatedAt: string
}

export interface HealthData {
  id: string
  type: 'blood_pressure' | 'blood_sugar' | 'heart_rate' | 'temperature'
  value: number
  unit: string
  measuredAt: string
  notes?: string
  userId: string
  deviceId?: string
}

export interface BloodPressureData extends HealthData {
  type: 'blood_pressure'
  systolic: number
  diastolic: number
  pulse: number
}

export interface BloodSugarData extends HealthData {
  type: 'blood_sugar'
  value: number
  measurementType: 'fasting' | 'postprandial' | 'random'
}

export interface EmergencyContact {
  id: string
  name: string
  phone: string
  relationship: string
  priority: number
  userId: string
}

export interface EmergencyCall {
  id: string
  type: 'manual' | 'automatic'
  location?: {
    latitude: number
    longitude: number
    address: string
  }
  status: 'pending' | 'responded' | 'resolved'
  createdAt: string
  userId: string
  contactedPersons: string[]
}

export interface FamilyMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  type: 'text' | 'voice' | 'image'
  priority: 'normal' | 'important' | 'urgent'
  createdAt: string
  readAt?: string
}

export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar?: string
  role: 'patient' | 'family' | 'caregiver'
  createdAt: string
  updatedAt: string
}

export interface Device {
  id: string
  name: string
  type: 'blood_pressure_monitor' | 'glucose_meter' | 'smart_watch' | 'other'
  brand: string
  model: string
  connectionType: 'bluetooth' | 'wifi' | 'usb'
  isConnected: boolean
  lastSyncAt?: string
  userId: string
}
