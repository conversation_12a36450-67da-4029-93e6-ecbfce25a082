export interface SpeechRecognitionResult {
  transcript: string
  confidence: number
  isFinal: boolean
}

export interface SpeechSynthesisOptions {
  text: string
  lang?: string
  rate?: number
  pitch?: number
  volume?: number
}

export class SpeechService {
  private recognition: any = null
  private synthesis: SpeechSynthesis | null = null
  private isListening = false

  constructor() {
    this.initializeSpeechRecognition()
    this.initializeSpeechSynthesis()
  }

  /**
   * 初始化语音识别
   */
  private initializeSpeechRecognition() {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
      
      if (SpeechRecognition) {
        this.recognition = new SpeechRecognition()
        this.recognition.continuous = false
        this.recognition.interimResults = true
        this.recognition.lang = 'zh-CN'
        this.recognition.maxAlternatives = 1
      }
    }
  }

  /**
   * 初始化语音合成
   */
  private initializeSpeechSynthesis() {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      this.synthesis = window.speechSynthesis
    }
  }

  /**
   * 检查浏览器是否支持语音识别
   */
  isSpeechRecognitionSupported(): boolean {
    return this.recognition !== null
  }

  /**
   * 检查浏览器是否支持语音合成
   */
  isSpeechSynthesisSupported(): boolean {
    return this.synthesis !== null
  }

  /**
   * 开始语音识别
   */
  async startListening(
    onResult: (result: SpeechRecognitionResult) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    if (!this.recognition) {
      throw new Error('语音识别不支持')
    }

    if (this.isListening) {
      this.stopListening()
    }

    return new Promise((resolve, reject) => {
      this.recognition.onstart = () => {
        this.isListening = true
        resolve()
      }

      this.recognition.onresult = (event: any) => {
        const result = event.results[event.results.length - 1]
        const transcript = result[0].transcript
        const confidence = result[0].confidence
        const isFinal = result.isFinal

        onResult({
          transcript: transcript.trim(),
          confidence,
          isFinal
        })
      }

      this.recognition.onerror = (event: any) => {
        this.isListening = false
        const errorMessage = this.getErrorMessage(event.error)
        onError?.(errorMessage)
        reject(new Error(errorMessage))
      }

      this.recognition.onend = () => {
        this.isListening = false
      }

      try {
        this.recognition.start()
      } catch (error) {
        this.isListening = false
        reject(error)
      }
    })
  }

  /**
   * 停止语音识别
   */
  stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop()
      this.isListening = false
    }
  }

  /**
   * 语音播报
   */
  async speak(options: SpeechSynthesisOptions): Promise<void> {
    if (!this.synthesis) {
      throw new Error('语音合成不支持')
    }

    // 停止当前播报
    this.synthesis.cancel()

    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(options.text)
      
      utterance.lang = options.lang || 'zh-CN'
      utterance.rate = options.rate || 1
      utterance.pitch = options.pitch || 1
      utterance.volume = options.volume || 1

      utterance.onend = () => resolve()
      utterance.onerror = (event) => reject(new Error(`语音播报失败: ${event.error}`))

      // 获取中文语音
      const voices = this.synthesis.getVoices()
      const chineseVoice = voices.find(voice => 
        voice.lang.includes('zh') || voice.lang.includes('CN')
      )
      
      if (chineseVoice) {
        utterance.voice = chineseVoice
      }

      this.synthesis.speak(utterance)
    })
  }

  /**
   * 停止语音播报
   */
  stopSpeaking(): void {
    if (this.synthesis) {
      this.synthesis.cancel()
    }
  }

  /**
   * 解析药品信息的语音输入
   */
  parseMedicineInput(transcript: string): {
    medicineName?: string
    dosage?: string
    usage?: string
    frequency?: string
  } {
    const result: any = {}

    // 提取药品名称（通常在开头）
    const medicineNameMatch = transcript.match(/^(.+?)(?:\s|，|,)/)
    if (medicineNameMatch) {
      result.medicineName = medicineNameMatch[1].trim()
    }

    // 提取剂量信息
    const dosagePatterns = [
      /(\d+)\s*片/,
      /(\d+)\s*粒/,
      /(\d+)\s*毫升/,
      /(\d+)\s*ml/,
      /(\d+)\s*毫克/,
      /(\d+)\s*mg/,
      /(\d+)\s*滴/
    ]

    for (const pattern of dosagePatterns) {
      const match = transcript.match(pattern)
      if (match) {
        const unit = match[0].replace(match[1], '').trim()
        result.dosage = `${match[1]}${unit}`
        break
      }
    }

    // 提取用法信息
    const usagePatterns = [
      /餐前\s*(\d+)?\s*分钟?/,
      /饭前\s*(\d+)?\s*分钟?/,
      /餐后\s*(\d+)?\s*分钟?/,
      /饭后\s*(\d+)?\s*分钟?/,
      /睡前\s*(\d+)?\s*分钟?/,
      /晨起/,
      /起床后/
    ]

    for (const pattern of usagePatterns) {
      const match = transcript.match(pattern)
      if (match) {
        result.usage = match[0]
        break
      }
    }

    // 提取频次信息
    const frequencyPatterns = [
      /每日\s*(\d+)\s*次/,
      /一日\s*(\d+)\s*次/,
      /每天\s*(\d+)\s*次/,
      /(\d+)\s*次\s*每日/,
      /(\d+)\s*次\s*一日/
    ]

    for (const pattern of frequencyPatterns) {
      const match = transcript.match(pattern)
      if (match) {
        result.frequency = match[0]
        break
      }
    }

    return result
  }

  /**
   * 生成用药提醒的语音内容
   */
  generateReminderSpeech(medicineName: string, dosage: string, usage?: string): string {
    let speech = `请注意，现在是服用${medicineName}的时间。`
    
    if (dosage) {
      speech += `请服用${dosage}。`
    }
    
    if (usage) {
      speech += `用法：${usage}。`
    }
    
    speech += '请确认是否已经服药。'
    
    return speech
  }

  /**
   * 获取错误信息
   */
  private getErrorMessage(error: string): string {
    switch (error) {
      case 'no-speech':
        return '没有检测到语音输入'
      case 'audio-capture':
        return '无法访问麦克风'
      case 'not-allowed':
        return '麦克风权限被拒绝'
      case 'network':
        return '网络错误'
      case 'service-not-allowed':
        return '语音识别服务不可用'
      default:
        return `语音识别错误: ${error}`
    }
  }

  /**
   * 请求麦克风权限
   */
  async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error) {
      console.error('麦克风权限请求失败:', error)
      return false
    }
  }

  /**
   * 获取可用的语音列表
   */
  getAvailableVoices(): SpeechSynthesisVoice[] {
    if (!this.synthesis) return []
    return this.synthesis.getVoices().filter(voice => 
      voice.lang.includes('zh') || voice.lang.includes('CN')
    )
  }

  /**
   * 测试语音功能
   */
  async testSpeech(): Promise<{ recognition: boolean, synthesis: boolean }> {
    const result = {
      recognition: this.isSpeechRecognitionSupported(),
      synthesis: this.isSpeechSynthesisSupported()
    }

    if (result.synthesis) {
      try {
        await this.speak({ text: '语音功能测试正常' })
      } catch (error) {
        result.synthesis = false
      }
    }

    return result
  }
}

export const speechService = new SpeechService()
