export interface SpeechRecognitionResult {
  transcript: string
  confidence: number
  isFinal: boolean
}

export interface SpeechSynthesisOptions {
  text: string
  lang?: string
  rate?: number
  pitch?: number
  volume?: number
}

export class SpeechService {
  private recognition: any = null
  private synthesis: SpeechSynthesis | null = null
  private isListening = false
  private medicineDatabase: Map<string, string[]> = new Map()
  private commonMedicines: string[] = []

  constructor() {
    this.initializeSpeechRecognition()
    this.initializeSpeechSynthesis()
    this.initializeMedicineDatabase()
  }

  /**
   * 初始化语音识别
   */
  private initializeSpeechRecognition() {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
      
      if (SpeechRecognition) {
        this.recognition = new SpeechRecognition()
        this.recognition.continuous = false
        this.recognition.interimResults = true
        this.recognition.lang = 'zh-CN'
        this.recognition.maxAlternatives = 1
      }
    }
  }

  /**
   * 初始化语音合成
   */
  private initializeSpeechSynthesis() {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      this.synthesis = window.speechSynthesis
    }
  }

  /**
   * 初始化药品数据库
   */
  private initializeMedicineDatabase() {
    // 常用药品及其别名
    const medicines = [
      // 心血管药物
      { name: '阿司匹林', aliases: ['阿斯匹林', '阿司匹林肠溶片', '拜阿司匹灵'] },
      { name: '硝苯地平', aliases: ['硝苯地平片', '心痛定', '拜新同'] },
      { name: '卡托普利', aliases: ['卡托普利片', '开博通'] },
      { name: '美托洛尔', aliases: ['美托洛尔片', '倍他乐克', '酒石酸美托洛尔'] },

      // 降糖药物
      { name: '二甲双胍', aliases: ['二甲双胍片', '格华止', '美迪康'] },
      { name: '格列齐特', aliases: ['格列齐特片', '达美康', '迪沙片'] },
      { name: '胰岛素', aliases: ['胰岛素注射液', '诺和灵', '优泌林'] },

      // 抗生素
      { name: '阿莫西林', aliases: ['阿莫西林胶囊', '阿莫西林片', '再林'] },
      { name: '头孢拉定', aliases: ['头孢拉定胶囊', '头孢拉定片'] },
      { name: '左氧氟沙星', aliases: ['左氧氟沙星片', '可乐必妥', '来立信'] },

      // 消化系统药物
      { name: '奥美拉唑', aliases: ['奥美拉唑肠溶胶囊', '洛赛克', '奥克'] },
      { name: '多潘立酮', aliases: ['多潘立酮片', '吗丁啉', '胃复安'] },
      { name: '蒙脱石散', aliases: ['思密达', '必奇'] },

      // 感冒药物
      { name: '对乙酰氨基酚', aliases: ['对乙酰氨基酚片', '泰诺林', '百服宁', '扑热息痛'] },
      { name: '布洛芬', aliases: ['布洛芬片', '芬必得', '美林'] },
      { name: '复方氨酚烷胺', aliases: ['快克', '感康'] },

      // 维生素类
      { name: '维生素C', aliases: ['维生素C片', 'VC片', '维C'] },
      { name: '维生素D', aliases: ['维生素D3', 'VD3', '钙尔奇D'] },
      { name: '复合维生素B', aliases: ['维生素B族', 'VB片', '复合VB'] }
    ]

    // 构建药品数据库
    medicines.forEach(medicine => {
      const allNames = [medicine.name, ...medicine.aliases]
      this.medicineDatabase.set(medicine.name, allNames)
      this.commonMedicines.push(medicine.name)

      // 为每个别名也建立映射
      medicine.aliases.forEach(alias => {
        this.medicineDatabase.set(alias, allNames)
      })
    })
  }

  /**
   * 检查浏览器是否支持语音识别
   */
  isSpeechRecognitionSupported(): boolean {
    return this.recognition !== null
  }

  /**
   * 检查浏览器是否支持语音合成
   */
  isSpeechSynthesisSupported(): boolean {
    return this.synthesis !== null
  }

  /**
   * 开始语音识别
   */
  async startListening(
    onResult: (result: SpeechRecognitionResult) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    if (!this.recognition) {
      throw new Error('语音识别不支持')
    }

    if (this.isListening) {
      this.stopListening()
    }

    return new Promise((resolve, reject) => {
      this.recognition.onstart = () => {
        this.isListening = true
        resolve()
      }

      this.recognition.onresult = (event: any) => {
        const result = event.results[event.results.length - 1]
        const transcript = result[0].transcript
        const confidence = result[0].confidence
        const isFinal = result.isFinal

        onResult({
          transcript: transcript.trim(),
          confidence,
          isFinal
        })
      }

      this.recognition.onerror = (event: any) => {
        this.isListening = false
        const errorMessage = this.getErrorMessage(event.error)
        onError?.(errorMessage)
        reject(new Error(errorMessage))
      }

      this.recognition.onend = () => {
        this.isListening = false
      }

      try {
        this.recognition.start()
      } catch (error) {
        this.isListening = false
        reject(error)
      }
    })
  }

  /**
   * 停止语音识别
   */
  stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop()
      this.isListening = false
    }
  }

  /**
   * 语音播报
   */
  async speak(options: SpeechSynthesisOptions): Promise<void> {
    if (!this.synthesis) {
      throw new Error('语音合成不支持')
    }

    // 停止当前播报
    this.synthesis.cancel()

    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(options.text)
      
      utterance.lang = options.lang || 'zh-CN'
      utterance.rate = options.rate || 1
      utterance.pitch = options.pitch || 1
      utterance.volume = options.volume || 1

      utterance.onend = () => resolve()
      utterance.onerror = (event) => reject(new Error(`语音播报失败: ${event.error}`))

      // 获取中文语音
      const voices = this.synthesis.getVoices()
      const chineseVoice = voices.find(voice => 
        voice.lang.includes('zh') || voice.lang.includes('CN')
      )
      
      if (chineseVoice) {
        utterance.voice = chineseVoice
      }

      this.synthesis.speak(utterance)
    })
  }

  /**
   * 停止语音播报
   */
  stopSpeaking(): void {
    if (this.synthesis) {
      this.synthesis.cancel()
    }
  }

  /**
   * 解析药品信息的语音输入（增强版）
   */
  parseMedicineInput(transcript: string): {
    medicineName?: string
    dosage?: string
    usage?: string
    frequency?: string
    confidence?: number
    suggestions?: string[]
  } {
    const result: any = {}
    const cleanTranscript = this.cleanTranscript(transcript)

    // 智能提取药品名称
    const medicineInfo = this.extractMedicineName(cleanTranscript)
    if (medicineInfo) {
      result.medicineName = medicineInfo.name
      result.confidence = medicineInfo.confidence
      result.suggestions = medicineInfo.suggestions
    }

    // 提取剂量信息
    const dosagePatterns = [
      /(\d+)\s*片/,
      /(\d+)\s*粒/,
      /(\d+)\s*毫升/,
      /(\d+)\s*ml/,
      /(\d+)\s*毫克/,
      /(\d+)\s*mg/,
      /(\d+)\s*滴/
    ]

    for (const pattern of dosagePatterns) {
      const match = transcript.match(pattern)
      if (match) {
        const unit = match[0].replace(match[1], '').trim()
        result.dosage = `${match[1]}${unit}`
        break
      }
    }

    // 提取用法信息
    const usagePatterns = [
      /餐前\s*(\d+)?\s*分钟?/,
      /饭前\s*(\d+)?\s*分钟?/,
      /餐后\s*(\d+)?\s*分钟?/,
      /饭后\s*(\d+)?\s*分钟?/,
      /睡前\s*(\d+)?\s*分钟?/,
      /晨起/,
      /起床后/
    ]

    for (const pattern of usagePatterns) {
      const match = transcript.match(pattern)
      if (match) {
        result.usage = match[0]
        break
      }
    }

    // 提取频次信息
    const frequencyPatterns = [
      /每日\s*(\d+)\s*次/,
      /一日\s*(\d+)\s*次/,
      /每天\s*(\d+)\s*次/,
      /(\d+)\s*次\s*每日/,
      /(\d+)\s*次\s*一日/
    ]

    for (const pattern of frequencyPatterns) {
      const match = transcript.match(pattern)
      if (match) {
        result.frequency = match[0]
        break
      }
    }

    return result
  }

  /**
   * 清理语音识别文本
   */
  private cleanTranscript(transcript: string): string {
    return transcript
      .replace(/[，。！？；：""''（）【】]/g, ' ') // 替换标点符号
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim()
      .toLowerCase()
  }

  /**
   * 智能提取药品名称
   */
  private extractMedicineName(transcript: string): {
    name: string
    confidence: number
    suggestions: string[]
  } | null {
    const words = transcript.split(' ')
    let bestMatch: { name: string; confidence: number; suggestions: string[] } | null = null

    // 1. 精确匹配
    for (const [medicine, aliases] of this.medicineDatabase.entries()) {
      for (const alias of aliases) {
        if (transcript.includes(alias.toLowerCase())) {
          return {
            name: medicine,
            confidence: 1.0,
            suggestions: []
          }
        }
      }
    }

    // 2. 模糊匹配
    for (const medicine of this.commonMedicines) {
      const similarity = this.calculateSimilarity(transcript, medicine.toLowerCase())
      if (similarity > 0.6) {
        if (!bestMatch || similarity > bestMatch.confidence) {
          bestMatch = {
            name: medicine,
            confidence: similarity,
            suggestions: this.getSimilarMedicines(medicine, 3)
          }
        }
      }
    }

    // 3. 部分匹配
    if (!bestMatch) {
      for (const word of words) {
        if (word.length >= 2) {
          for (const medicine of this.commonMedicines) {
            if (medicine.toLowerCase().includes(word) || word.includes(medicine.toLowerCase().substring(0, 2))) {
              const similarity = this.calculateSimilarity(word, medicine.toLowerCase())
              if (similarity > 0.4) {
                if (!bestMatch || similarity > bestMatch.confidence) {
                  bestMatch = {
                    name: medicine,
                    confidence: similarity * 0.8, // 降低部分匹配的置信度
                    suggestions: this.getSimilarMedicines(medicine, 5)
                  }
                }
              }
            }
          }
        }
      }
    }

    return bestMatch
  }

  /**
   * 计算字符串相似度（使用编辑距离）
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const len1 = str1.length
    const len2 = str2.length

    if (len1 === 0) return len2 === 0 ? 1 : 0
    if (len2 === 0) return 0

    const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null))

    for (let i = 0; i <= len1; i++) matrix[i][0] = i
    for (let j = 0; j <= len2; j++) matrix[0][j] = j

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // deletion
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j - 1] + cost // substitution
        )
      }
    }

    const maxLen = Math.max(len1, len2)
    return (maxLen - matrix[len1][len2]) / maxLen
  }

  /**
   * 获取相似药品建议
   */
  private getSimilarMedicines(medicine: string, count: number): string[] {
    const similarities = this.commonMedicines
      .filter(m => m !== medicine)
      .map(m => ({
        name: m,
        similarity: this.calculateSimilarity(medicine.toLowerCase(), m.toLowerCase())
      }))
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, count)
      .map(item => item.name)

    return similarities
  }

  /**
   * 生成用药提醒的语音内容
   */
  generateReminderSpeech(medicineName: string, dosage: string, usage?: string): string {
    let speech = `请注意，现在是服用${medicineName}的时间。`
    
    if (dosage) {
      speech += `请服用${dosage}。`
    }
    
    if (usage) {
      speech += `用法：${usage}。`
    }
    
    speech += '请确认是否已经服药。'
    
    return speech
  }

  /**
   * 获取错误信息
   */
  private getErrorMessage(error: string): string {
    switch (error) {
      case 'no-speech':
        return '没有检测到语音输入'
      case 'audio-capture':
        return '无法访问麦克风'
      case 'not-allowed':
        return '麦克风权限被拒绝'
      case 'network':
        return '网络错误'
      case 'service-not-allowed':
        return '语音识别服务不可用'
      default:
        return `语音识别错误: ${error}`
    }
  }

  /**
   * 请求麦克风权限
   */
  async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error) {
      console.error('麦克风权限请求失败:', error)
      return false
    }
  }

  /**
   * 获取可用的语音列表
   */
  getAvailableVoices(): SpeechSynthesisVoice[] {
    if (!this.synthesis) return []
    return this.synthesis.getVoices().filter(voice => 
      voice.lang.includes('zh') || voice.lang.includes('CN')
    )
  }

  /**
   * 测试语音功能
   */
  async testSpeech(): Promise<{ recognition: boolean, synthesis: boolean }> {
    const result = {
      recognition: this.isSpeechRecognitionSupported(),
      synthesis: this.isSpeechSynthesisSupported()
    }

    if (result.synthesis) {
      try {
        await this.speak({ text: '语音功能测试正常' })
      } catch (error) {
        result.synthesis = false
      }
    }

    return result
  }
}

export const speechService = new SpeechService()
