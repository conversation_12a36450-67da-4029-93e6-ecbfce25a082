"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/app/medication/page.tsx":
/*!*************************************!*\
  !*** ./src/app/medication/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MedicationPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _store_medication__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/medication */ \"(app-pages-browser)/./src/store/medication.ts\");\n/* harmony import */ var _lib_notification_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/notification-service */ \"(app-pages-browser)/./src/lib/notification-service.ts\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,Clock,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,Clock,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,Clock,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,Clock,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,Clock,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BellOff,Clock,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell-off.mjs\");\n/* harmony import */ var _components_medication_MedicineInputForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/medication/MedicineInputForm */ \"(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx\");\n/* harmony import */ var _components_medication_DailyScheduleForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/medication/DailyScheduleForm */ \"(app-pages-browser)/./src/components/medication/DailyScheduleForm.tsx\");\n/* harmony import */ var _components_medication_MedicineReminderList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/medication/MedicineReminderList */ \"(app-pages-browser)/./src/components/medication/MedicineReminderList.tsx\");\n/* harmony import */ var _components_medication_ReminderManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/medication/ReminderManager */ \"(app-pages-browser)/./src/components/medication/ReminderManager.tsx\");\n/* harmony import */ var _components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/medication/ReminderPopup */ \"(app-pages-browser)/./src/components/medication/ReminderPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 强制动态渲染\nconst dynamic = 'force-dynamic';\nfunction MedicationPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [showReminderPopup, setShowReminderPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConfirmationDialog, setShowConfirmationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [reminderSystemActive, setReminderSystemActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingReminder, setEditingReminder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, initialized, initialize } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { reminders, dailySchedule, reminderSettings, startReminderSystem, stopReminderSystem, loadDailySchedule, loadReminderSettings } = (0,_store_medication__WEBPACK_IMPORTED_MODULE_4__.useMedication)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            setMounted(true);\n            initialize();\n        }\n    }[\"MedicationPage.useEffect\"], [\n        initialize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            if (mounted && initialized && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"MedicationPage.useEffect\"], [\n        user,\n        initialized,\n        router,\n        mounted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            if (user) {\n                loadDailySchedule(user.id);\n                loadReminderSettings(user.id);\n            }\n        }\n    }[\"MedicationPage.useEffect\"], [\n        user,\n        loadDailySchedule,\n        loadReminderSettings\n    ]);\n    // 监听提醒事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            const handleReminderPopup = {\n                \"MedicationPage.useEffect.handleReminderPopup\": (event)=>{\n                    setShowReminderPopup(event.detail);\n                }\n            }[\"MedicationPage.useEffect.handleReminderPopup\"];\n            const handleConfirmationDialog = {\n                \"MedicationPage.useEffect.handleConfirmationDialog\": (event)=>{\n                    setShowConfirmationDialog(event.detail);\n                }\n            }[\"MedicationPage.useEffect.handleConfirmationDialog\"];\n            const handleReminderConfirmed = {\n                \"MedicationPage.useEffect.handleReminderConfirmed\": (event)=>{\n                    setShowReminderPopup(null);\n                    setShowConfirmationDialog(null);\n                }\n            }[\"MedicationPage.useEffect.handleReminderConfirmed\"];\n            window.addEventListener('medication-reminder-popup', handleReminderPopup);\n            window.addEventListener('medication-confirmation-dialog', handleConfirmationDialog);\n            window.addEventListener('medication-reminder-confirmed', handleReminderConfirmed);\n            return ({\n                \"MedicationPage.useEffect\": ()=>{\n                    window.removeEventListener('medication-reminder-popup', handleReminderPopup);\n                    window.removeEventListener('medication-confirmation-dialog', handleConfirmationDialog);\n                    window.removeEventListener('medication-reminder-confirmed', handleReminderConfirmed);\n                }\n            })[\"MedicationPage.useEffect\"];\n        }\n    }[\"MedicationPage.useEffect\"], []);\n    const handleStartReminderSystem = async ()=>{\n        if (!user) return;\n        try {\n            // 请求通知权限\n            const notificationService = (0,_lib_notification_service__WEBPACK_IMPORTED_MODULE_5__.getNotificationService)();\n            const hasPermission = await notificationService.requestNotificationPermission();\n            if (!hasPermission) {\n                alert('需要通知权限才能启用提醒系统');\n                return;\n            }\n            await startReminderSystem(user.id);\n            setReminderSystemActive(true);\n        } catch (error) {\n            console.error('启动提醒系统失败:', error);\n            alert('启动提醒系统失败，请检查设置');\n        }\n    };\n    const handleStopReminderSystem = ()=>{\n        stopReminderSystem();\n        setReminderSystemActive(false);\n    };\n    const handleReminderConfirm = (confirmed)=>{\n        if (showReminderPopup && user) {\n            // 这里可以记录用药状态\n            console.log(\"用药确认: \".concat(showReminderPopup.medicineName, \" - \").concat(confirmed ? '已服药' : '未服药'));\n        }\n        setShowReminderPopup(null);\n    };\n    const handleConfirmationSubmit = (confirmed)=>{\n        if (showConfirmationDialog && user) {\n            // 记录用药状态\n            console.log(\"用药确认: \".concat(showConfirmationDialog.medicineName, \" - \").concat(confirmed ? '已服药' : '错过'));\n        }\n        setShowConfirmationDialog(null);\n    };\n    // 测试提醒功能\n    const testReminder = (level)=>{\n        const testNotification = {\n            id: \"test-\".concat(Date.now()),\n            reminderId: 'test-reminder',\n            medicineName: '测试药物',\n            dosage: '1片',\n            usage: '餐后服用',\n            scheduledTime: new Date().toLocaleTimeString('zh-CN', {\n                hour: '2-digit',\n                minute: '2-digit'\n            }),\n            level,\n            isActive: true,\n            createdAt: new Date()\n        };\n        // 触发相应级别的提醒\n        const eventName = level === 3 ? 'confirmation-dialog-urgent' : level === 2 ? 'reminder-popup-urgent' : 'reminder-popup';\n        window.dispatchEvent(new CustomEvent(\"medication-\".concat(eventName), {\n            detail: testNotification\n        }));\n    };\n    if (!mounted || !initialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载用药提醒系统...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    const tabs = [\n        {\n            id: 'list',\n            label: '提醒列表',\n            icon: _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'add',\n            label: '添加提醒',\n            icon: _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'schedule',\n            label: '作息设置',\n            icon: _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'settings',\n            label: '提醒设置',\n            icon: _barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-500 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                children: \"用药提醒系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"智能用药管理，健康生活助手\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"提醒系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: reminderSystemActive ? handleStopReminderSystem : handleStartReminderSystem,\n                                                className: \"flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors \".concat(reminderSystemActive ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'),\n                                                children: reminderSystemActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"已启用\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BellOff_Clock_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"已禁用\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/dashboard'),\n                                        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: \"返回主页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-8\",\n                        children: tabs.map((tab)=>{\n                            const IconComponent = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center px-4 py-4 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 19\n                                    }, this),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    activeTab === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicineReminderList__WEBPACK_IMPORTED_MODULE_8__.MedicineReminderList, {\n                        userId: user.id,\n                        onEdit: (reminder)=>{\n                            setEditingReminder(reminder);\n                            setActiveTab('add');\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicineInputForm__WEBPACK_IMPORTED_MODULE_6__.MedicineInputForm, {\n                        userId: user.id,\n                        onSuccess: ()=>{\n                            setActiveTab('list');\n                            setEditingReminder(null);\n                        },\n                        onCancel: ()=>{\n                            setActiveTab('list');\n                            setEditingReminder(null);\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'schedule' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_DailyScheduleForm__WEBPACK_IMPORTED_MODULE_7__.DailyScheduleForm, {\n                        userId: user.id,\n                        onSuccess: ()=>{\n                        // 可以选择切换到其他标签页或显示成功消息\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                children: \"提醒设置\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-700 mb-3\",\n                                        children: \"测试提醒功能\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(1),\n                                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700\",\n                                                children: \"测试第一级提醒（普通）\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(2),\n                                                className: \"w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700\",\n                                                children: \"测试第二级提醒（重要）\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(3),\n                                                className: \"w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700\",\n                                                children: \"测试第三级提醒（紧急）\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"更多提醒设置功能开发中...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            showReminderPopup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_10__.ReminderPopup, {\n                notification: showReminderPopup,\n                onConfirm: handleReminderConfirm,\n                onClose: ()=>setShowReminderPopup(null)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this),\n            showConfirmationDialog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_10__.ConfirmationDialog, {\n                notification: showConfirmationDialog,\n                onConfirm: handleConfirmationSubmit,\n                onClose: ()=>setShowConfirmationDialog(null)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderManager__WEBPACK_IMPORTED_MODULE_9__.ReminderManager, {}, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicationPage, \"4HKtr2YmjS0dqLXuR7pouWYelUw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _store_medication__WEBPACK_IMPORTED_MODULE_4__.useMedication,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = MedicationPage;\nvar _c;\n$RefreshReg$(_c, \"MedicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbWVkaWNhdGlvbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0E7QUFDTDtBQUNZO0FBQ2lCO0FBQ007QUFDSTtBQUNBO0FBQ007QUFDVjtBQUdnQjtBQUl6RixTQUFTO0FBQ0YsTUFBTWtCLFVBQVUsZ0JBQWU7QUFFdkIsU0FBU0M7O0lBQ3RCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHckIsK0NBQVFBLENBQTJDO0lBQ3JGLE1BQU0sQ0FBQ3NCLG1CQUFtQkMscUJBQXFCLEdBQUd2QiwrQ0FBUUEsQ0FBOEI7SUFDeEYsTUFBTSxDQUFDd0Isd0JBQXdCQywwQkFBMEIsR0FBR3pCLCtDQUFRQSxDQUE4QjtJQUNsRyxNQUFNLENBQUMwQixzQkFBc0JDLHdCQUF3QixHQUFHM0IsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDNEIsaUJBQWlCQyxtQkFBbUIsR0FBRzdCLCtDQUFRQSxDQUEwQjtJQUNoRixNQUFNLENBQUM4QixTQUFTQyxXQUFXLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNLEVBQUVnQyxJQUFJLEVBQUVDLFdBQVcsRUFBRUMsVUFBVSxFQUFFLEdBQUcvQixvREFBT0E7SUFDakQsTUFBTSxFQUNKZ0MsU0FBUyxFQUNUQyxhQUFhLEVBQ2JDLGdCQUFnQixFQUNoQkMsbUJBQW1CLEVBQ25CQyxrQkFBa0IsRUFDbEJDLGlCQUFpQixFQUNqQkMsb0JBQW9CLEVBQ3JCLEdBQUdyQyxnRUFBYUE7SUFDakIsTUFBTXNDLFNBQVN4QywwREFBU0E7SUFFeEJELGdEQUFTQTtvQ0FBQztZQUNSOEIsV0FBVztZQUNYRztRQUNGO21DQUFHO1FBQUNBO0tBQVc7SUFFZmpDLGdEQUFTQTtvQ0FBQztZQUNSLElBQUk2QixXQUFXRyxlQUFlLENBQUNELE1BQU07Z0JBQ25DVSxPQUFPQyxJQUFJLENBQUM7WUFDZDtRQUNGO21DQUFHO1FBQUNYO1FBQU1DO1FBQWFTO1FBQVFaO0tBQVE7SUFFdkM3QixnREFBU0E7b0NBQUM7WUFDUixJQUFJK0IsTUFBTTtnQkFDUlEsa0JBQWtCUixLQUFLWSxFQUFFO2dCQUN6QkgscUJBQXFCVCxLQUFLWSxFQUFFO1lBQzlCO1FBQ0Y7bUNBQUc7UUFBQ1o7UUFBTVE7UUFBbUJDO0tBQXFCO0lBRWxELFNBQVM7SUFDVHhDLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU00QztnRUFBc0IsQ0FBQ0M7b0JBQzNCdkIscUJBQXFCdUIsTUFBTUMsTUFBTTtnQkFDbkM7O1lBRUEsTUFBTUM7cUVBQTJCLENBQUNGO29CQUNoQ3JCLDBCQUEwQnFCLE1BQU1DLE1BQU07Z0JBQ3hDOztZQUVBLE1BQU1FO29FQUEwQixDQUFDSDtvQkFDL0J2QixxQkFBcUI7b0JBQ3JCRSwwQkFBMEI7Z0JBQzVCOztZQUVBeUIsT0FBT0MsZ0JBQWdCLENBQUMsNkJBQTZCTjtZQUNyREssT0FBT0MsZ0JBQWdCLENBQUMsa0NBQWtDSDtZQUMxREUsT0FBT0MsZ0JBQWdCLENBQUMsaUNBQWlDRjtZQUV6RDs0Q0FBTztvQkFDTEMsT0FBT0UsbUJBQW1CLENBQUMsNkJBQTZCUDtvQkFDeERLLE9BQU9FLG1CQUFtQixDQUFDLGtDQUFrQ0o7b0JBQzdERSxPQUFPRSxtQkFBbUIsQ0FBQyxpQ0FBaUNIO2dCQUM5RDs7UUFDRjttQ0FBRyxFQUFFO0lBRUwsTUFBTUksNEJBQTRCO1FBQ2hDLElBQUksQ0FBQ3JCLE1BQU07UUFFWCxJQUFJO1lBQ0YsU0FBUztZQUNULE1BQU1zQixzQkFBc0JqRCxpRkFBc0JBO1lBQ2xELE1BQU1rRCxnQkFBZ0IsTUFBTUQsb0JBQW9CRSw2QkFBNkI7WUFDN0UsSUFBSSxDQUFDRCxlQUFlO2dCQUNsQkUsTUFBTTtnQkFDTjtZQUNGO1lBRUEsTUFBTW5CLG9CQUFvQk4sS0FBS1ksRUFBRTtZQUNqQ2pCLHdCQUF3QjtRQUMxQixFQUFFLE9BQU8rQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxhQUFhQTtZQUMzQkQsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNRywyQkFBMkI7UUFDL0JyQjtRQUNBWix3QkFBd0I7SUFDMUI7SUFFQSxNQUFNa0Msd0JBQXdCLENBQUNDO1FBQzdCLElBQUl4QyxxQkFBcUJVLE1BQU07WUFDN0IsYUFBYTtZQUNiMkIsUUFBUUksR0FBRyxDQUFDLFNBQTZDRCxPQUFwQ3hDLGtCQUFrQjBDLFlBQVksRUFBQyxPQUErQixPQUExQkYsWUFBWSxRQUFRO1FBQy9FO1FBQ0F2QyxxQkFBcUI7SUFDdkI7SUFFQSxNQUFNMEMsMkJBQTJCLENBQUNIO1FBQ2hDLElBQUl0QywwQkFBMEJRLE1BQU07WUFDbEMsU0FBUztZQUNUMkIsUUFBUUksR0FBRyxDQUFDLFNBQWtERCxPQUF6Q3RDLHVCQUF1QndDLFlBQVksRUFBQyxPQUE4QixPQUF6QkYsWUFBWSxRQUFRO1FBQ3BGO1FBQ0FyQywwQkFBMEI7SUFDNUI7SUFFQSxTQUFTO0lBQ1QsTUFBTXlDLGVBQWUsQ0FBQ0M7UUFDcEIsTUFBTUMsbUJBQW1CO1lBQ3ZCeEIsSUFBSSxRQUFtQixPQUFYeUIsS0FBS0MsR0FBRztZQUNwQkMsWUFBWTtZQUNaUCxjQUFjO1lBQ2RRLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxlQUFlLElBQUlMLE9BQU9NLGtCQUFrQixDQUFDLFNBQVM7Z0JBQ3BEQyxNQUFNO2dCQUNOQyxRQUFRO1lBQ1Y7WUFDQVY7WUFDQVcsVUFBVTtZQUNWQyxXQUFXLElBQUlWO1FBQ2pCO1FBRUEsWUFBWTtRQUNaLE1BQU1XLFlBQVliLFVBQVUsSUFBSSwrQkFDZkEsVUFBVSxJQUFJLDBCQUNkO1FBRWpCakIsT0FBTytCLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLGNBQXdCLE9BQVZGLFlBQWE7WUFDOURqQyxRQUFRcUI7UUFDVjtJQUNGO0lBRUEsSUFBSSxDQUFDdEMsV0FBVyxDQUFDRyxhQUFhO1FBQzVCLHFCQUNFLDhEQUFDa0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsSUFBSSxDQUFDcEQsTUFBTTtRQUNULE9BQU87SUFDVDtJQUVBLE1BQU1zRCxPQUFPO1FBQ1g7WUFBRTFDLElBQUk7WUFBUTJDLE9BQU87WUFBUUMsTUFBTS9FLGtIQUFJQTtRQUFDO1FBQ3hDO1lBQUVtQyxJQUFJO1lBQU8yQyxPQUFPO1lBQVFDLE1BQU1sRixrSEFBSUE7UUFBQztRQUN2QztZQUFFc0MsSUFBSTtZQUFZMkMsT0FBTztZQUFRQyxNQUFNaEYsa0hBQUtBO1FBQUM7UUFDN0M7WUFBRW9DLElBQUk7WUFBWTJDLE9BQU87WUFBUUMsTUFBTWpGLGtIQUFRQTtRQUFDO0tBQ2pEO0lBRUQscUJBQ0UsOERBQUM0RTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzNFLGtIQUFJQTt3Q0FBQzJFLFdBQVU7Ozs7OztrREFDaEIsOERBQUNEOzswREFDQyw4REFBQ007Z0RBQUdMLFdBQVU7MERBQW1DOzs7Ozs7MERBQ2pELDhEQUFDQztnREFBRUQsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJekMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTTtnREFBS04sV0FBVTswREFBd0I7Ozs7OzswREFDeEMsOERBQUNPO2dEQUNDQyxTQUFTbEUsdUJBQXVCa0MsMkJBQTJCUDtnREFDM0QrQixXQUFXLGtGQUlWLE9BSEMxRCx1QkFDSSxtREFDQTswREFHTEEscUNBQ0M7O3NFQUNFLDhEQUFDaEIsa0hBQUlBOzREQUFDMEUsV0FBVTs7Ozs7O3dEQUFpQjs7aUZBSW5DOztzRUFDRSw4REFBQ3pFLGtIQUFPQTs0REFBQ3lFLFdBQVU7Ozs7Ozt3REFBaUI7Ozs7Ozs7Ozs7Ozs7O2tEQVE1Qyw4REFBQ087d0NBQ0NDLFNBQVMsSUFBTWxELE9BQU9DLElBQUksQ0FBQzt3Q0FDM0J5QyxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNULDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNaRSxLQUFLTyxHQUFHLENBQUNDLENBQUFBOzRCQUNSLE1BQU1DLGdCQUFnQkQsSUFBSU4sSUFBSTs0QkFDOUIscUJBQ0UsOERBQUNHO2dDQUVDQyxTQUFTLElBQU12RSxhQUFheUUsSUFBSWxELEVBQUU7Z0NBQ2xDd0MsV0FBVyxnRkFJVixPQUhDaEUsY0FBYzBFLElBQUlsRCxFQUFFLEdBQ2hCLGtDQUNBOztrREFHTiw4REFBQ21EO3dDQUFjWCxXQUFVOzs7Ozs7b0NBQ3hCVSxJQUFJUCxLQUFLOzsrQkFUTE8sSUFBSWxELEVBQUU7Ozs7O3dCQVlqQjs7Ozs7Ozs7Ozs7Ozs7OzswQkFNTiw4REFBQ3VDO2dCQUFJQyxXQUFVOztvQkFDWmhFLGNBQWMsd0JBQ2IsOERBQUNOLDZGQUFvQkE7d0JBQ25Ca0YsUUFBUWhFLEtBQUtZLEVBQUU7d0JBQ2ZxRCxRQUFRLENBQUNDOzRCQUNQckUsbUJBQW1CcUU7NEJBQ25CN0UsYUFBYTt3QkFDZjs7Ozs7O29CQUlIRCxjQUFjLHVCQUNiLDhEQUFDUix1RkFBaUJBO3dCQUNoQm9GLFFBQVFoRSxLQUFLWSxFQUFFO3dCQUNmdUQsV0FBVzs0QkFDVDlFLGFBQWE7NEJBQ2JRLG1CQUFtQjt3QkFDckI7d0JBQ0F1RSxVQUFVOzRCQUNSL0UsYUFBYTs0QkFDYlEsbUJBQW1CO3dCQUNyQjs7Ozs7O29CQUlIVCxjQUFjLDRCQUNiLDhEQUFDUCx1RkFBaUJBO3dCQUNoQm1GLFFBQVFoRSxLQUFLWSxFQUFFO3dCQUNmdUQsV0FBVzt3QkFDVCxzQkFBc0I7d0JBQ3hCOzs7Ozs7b0JBSUgvRSxjQUFjLDRCQUNiLDhEQUFDK0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDaUI7Z0NBQUdqQixXQUFVOzBDQUEyQzs7Ozs7OzBDQUd6RCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDa0I7d0NBQUdsQixXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTztnREFDQ1ksTUFBSztnREFDTFgsU0FBUyxJQUFNMUIsYUFBYTtnREFDNUJrQixXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUNPO2dEQUNDWSxNQUFLO2dEQUNMWCxTQUFTLElBQU0xQixhQUFhO2dEQUM1QmtCLFdBQVU7MERBQ1g7Ozs7OzswREFHRCw4REFBQ087Z0RBQ0NZLE1BQUs7Z0RBQ0xYLFNBQVMsSUFBTTFCLGFBQWE7Z0RBQzVCa0IsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1MLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUVELFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9wQzlELG1DQUNDLDhEQUFDTixnRkFBYUE7Z0JBQ1p3RixjQUFjbEY7Z0JBQ2RtRixXQUFXNUM7Z0JBQ1g2QyxTQUFTLElBQU1uRixxQkFBcUI7Ozs7OztZQUt2Q0Msd0NBQ0MsOERBQUNQLHFGQUFrQkE7Z0JBQ2pCdUYsY0FBY2hGO2dCQUNkaUYsV0FBV3hDO2dCQUNYeUMsU0FBUyxJQUFNakYsMEJBQTBCOzs7Ozs7MEJBSzdDLDhEQUFDVixtRkFBZUE7Ozs7Ozs7Ozs7O0FBR3RCO0dBdlV3Qkk7O1FBUW9CaEIsZ0RBQU9BO1FBUzdDQyw0REFBYUE7UUFDRkYsc0RBQVNBOzs7S0FsQkZpQiIsInNvdXJjZXMiOlsiRTpcXGNhcmViYW9cXGNhcmVhcHAtd2ViXFxzcmNcXGFwcFxcbWVkaWNhdGlvblxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL3N0b3JlL2F1dGgnXG5pbXBvcnQgeyB1c2VNZWRpY2F0aW9uIH0gZnJvbSAnQC9zdG9yZS9tZWRpY2F0aW9uJ1xuaW1wb3J0IHsgZ2V0Tm90aWZpY2F0aW9uU2VydmljZSB9IGZyb20gJ0AvbGliL25vdGlmaWNhdGlvbi1zZXJ2aWNlJ1xuaW1wb3J0IHsgUGx1cywgU2V0dGluZ3MsIENsb2NrLCBQaWxsLCBCZWxsLCBCZWxsT2ZmIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgTWVkaWNpbmVJbnB1dEZvcm0gfSBmcm9tICdAL2NvbXBvbmVudHMvbWVkaWNhdGlvbi9NZWRpY2luZUlucHV0Rm9ybSdcbmltcG9ydCB7IERhaWx5U2NoZWR1bGVGb3JtIH0gZnJvbSAnQC9jb21wb25lbnRzL21lZGljYXRpb24vRGFpbHlTY2hlZHVsZUZvcm0nXG5pbXBvcnQgeyBNZWRpY2luZVJlbWluZGVyTGlzdCB9IGZyb20gJ0AvY29tcG9uZW50cy9tZWRpY2F0aW9uL01lZGljaW5lUmVtaW5kZXJMaXN0J1xuaW1wb3J0IHsgUmVtaW5kZXJNYW5hZ2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL21lZGljYXRpb24vUmVtaW5kZXJNYW5hZ2VyJ1xuaW1wb3J0IHsgTWVkaWNhdGlvblN0YXRpc3RpY3MgfSBmcm9tICdAL2NvbXBvbmVudHMvbWVkaWNhdGlvbi9NZWRpY2F0aW9uU3RhdGlzdGljcydcbmltcG9ydCB7IE1lZGljYXRpb25IaXN0b3J5IH0gZnJvbSAnQC9jb21wb25lbnRzL21lZGljYXRpb24vTWVkaWNhdGlvbkhpc3RvcnknXG5pbXBvcnQgeyBSZW1pbmRlclBvcHVwLCBDb25maXJtYXRpb25EaWFsb2cgfSBmcm9tICdAL2NvbXBvbmVudHMvbWVkaWNhdGlvbi9SZW1pbmRlclBvcHVwJ1xuaW1wb3J0IHR5cGUgeyBSZW1pbmRlck5vdGlmaWNhdGlvbiB9IGZyb20gJ0AvbGliL25vdGlmaWNhdGlvbi1zZXJ2aWNlJ1xuaW1wb3J0IHR5cGUgeyBNZWRpY2luZVJlbWluZGVyIH0gZnJvbSAnQC90eXBlcydcblxuLy8g5by65Yi25Yqo5oCB5riy5p+TXG5leHBvcnQgY29uc3QgZHluYW1pYyA9ICdmb3JjZS1keW5hbWljJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNZWRpY2F0aW9uUGFnZSgpIHtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPCdsaXN0JyB8ICdhZGQnIHwgJ3NjaGVkdWxlJyB8ICdzZXR0aW5ncyc+KCdsaXN0JylcbiAgY29uc3QgW3Nob3dSZW1pbmRlclBvcHVwLCBzZXRTaG93UmVtaW5kZXJQb3B1cF0gPSB1c2VTdGF0ZTxSZW1pbmRlck5vdGlmaWNhdGlvbiB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzaG93Q29uZmlybWF0aW9uRGlhbG9nLCBzZXRTaG93Q29uZmlybWF0aW9uRGlhbG9nXSA9IHVzZVN0YXRlPFJlbWluZGVyTm90aWZpY2F0aW9uIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3JlbWluZGVyU3lzdGVtQWN0aXZlLCBzZXRSZW1pbmRlclN5c3RlbUFjdGl2ZV0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2VkaXRpbmdSZW1pbmRlciwgc2V0RWRpdGluZ1JlbWluZGVyXSA9IHVzZVN0YXRlPE1lZGljaW5lUmVtaW5kZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCB7IHVzZXIsIGluaXRpYWxpemVkLCBpbml0aWFsaXplIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgeyBcbiAgICByZW1pbmRlcnMsIFxuICAgIGRhaWx5U2NoZWR1bGUsIFxuICAgIHJlbWluZGVyU2V0dGluZ3MsXG4gICAgc3RhcnRSZW1pbmRlclN5c3RlbSwgXG4gICAgc3RvcFJlbWluZGVyU3lzdGVtLFxuICAgIGxvYWREYWlseVNjaGVkdWxlLFxuICAgIGxvYWRSZW1pbmRlclNldHRpbmdzXG4gIH0gPSB1c2VNZWRpY2F0aW9uKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldE1vdW50ZWQodHJ1ZSlcbiAgICBpbml0aWFsaXplKClcbiAgfSwgW2luaXRpYWxpemVdKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG1vdW50ZWQgJiYgaW5pdGlhbGl6ZWQgJiYgIXVzZXIpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvYXV0aCcpXG4gICAgfVxuICB9LCBbdXNlciwgaW5pdGlhbGl6ZWQsIHJvdXRlciwgbW91bnRlZF0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlcikge1xuICAgICAgbG9hZERhaWx5U2NoZWR1bGUodXNlci5pZClcbiAgICAgIGxvYWRSZW1pbmRlclNldHRpbmdzKHVzZXIuaWQpXG4gICAgfVxuICB9LCBbdXNlciwgbG9hZERhaWx5U2NoZWR1bGUsIGxvYWRSZW1pbmRlclNldHRpbmdzXSlcblxuICAvLyDnm5HlkKzmj5DphpLkuovku7ZcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVSZW1pbmRlclBvcHVwID0gKGV2ZW50OiBDdXN0b21FdmVudCkgPT4ge1xuICAgICAgc2V0U2hvd1JlbWluZGVyUG9wdXAoZXZlbnQuZGV0YWlsKVxuICAgIH1cblxuICAgIGNvbnN0IGhhbmRsZUNvbmZpcm1hdGlvbkRpYWxvZyA9IChldmVudDogQ3VzdG9tRXZlbnQpID0+IHtcbiAgICAgIHNldFNob3dDb25maXJtYXRpb25EaWFsb2coZXZlbnQuZGV0YWlsKVxuICAgIH1cblxuICAgIGNvbnN0IGhhbmRsZVJlbWluZGVyQ29uZmlybWVkID0gKGV2ZW50OiBDdXN0b21FdmVudCkgPT4ge1xuICAgICAgc2V0U2hvd1JlbWluZGVyUG9wdXAobnVsbClcbiAgICAgIHNldFNob3dDb25maXJtYXRpb25EaWFsb2cobnVsbClcbiAgICB9XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbWVkaWNhdGlvbi1yZW1pbmRlci1wb3B1cCcsIGhhbmRsZVJlbWluZGVyUG9wdXAgYXMgRXZlbnRMaXN0ZW5lcilcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbWVkaWNhdGlvbi1jb25maXJtYXRpb24tZGlhbG9nJywgaGFuZGxlQ29uZmlybWF0aW9uRGlhbG9nIGFzIEV2ZW50TGlzdGVuZXIpXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lZGljYXRpb24tcmVtaW5kZXItY29uZmlybWVkJywgaGFuZGxlUmVtaW5kZXJDb25maXJtZWQgYXMgRXZlbnRMaXN0ZW5lcilcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVkaWNhdGlvbi1yZW1pbmRlci1wb3B1cCcsIGhhbmRsZVJlbWluZGVyUG9wdXAgYXMgRXZlbnRMaXN0ZW5lcilcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtZWRpY2F0aW9uLWNvbmZpcm1hdGlvbi1kaWFsb2cnLCBoYW5kbGVDb25maXJtYXRpb25EaWFsb2cgYXMgRXZlbnRMaXN0ZW5lcilcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtZWRpY2F0aW9uLXJlbWluZGVyLWNvbmZpcm1lZCcsIGhhbmRsZVJlbWluZGVyQ29uZmlybWVkIGFzIEV2ZW50TGlzdGVuZXIpXG4gICAgfVxuICB9LCBbXSlcblxuICBjb25zdCBoYW5kbGVTdGFydFJlbWluZGVyU3lzdGVtID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgLy8g6K+35rGC6YCa55+l5p2D6ZmQXG4gICAgICBjb25zdCBub3RpZmljYXRpb25TZXJ2aWNlID0gZ2V0Tm90aWZpY2F0aW9uU2VydmljZSgpXG4gICAgICBjb25zdCBoYXNQZXJtaXNzaW9uID0gYXdhaXQgbm90aWZpY2F0aW9uU2VydmljZS5yZXF1ZXN0Tm90aWZpY2F0aW9uUGVybWlzc2lvbigpXG4gICAgICBpZiAoIWhhc1Blcm1pc3Npb24pIHtcbiAgICAgICAgYWxlcnQoJ+mcgOimgemAmuefpeadg+mZkOaJjeiDveWQr+eUqOaPkOmGkuezu+e7nycpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBhd2FpdCBzdGFydFJlbWluZGVyU3lzdGVtKHVzZXIuaWQpXG4gICAgICBzZXRSZW1pbmRlclN5c3RlbUFjdGl2ZSh0cnVlKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCflkK/liqjmj5DphpLns7vnu5/lpLHotKU6JywgZXJyb3IpXG4gICAgICBhbGVydCgn5ZCv5Yqo5o+Q6YaS57O757uf5aSx6LSl77yM6K+35qOA5p+l6K6+572uJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVTdG9wUmVtaW5kZXJTeXN0ZW0gPSAoKSA9PiB7XG4gICAgc3RvcFJlbWluZGVyU3lzdGVtKClcbiAgICBzZXRSZW1pbmRlclN5c3RlbUFjdGl2ZShmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVJlbWluZGVyQ29uZmlybSA9IChjb25maXJtZWQ6IGJvb2xlYW4pID0+IHtcbiAgICBpZiAoc2hvd1JlbWluZGVyUG9wdXAgJiYgdXNlcikge1xuICAgICAgLy8g6L+Z6YeM5Y+v5Lul6K6w5b2V55So6I2v54q25oCBXG4gICAgICBjb25zb2xlLmxvZyhg55So6I2v56Gu6K6kOiAke3Nob3dSZW1pbmRlclBvcHVwLm1lZGljaW5lTmFtZX0gLSAke2NvbmZpcm1lZCA/ICflt7LmnI3oja8nIDogJ+acquacjeiNryd9YClcbiAgICB9XG4gICAgc2V0U2hvd1JlbWluZGVyUG9wdXAobnVsbClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUNvbmZpcm1hdGlvblN1Ym1pdCA9IChjb25maXJtZWQ6IGJvb2xlYW4pID0+IHtcbiAgICBpZiAoc2hvd0NvbmZpcm1hdGlvbkRpYWxvZyAmJiB1c2VyKSB7XG4gICAgICAvLyDorrDlvZXnlKjoja/nirbmgIFcbiAgICAgIGNvbnNvbGUubG9nKGDnlKjoja/noa7orqQ6ICR7c2hvd0NvbmZpcm1hdGlvbkRpYWxvZy5tZWRpY2luZU5hbWV9IC0gJHtjb25maXJtZWQgPyAn5bey5pyN6I2vJyA6ICfplJnov4cnfWApXG4gICAgfVxuICAgIHNldFNob3dDb25maXJtYXRpb25EaWFsb2cobnVsbClcbiAgfVxuXG4gIC8vIOa1i+ivleaPkOmGkuWKn+iDvVxuICBjb25zdCB0ZXN0UmVtaW5kZXIgPSAobGV2ZWw6IDEgfCAyIHwgMykgPT4ge1xuICAgIGNvbnN0IHRlc3ROb3RpZmljYXRpb24gPSB7XG4gICAgICBpZDogYHRlc3QtJHtEYXRlLm5vdygpfWAsXG4gICAgICByZW1pbmRlcklkOiAndGVzdC1yZW1pbmRlcicsXG4gICAgICBtZWRpY2luZU5hbWU6ICfmtYvor5Xoja/niaknLFxuICAgICAgZG9zYWdlOiAnMeeJhycsXG4gICAgICB1c2FnZTogJ+mkkOWQjuacjeeUqCcsXG4gICAgICBzY2hlZHVsZWRUaW1lOiBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygnemgtQ04nLCB7XG4gICAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgICAgbWludXRlOiAnMi1kaWdpdCdcbiAgICAgIH0pLFxuICAgICAgbGV2ZWwsXG4gICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKVxuICAgIH1cblxuICAgIC8vIOinpuWPkeebuOW6lOe6p+WIq+eahOaPkOmGklxuICAgIGNvbnN0IGV2ZW50TmFtZSA9IGxldmVsID09PSAzID8gJ2NvbmZpcm1hdGlvbi1kaWFsb2ctdXJnZW50JyA6XG4gICAgICAgICAgICAgICAgICAgICBsZXZlbCA9PT0gMiA/ICdyZW1pbmRlci1wb3B1cC11cmdlbnQnIDpcbiAgICAgICAgICAgICAgICAgICAgICdyZW1pbmRlci1wb3B1cCdcblxuICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudChgbWVkaWNhdGlvbi0ke2V2ZW50TmFtZX1gLCB7XG4gICAgICBkZXRhaWw6IHRlc3ROb3RpZmljYXRpb25cbiAgICB9KSlcbiAgfVxuXG4gIGlmICghbW91bnRlZCB8fCAhaW5pdGlhbGl6ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby0xMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJvcmRlci00IGJvcmRlci1ibHVlLTYwMCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPuato+WcqOWKoOi9veeUqOiNr+aPkOmGkuezu+e7ny4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBpZiAoIXVzZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgY29uc3QgdGFicyA9IFtcbiAgICB7IGlkOiAnbGlzdCcsIGxhYmVsOiAn5o+Q6YaS5YiX6KGoJywgaWNvbjogUGlsbCB9LFxuICAgIHsgaWQ6ICdhZGQnLCBsYWJlbDogJ+a3u+WKoOaPkOmGkicsIGljb246IFBsdXMgfSxcbiAgICB7IGlkOiAnc2NoZWR1bGUnLCBsYWJlbDogJ+S9nOaBr+iuvue9ricsIGljb246IENsb2NrIH0sXG4gICAgeyBpZDogJ3NldHRpbmdzJywgbGFiZWw6ICfmj5DphpLorr7nva4nLCBpY29uOiBTZXR0aW5ncyB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tMTAwXCI+XG4gICAgICB7Lyog5aS06YOoICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctc21cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8UGlsbCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtYmx1ZS01MDAgbXItM1wiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCI+55So6I2v5o+Q6YaS57O757ufPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7mmbrog73nlKjoja/nrqHnkIbvvIzlgaXlurfnlJ/mtLvliqnmiYs8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIHsvKiDmj5DphpLns7vnu5/nirbmgIEgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+5o+Q6YaS57O757ufPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3JlbWluZGVyU3lzdGVtQWN0aXZlID8gaGFuZGxlU3RvcFJlbWluZGVyU3lzdGVtIDogaGFuZGxlU3RhcnRSZW1pbmRlclN5c3RlbX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICByZW1pbmRlclN5c3RlbUFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTcwMCBob3ZlcjpiZy1ncmVlbi0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTIwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtyZW1pbmRlclN5c3RlbUFjdGl2ZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8QmVsbCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIOW3suWQr+eUqFxuICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPEJlbGxPZmYgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICDlt7LnpoHnlKhcbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog6L+U5Zue5oyJ6ZKuICovfVxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg6L+U5Zue5Li76aG1XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDmoIfnrb7pobXlr7zoiKogKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC04XCI+XG4gICAgICAgICAgICB7dGFicy5tYXAodGFiID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgSWNvbkNvbXBvbmVudCA9IHRhYi5pY29uXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXt0YWIuaWR9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIodGFiLmlkIGFzIGFueSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTQgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gdGFiLmlkXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIHRleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAge3RhYi5sYWJlbH1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDkuLvopoHlhoXlrrkgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICB7YWN0aXZlVGFiID09PSAnbGlzdCcgJiYgKFxuICAgICAgICAgIDxNZWRpY2luZVJlbWluZGVyTGlzdCBcbiAgICAgICAgICAgIHVzZXJJZD17dXNlci5pZH0gXG4gICAgICAgICAgICBvbkVkaXQ9eyhyZW1pbmRlcikgPT4ge1xuICAgICAgICAgICAgICBzZXRFZGl0aW5nUmVtaW5kZXIocmVtaW5kZXIpXG4gICAgICAgICAgICAgIHNldEFjdGl2ZVRhYignYWRkJylcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnYWRkJyAmJiAoXG4gICAgICAgICAgPE1lZGljaW5lSW5wdXRGb3JtXG4gICAgICAgICAgICB1c2VySWQ9e3VzZXIuaWR9XG4gICAgICAgICAgICBvblN1Y2Nlc3M9eygpID0+IHtcbiAgICAgICAgICAgICAgc2V0QWN0aXZlVGFiKCdsaXN0JylcbiAgICAgICAgICAgICAgc2V0RWRpdGluZ1JlbWluZGVyKG51bGwpXG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICAgICAgc2V0QWN0aXZlVGFiKCdsaXN0JylcbiAgICAgICAgICAgICAgc2V0RWRpdGluZ1JlbWluZGVyKG51bGwpXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3NjaGVkdWxlJyAmJiAoXG4gICAgICAgICAgPERhaWx5U2NoZWR1bGVGb3JtXG4gICAgICAgICAgICB1c2VySWQ9e3VzZXIuaWR9XG4gICAgICAgICAgICBvblN1Y2Nlc3M9eygpID0+IHtcbiAgICAgICAgICAgICAgLy8g5Y+v5Lul6YCJ5oup5YiH5o2i5Yiw5YW25LuW5qCH562+6aG15oiW5pi+56S65oiQ5Yqf5raI5oGvXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3NldHRpbmdzJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi00XCI+5o+Q6YaS6K6+572uPC9oMj5cblxuICAgICAgICAgICAgey8qIOa1i+ivleaPkOmGkuWKn+iDvSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTNcIj7mtYvor5Xmj5DphpLlip/og708L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdGVzdFJlbWluZGVyKDEpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHktMiBweC00IHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOa1i+ivleesrOS4gOe6p+aPkOmGku+8iOaZrumAmu+8iVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdGVzdFJlbWluZGVyKDIpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLW9yYW5nZS02MDAgdGV4dC13aGl0ZSBweS0yIHB4LTQgcm91bmRlZC1sZyBob3ZlcjpiZy1vcmFuZ2UtNzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDmtYvor5XnrKzkuoznuqfmj5DphpLvvIjph43opoHvvIlcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRlc3RSZW1pbmRlcigzKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1yZWQtNjAwIHRleHQtd2hpdGUgcHktMiBweC00IHJvdW5kZWQtbGcgaG92ZXI6YmctcmVkLTcwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg5rWL6K+V56ys5LiJ57qn5o+Q6YaS77yI57Sn5oCl77yJXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgcHQtNlwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+5pu05aSa5o+Q6YaS6K6+572u5Yqf6IO95byA5Y+R5LitLi4uPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOaPkOmGkuW8ueeqlyAqL31cbiAgICAgIHtzaG93UmVtaW5kZXJQb3B1cCAmJiAoXG4gICAgICAgIDxSZW1pbmRlclBvcHVwXG4gICAgICAgICAgbm90aWZpY2F0aW9uPXtzaG93UmVtaW5kZXJQb3B1cH1cbiAgICAgICAgICBvbkNvbmZpcm09e2hhbmRsZVJlbWluZGVyQ29uZmlybX1cbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93UmVtaW5kZXJQb3B1cChudWxsKX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDnoa7orqTlr7nor53moYYgKi99XG4gICAgICB7c2hvd0NvbmZpcm1hdGlvbkRpYWxvZyAmJiAoXG4gICAgICAgIDxDb25maXJtYXRpb25EaWFsb2dcbiAgICAgICAgICBub3RpZmljYXRpb249e3Nob3dDb25maXJtYXRpb25EaWFsb2d9XG4gICAgICAgICAgb25Db25maXJtPXtoYW5kbGVDb25maXJtYXRpb25TdWJtaXR9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0NvbmZpcm1hdGlvbkRpYWxvZyhudWxsKX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDmj5DphpLnrqHnkIblmaggKi99XG4gICAgICA8UmVtaW5kZXJNYW5hZ2VyIC8+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZUF1dGgiLCJ1c2VNZWRpY2F0aW9uIiwiZ2V0Tm90aWZpY2F0aW9uU2VydmljZSIsIlBsdXMiLCJTZXR0aW5ncyIsIkNsb2NrIiwiUGlsbCIsIkJlbGwiLCJCZWxsT2ZmIiwiTWVkaWNpbmVJbnB1dEZvcm0iLCJEYWlseVNjaGVkdWxlRm9ybSIsIk1lZGljaW5lUmVtaW5kZXJMaXN0IiwiUmVtaW5kZXJNYW5hZ2VyIiwiUmVtaW5kZXJQb3B1cCIsIkNvbmZpcm1hdGlvbkRpYWxvZyIsImR5bmFtaWMiLCJNZWRpY2F0aW9uUGFnZSIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInNob3dSZW1pbmRlclBvcHVwIiwic2V0U2hvd1JlbWluZGVyUG9wdXAiLCJzaG93Q29uZmlybWF0aW9uRGlhbG9nIiwic2V0U2hvd0NvbmZpcm1hdGlvbkRpYWxvZyIsInJlbWluZGVyU3lzdGVtQWN0aXZlIiwic2V0UmVtaW5kZXJTeXN0ZW1BY3RpdmUiLCJlZGl0aW5nUmVtaW5kZXIiLCJzZXRFZGl0aW5nUmVtaW5kZXIiLCJtb3VudGVkIiwic2V0TW91bnRlZCIsInVzZXIiLCJpbml0aWFsaXplZCIsImluaXRpYWxpemUiLCJyZW1pbmRlcnMiLCJkYWlseVNjaGVkdWxlIiwicmVtaW5kZXJTZXR0aW5ncyIsInN0YXJ0UmVtaW5kZXJTeXN0ZW0iLCJzdG9wUmVtaW5kZXJTeXN0ZW0iLCJsb2FkRGFpbHlTY2hlZHVsZSIsImxvYWRSZW1pbmRlclNldHRpbmdzIiwicm91dGVyIiwicHVzaCIsImlkIiwiaGFuZGxlUmVtaW5kZXJQb3B1cCIsImV2ZW50IiwiZGV0YWlsIiwiaGFuZGxlQ29uZmlybWF0aW9uRGlhbG9nIiwiaGFuZGxlUmVtaW5kZXJDb25maXJtZWQiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZVN0YXJ0UmVtaW5kZXJTeXN0ZW0iLCJub3RpZmljYXRpb25TZXJ2aWNlIiwiaGFzUGVybWlzc2lvbiIsInJlcXVlc3ROb3RpZmljYXRpb25QZXJtaXNzaW9uIiwiYWxlcnQiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVTdG9wUmVtaW5kZXJTeXN0ZW0iLCJoYW5kbGVSZW1pbmRlckNvbmZpcm0iLCJjb25maXJtZWQiLCJsb2ciLCJtZWRpY2luZU5hbWUiLCJoYW5kbGVDb25maXJtYXRpb25TdWJtaXQiLCJ0ZXN0UmVtaW5kZXIiLCJsZXZlbCIsInRlc3ROb3RpZmljYXRpb24iLCJEYXRlIiwibm93IiwicmVtaW5kZXJJZCIsImRvc2FnZSIsInVzYWdlIiwic2NoZWR1bGVkVGltZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJpc0FjdGl2ZSIsImNyZWF0ZWRBdCIsImV2ZW50TmFtZSIsImRpc3BhdGNoRXZlbnQiLCJDdXN0b21FdmVudCIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJ0YWJzIiwibGFiZWwiLCJpY29uIiwiaDEiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsIm1hcCIsInRhYiIsIkljb25Db21wb25lbnQiLCJ1c2VySWQiLCJvbkVkaXQiLCJyZW1pbmRlciIsIm9uU3VjY2VzcyIsIm9uQ2FuY2VsIiwiaDIiLCJoMyIsInR5cGUiLCJub3RpZmljYXRpb24iLCJvbkNvbmZpcm0iLCJvbkNsb3NlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/medication/page.tsx\n"));

/***/ })

});