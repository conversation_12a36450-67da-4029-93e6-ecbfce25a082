"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/speech-service.ts":
/*!***********************************!*\
  !*** ./src/lib/speech-service.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeechService: () => (/* binding */ SpeechService),\n/* harmony export */   speechService: () => (/* binding */ speechService)\n/* harmony export */ });\nclass SpeechService {\n    /**\n   * 初始化语音识别\n   */ initializeSpeechRecognition() {\n        if (true) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (SpeechRecognition) {\n                this.recognition = new SpeechRecognition();\n                this.recognition.continuous = false;\n                this.recognition.interimResults = true;\n                this.recognition.lang = 'zh-CN';\n                this.recognition.maxAlternatives = 1;\n            }\n        }\n    }\n    /**\n   * 初始化语音合成\n   */ initializeSpeechSynthesis() {\n        if ( true && 'speechSynthesis' in window) {\n            this.synthesis = window.speechSynthesis;\n        }\n    }\n    /**\n   * 检查浏览器是否支持语音识别\n   */ isSpeechRecognitionSupported() {\n        return this.recognition !== null;\n    }\n    /**\n   * 检查浏览器是否支持语音合成\n   */ isSpeechSynthesisSupported() {\n        return this.synthesis !== null;\n    }\n    /**\n   * 开始语音识别\n   */ async startListening(onResult, onError) {\n        if (!this.recognition) {\n            throw new Error('语音识别不支持');\n        }\n        if (this.isListening) {\n            this.stopListening();\n        }\n        return new Promise((resolve, reject)=>{\n            this.recognition.onstart = ()=>{\n                this.isListening = true;\n                resolve();\n            };\n            this.recognition.onresult = (event)=>{\n                const result = event.results[event.results.length - 1];\n                const transcript = result[0].transcript;\n                const confidence = result[0].confidence;\n                const isFinal = result.isFinal;\n                onResult({\n                    transcript: transcript.trim(),\n                    confidence,\n                    isFinal\n                });\n            };\n            this.recognition.onerror = (event)=>{\n                this.isListening = false;\n                const errorMessage = this.getErrorMessage(event.error);\n                onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n                reject(new Error(errorMessage));\n            };\n            this.recognition.onend = ()=>{\n                this.isListening = false;\n            };\n            try {\n                this.recognition.start();\n            } catch (error) {\n                this.isListening = false;\n                reject(error);\n            }\n        });\n    }\n    /**\n   * 停止语音识别\n   */ stopListening() {\n        if (this.recognition && this.isListening) {\n            this.recognition.stop();\n            this.isListening = false;\n        }\n    }\n    /**\n   * 语音播报\n   */ async speak(options) {\n        if (!this.synthesis) {\n            throw new Error('语音合成不支持');\n        }\n        // 停止当前播报\n        this.synthesis.cancel();\n        return new Promise((resolve, reject)=>{\n            const utterance = new SpeechSynthesisUtterance(options.text);\n            utterance.lang = options.lang || 'zh-CN';\n            utterance.rate = options.rate || 1;\n            utterance.pitch = options.pitch || 1;\n            utterance.volume = options.volume || 1;\n            utterance.onend = ()=>resolve();\n            utterance.onerror = (event)=>reject(new Error(\"语音播报失败: \".concat(event.error)));\n            // 获取中文语音\n            const voices = this.synthesis.getVoices();\n            const chineseVoice = voices.find((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n            if (chineseVoice) {\n                utterance.voice = chineseVoice;\n            }\n            this.synthesis.speak(utterance);\n        });\n    }\n    /**\n   * 停止语音播报\n   */ stopSpeaking() {\n        if (this.synthesis) {\n            this.synthesis.cancel();\n        }\n    }\n    /**\n   * 解析药品信息的语音输入\n   */ parseMedicineInput(transcript) {\n        const result = {};\n        // 提取药品名称（通常在开头）\n        const medicineNameMatch = transcript.match(/^(.+?)(?:\\s|，|,)/);\n        if (medicineNameMatch) {\n            result.medicineName = medicineNameMatch[1].trim();\n        }\n        // 提取剂量信息\n        const dosagePatterns = [\n            /(\\d+)\\s*片/,\n            /(\\d+)\\s*粒/,\n            /(\\d+)\\s*毫升/,\n            /(\\d+)\\s*ml/,\n            /(\\d+)\\s*毫克/,\n            /(\\d+)\\s*mg/,\n            /(\\d+)\\s*滴/\n        ];\n        for (const pattern of dosagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                const unit = match[0].replace(match[1], '').trim();\n                result.dosage = \"\".concat(match[1]).concat(unit);\n                break;\n            }\n        }\n        // 提取用法信息\n        const usagePatterns = [\n            /餐前\\s*(\\d+)?\\s*分钟?/,\n            /饭前\\s*(\\d+)?\\s*分钟?/,\n            /餐后\\s*(\\d+)?\\s*分钟?/,\n            /饭后\\s*(\\d+)?\\s*分钟?/,\n            /睡前\\s*(\\d+)?\\s*分钟?/,\n            /晨起/,\n            /起床后/\n        ];\n        for (const pattern of usagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.usage = match[0];\n                break;\n            }\n        }\n        // 提取频次信息\n        const frequencyPatterns = [\n            /每日\\s*(\\d+)\\s*次/,\n            /一日\\s*(\\d+)\\s*次/,\n            /每天\\s*(\\d+)\\s*次/,\n            /(\\d+)\\s*次\\s*每日/,\n            /(\\d+)\\s*次\\s*一日/\n        ];\n        for (const pattern of frequencyPatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.frequency = match[0];\n                break;\n            }\n        }\n        return result;\n    }\n    /**\n   * 生成用药提醒的语音内容\n   */ generateReminderSpeech(medicineName, dosage, usage) {\n        let speech = \"请注意，现在是服用\".concat(medicineName, \"的时间。\");\n        if (dosage) {\n            speech += \"请服用\".concat(dosage, \"。\");\n        }\n        if (usage) {\n            speech += \"用法：\".concat(usage, \"。\");\n        }\n        speech += '请确认是否已经服药。';\n        return speech;\n    }\n    /**\n   * 获取错误信息\n   */ getErrorMessage(error) {\n        switch(error){\n            case 'no-speech':\n                return '没有检测到语音输入';\n            case 'audio-capture':\n                return '无法访问麦克风';\n            case 'not-allowed':\n                return '麦克风权限被拒绝';\n            case 'network':\n                return '网络错误';\n            case 'service-not-allowed':\n                return '语音识别服务不可用';\n            default:\n                return \"语音识别错误: \".concat(error);\n        }\n    }\n    /**\n   * 请求麦克风权限\n   */ async requestMicrophonePermission() {\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            stream.getTracks().forEach((track)=>track.stop());\n            return true;\n        } catch (error) {\n            console.error('麦克风权限请求失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 获取可用的语音列表\n   */ getAvailableVoices() {\n        if (!this.synthesis) return [];\n        return this.synthesis.getVoices().filter((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n    }\n    /**\n   * 测试语音功能\n   */ async testSpeech() {\n        const result = {\n            recognition: this.isSpeechRecognitionSupported(),\n            synthesis: this.isSpeechSynthesisSupported()\n        };\n        if (result.synthesis) {\n            try {\n                await this.speak({\n                    text: '语音功能测试正常'\n                });\n            } catch (error) {\n                result.synthesis = false;\n            }\n        }\n        return result;\n    }\n    constructor(){\n        this.recognition = null;\n        this.synthesis = null;\n        this.isListening = false;\n        this.medicineDatabase = new Map();\n        this.commonMedicines = [];\n        this.initializeSpeechRecognition();\n        this.initializeSpeechSynthesis();\n        this.initializeMedicineDatabase();\n    }\n}\nconst speechService = new SpeechService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/speech-service.ts\n"));

/***/ })

});