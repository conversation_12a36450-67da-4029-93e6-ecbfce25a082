"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx":
/*!*********************************************************!*\
  !*** ./src/components/medication/MedicineInputForm.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MedicineInputForm: () => (/* binding */ MedicineInputForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.mjs\");\n/* harmony import */ var _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n/* harmony import */ var _store_medication__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/medication */ \"(app-pages-browser)/./src/store/medication.ts\");\n/* __next_internal_client_entry_do_not_use__ MedicineInputForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MedicineInputForm(param) {\n    let { userId, onSuccess, onCancel } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        medicineName: '',\n        dosage: '',\n        usage: '',\n        frequency: '',\n        duration: ''\n    });\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [speechSupported, setSpeechSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceTranscript, setVoiceTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [confidence, setConfidence] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addReminder, calculateMedicationTimes, loading, error, clearError } = (0,_store_medication__WEBPACK_IMPORTED_MODULE_3__.useMedication)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicineInputForm.useEffect\": ()=>{\n            setSpeechSupported(_lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.isSpeechRecognitionSupported());\n        }\n    }[\"MedicineInputForm.useEffect\"], []);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const handleVoiceInput = async ()=>{\n        if (!speechSupported) {\n            alert('您的浏览器不支持语音识别功能');\n            return;\n        }\n        if (isListening) {\n            _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.stopListening();\n            setIsListening(false);\n            return;\n        }\n        try {\n            // 请求麦克风权限\n            const hasPermission = await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.requestMicrophonePermission();\n            if (!hasPermission) {\n                alert('需要麦克风权限才能使用语音输入功能');\n                return;\n            }\n            setIsListening(true);\n            setVoiceTranscript('');\n            await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.startListening((result)=>{\n                setVoiceTranscript(result.transcript);\n                if (result.isFinal) {\n                    // 解析语音输入（增强版）\n                    const parsed = _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.parseMedicineInput(result.transcript);\n                    // 设置置信度和建议\n                    setConfidence(parsed.confidence || 0);\n                    setSuggestions(parsed.suggestions || []);\n                    setShowSuggestions((parsed.confidence || 0) < 0.8 && (parsed.suggestions || []).length > 0);\n                    setFormData((prev)=>({\n                            ...prev,\n                            medicineName: parsed.medicineName || prev.medicineName,\n                            dosage: parsed.dosage || prev.dosage,\n                            usage: parsed.usage || prev.usage,\n                            frequency: parsed.frequency || prev.frequency\n                        }));\n                    setIsListening(false);\n                }\n            }, (error)=>{\n                console.error('语音识别错误:', error);\n                setIsListening(false);\n                alert(\"语音识别失败: \".concat(error));\n            });\n        } catch (error) {\n            console.error('启动语音识别失败:', error);\n            setIsListening(false);\n            alert('启动语音识别失败，请检查麦克风权限');\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.medicineName.trim()) {\n            newErrors.medicineName = '请输入药品名称';\n        }\n        if (!formData.dosage.trim()) {\n            newErrors.dosage = '请输入用药剂量';\n        }\n        if (!formData.usage.trim()) {\n            newErrors.usage = '请输入用药规则';\n        }\n        if (!formData.frequency.trim()) {\n            newErrors.frequency = '请输入用药频次';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        try {\n            clearError();\n            // 计算用药时间\n            const scheduledTimes = calculateMedicationTimes(formData.usage, formData.frequency);\n            await addReminder({\n                medicineName: formData.medicineName.trim(),\n                dosage: formData.dosage.trim(),\n                usage: formData.usage.trim(),\n                frequency: formData.frequency.trim(),\n                duration: formData.duration ? parseInt(formData.duration) : undefined,\n                scheduledTimes,\n                isEnabled: true,\n                userId\n            });\n            // 重置表单\n            setFormData({\n                medicineName: '',\n                dosage: '',\n                usage: '',\n                frequency: '',\n                duration: ''\n            });\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            console.error('添加用药提醒失败:', error);\n        }\n    };\n    const usageOptions = [\n        '餐前30分钟',\n        '餐后1小时',\n        '随餐服用',\n        '睡前30分钟',\n        '晨起服用',\n        '空腹服用'\n    ];\n    const frequencyOptions = [\n        '每日1次',\n        '每日2次',\n        '每日3次',\n        '每日4次',\n        '每周2次',\n        '每周3次'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800\",\n                        children: \"添加用药提醒\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    speechSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleVoiceInput,\n                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors \".concat(isListening ? 'bg-red-500 text-white' : 'bg-blue-500 text-white hover:bg-blue-600'),\n                        children: [\n                            isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 66\n                            }, this),\n                            isListening ? '停止录音' : '语音输入'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            voiceTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"语音识别结果：\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            voiceTranscript\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    confidence > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-blue-600 mr-2\",\n                                children: \"识别置信度:\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-blue-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: \"\".concat(confidence * 100, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-blue-600 ml-2\",\n                                children: [\n                                    (confidence * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            showSuggestions && suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-yellow-800 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"\\uD83E\\uDD14 识别置信度较低，您是否想要：\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    setFormData((prev)=>({\n                                            ...prev,\n                                            medicineName: suggestion\n                                        }));\n                                    setShowSuggestions(false);\n                                },\n                                className: \"block w-full text-left px-3 py-2 text-sm bg-white border border-yellow-300 rounded hover:bg-yellow-100 transition-colors\",\n                                children: suggestion\n                            }, index, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setShowSuggestions(false),\n                        className: \"mt-2 text-xs text-yellow-600 hover:text-yellow-800\",\n                        children: \"关闭建议\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"药品名称 *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.medicineName,\n                                onChange: (e)=>handleInputChange('medicineName', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.medicineName ? 'border-red-300' : 'border-gray-300'),\n                                placeholder: \"例如：阿司匹林肠溶片\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            errors.medicineName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.medicineName\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"用药剂量 *\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.dosage,\n                                onChange: (e)=>handleInputChange('dosage', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.dosage ? 'border-red-300' : 'border-gray-300'),\n                                placeholder: \"例如：1片、2ml、100mg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            errors.dosage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.dosage\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"用药规则 *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.usage,\n                                onChange: (e)=>handleInputChange('usage', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.usage ? 'border-red-300' : 'border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"请选择用药规则\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    usageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.usage,\n                                onChange: (e)=>handleInputChange('usage', e.target.value),\n                                className: \"w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"或自定义用药规则\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            errors.usage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.usage\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"用药频次 *\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.frequency,\n                                onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.frequency ? 'border-red-300' : 'border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"请选择用药频次\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    frequencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.frequency,\n                                onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                className: \"w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"或自定义用药频次\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            errors.frequency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.frequency\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"疗程时长（可选）\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                value: formData.duration,\n                                onChange: (e)=>handleInputChange('duration', e.target.value),\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"请输入疗程天数\",\n                                min: \"1\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"添加提醒\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            speechSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-800 mb-2\",\n                        children: \"语音输入提示：\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: '您可以说：\"阿司匹林肠溶片，每次1片，餐后1小时服用，每日3次\"'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicineInputForm, \"QEmjyBvFEGpQ1/QEMGqYO17MO+Y=\", false, function() {\n    return [\n        _store_medication__WEBPACK_IMPORTED_MODULE_3__.useMedication\n    ];\n});\n_c = MedicineInputForm;\nvar _c;\n$RefreshReg$(_c, \"MedicineInputForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21lZGljYXRpb24vTWVkaWNpbmVJbnB1dEZvcm0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQzRCO0FBQ25CO0FBQ0Y7QUFRM0MsU0FBU1Usa0JBQWtCLEtBQXVEO1FBQXZELEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxRQUFRLEVBQTBCLEdBQXZEOztJQUNoQyxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2YsK0NBQVFBLENBQUM7UUFDdkNnQixjQUFjO1FBQ2RDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxXQUFXO1FBQ1hDLFVBQVU7SUFDWjtJQUVBLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDdUIsaUJBQWlCQyxtQkFBbUIsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3lCLGlCQUFpQkMsbUJBQW1CLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMyQixRQUFRQyxVQUFVLEdBQUc1QiwrQ0FBUUEsQ0FBeUIsQ0FBQztJQUM5RCxNQUFNLENBQUM2QixhQUFhQyxlQUFlLEdBQUc5QiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQzNELE1BQU0sQ0FBQytCLFlBQVlDLGNBQWMsR0FBR2hDLCtDQUFRQSxDQUFTO0lBQ3JELE1BQU0sQ0FBQ2lDLGlCQUFpQkMsbUJBQW1CLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUV2RCxNQUFNLEVBQUVtQyxXQUFXLEVBQUVDLHdCQUF3QixFQUFFQyxPQUFPLEVBQUVDLEtBQUssRUFBRUMsVUFBVSxFQUFFLEdBQUc5QixnRUFBYUE7SUFFM0ZSLGdEQUFTQTt1Q0FBQztZQUNSdUIsbUJBQW1CaEIsOERBQWFBLENBQUNnQyw0QkFBNEI7UUFDL0Q7c0NBQUcsRUFBRTtJQUVMLE1BQU1DLG9CQUFvQixDQUFDQyxPQUFlQztRQUN4QzVCLFlBQVk2QixDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ0YsTUFBTSxFQUFFQztZQUFNO1FBQy9DLElBQUloQixNQUFNLENBQUNlLE1BQU0sRUFBRTtZQUNqQmQsVUFBVWdCLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDRixNQUFNLEVBQUU7Z0JBQUc7UUFDNUM7SUFDRjtJQUVBLE1BQU1HLG1CQUFtQjtRQUN2QixJQUFJLENBQUN0QixpQkFBaUI7WUFDcEJ1QixNQUFNO1lBQ047UUFDRjtRQUVBLElBQUl6QixhQUFhO1lBQ2ZiLDhEQUFhQSxDQUFDdUMsYUFBYTtZQUMzQnpCLGVBQWU7WUFDZjtRQUNGO1FBRUEsSUFBSTtZQUNGLFVBQVU7WUFDVixNQUFNMEIsZ0JBQWdCLE1BQU14Qyw4REFBYUEsQ0FBQ3lDLDJCQUEyQjtZQUNyRSxJQUFJLENBQUNELGVBQWU7Z0JBQ2xCRixNQUFNO2dCQUNOO1lBQ0Y7WUFFQXhCLGVBQWU7WUFDZkksbUJBQW1CO1lBRW5CLE1BQU1sQiw4REFBYUEsQ0FBQzBDLGNBQWMsQ0FDaEMsQ0FBQ0M7Z0JBQ0N6QixtQkFBbUJ5QixPQUFPQyxVQUFVO2dCQUVwQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7b0JBQ2xCLGNBQWM7b0JBQ2QsTUFBTUMsU0FBUzlDLDhEQUFhQSxDQUFDK0Msa0JBQWtCLENBQUNKLE9BQU9DLFVBQVU7b0JBRWpFLFdBQVc7b0JBQ1hwQixjQUFjc0IsT0FBT3ZCLFVBQVUsSUFBSTtvQkFDbkNELGVBQWV3QixPQUFPekIsV0FBVyxJQUFJLEVBQUU7b0JBQ3ZDSyxtQkFBbUIsQ0FBQ29CLE9BQU92QixVQUFVLElBQUksS0FBSyxPQUFPLENBQUN1QixPQUFPekIsV0FBVyxJQUFJLEVBQUUsRUFBRTJCLE1BQU0sR0FBRztvQkFFekZ6QyxZQUFZNkIsQ0FBQUEsT0FBUzs0QkFDbkIsR0FBR0EsSUFBSTs0QkFDUDVCLGNBQWNzQyxPQUFPdEMsWUFBWSxJQUFJNEIsS0FBSzVCLFlBQVk7NEJBQ3REQyxRQUFRcUMsT0FBT3JDLE1BQU0sSUFBSTJCLEtBQUszQixNQUFNOzRCQUNwQ0MsT0FBT29DLE9BQU9wQyxLQUFLLElBQUkwQixLQUFLMUIsS0FBSzs0QkFDakNDLFdBQVdtQyxPQUFPbkMsU0FBUyxJQUFJeUIsS0FBS3pCLFNBQVM7d0JBQy9DO29CQUVBRyxlQUFlO2dCQUNqQjtZQUNGLEdBQ0EsQ0FBQ2dCO2dCQUNDbUIsUUFBUW5CLEtBQUssQ0FBQyxXQUFXQTtnQkFDekJoQixlQUFlO2dCQUNmd0IsTUFBTSxXQUFpQixPQUFOUjtZQUNuQjtRQUVKLEVBQUUsT0FBT0EsT0FBTztZQUNkbUIsUUFBUW5CLEtBQUssQ0FBQyxhQUFhQTtZQUMzQmhCLGVBQWU7WUFDZndCLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTVksZUFBZTtRQUNuQixNQUFNQyxZQUFvQyxDQUFDO1FBRTNDLElBQUksQ0FBQzdDLFNBQVNFLFlBQVksQ0FBQzRDLElBQUksSUFBSTtZQUNqQ0QsVUFBVTNDLFlBQVksR0FBRztRQUMzQjtRQUVBLElBQUksQ0FBQ0YsU0FBU0csTUFBTSxDQUFDMkMsSUFBSSxJQUFJO1lBQzNCRCxVQUFVMUMsTUFBTSxHQUFHO1FBQ3JCO1FBRUEsSUFBSSxDQUFDSCxTQUFTSSxLQUFLLENBQUMwQyxJQUFJLElBQUk7WUFDMUJELFVBQVV6QyxLQUFLLEdBQUc7UUFDcEI7UUFFQSxJQUFJLENBQUNKLFNBQVNLLFNBQVMsQ0FBQ3lDLElBQUksSUFBSTtZQUM5QkQsVUFBVXhDLFNBQVMsR0FBRztRQUN4QjtRQUVBUyxVQUFVK0I7UUFDVixPQUFPRSxPQUFPQyxJQUFJLENBQUNILFdBQVdILE1BQU0sS0FBSztJQUMzQztJQUVBLE1BQU1PLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDUCxnQkFBZ0I7UUFFckIsSUFBSTtZQUNGbkI7WUFFQSxTQUFTO1lBQ1QsTUFBTTJCLGlCQUFpQjlCLHlCQUF5QnRCLFNBQVNJLEtBQUssRUFBRUosU0FBU0ssU0FBUztZQUVsRixNQUFNZ0IsWUFBWTtnQkFDaEJuQixjQUFjRixTQUFTRSxZQUFZLENBQUM0QyxJQUFJO2dCQUN4QzNDLFFBQVFILFNBQVNHLE1BQU0sQ0FBQzJDLElBQUk7Z0JBQzVCMUMsT0FBT0osU0FBU0ksS0FBSyxDQUFDMEMsSUFBSTtnQkFDMUJ6QyxXQUFXTCxTQUFTSyxTQUFTLENBQUN5QyxJQUFJO2dCQUNsQ3hDLFVBQVVOLFNBQVNNLFFBQVEsR0FBRytDLFNBQVNyRCxTQUFTTSxRQUFRLElBQUlnRDtnQkFDNURGO2dCQUNBRyxXQUFXO2dCQUNYMUQ7WUFDRjtZQUVBLE9BQU87WUFDUEksWUFBWTtnQkFDVkMsY0FBYztnQkFDZEMsUUFBUTtnQkFDUkMsT0FBTztnQkFDUEMsV0FBVztnQkFDWEMsVUFBVTtZQUNaO1lBRUFSLHNCQUFBQSxnQ0FBQUE7UUFDRixFQUFFLE9BQU8wQixPQUFPO1lBQ2RtQixRQUFRbkIsS0FBSyxDQUFDLGFBQWFBO1FBQzdCO0lBQ0Y7SUFFQSxNQUFNZ0MsZUFBZTtRQUNuQjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELE1BQU1DLG1CQUFtQjtRQUN2QjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBc0M7Ozs7OztvQkFDbkRsRCxpQ0FDQyw4REFBQ29EO3dCQUNDQyxNQUFLO3dCQUNMQyxTQUFTaEM7d0JBQ1Q0QixXQUFXLDREQUlWLE9BSENwRCxjQUNJLDBCQUNBOzs0QkFHTEEsNEJBQWMsOERBQUNsQiwrR0FBTUE7Z0NBQUNzRSxXQUFVOzs7OztxREFBb0IsOERBQUN2RSwrR0FBR0E7Z0NBQUN1RSxXQUFVOzs7Ozs7NEJBQ25FcEQsY0FBYyxTQUFTOzs7Ozs7Ozs7Ozs7O1lBSzdCSSxpQ0FDQyw4REFBQytDO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0s7d0JBQUVMLFdBQVU7OzBDQUNYLDhEQUFDTTswQ0FBTzs7Ozs7OzRCQUFpQnREOzs7Ozs7O29CQUUxQk0sYUFBYSxtQkFDWiw4REFBQ3lDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ087Z0NBQUtQLFdBQVU7MENBQTZCOzs7Ozs7MENBQzdDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQ0NDLFdBQVU7b0NBQ1ZRLE9BQU87d0NBQUVDLE9BQU8sR0FBb0IsT0FBakJuRCxhQUFhLEtBQUk7b0NBQUc7Ozs7Ozs7Ozs7OzBDQUczQyw4REFBQ2lEO2dDQUFLUCxXQUFVOztvQ0FBK0IxQyxDQUFBQSxhQUFhLEdBQUUsRUFBR29ELE9BQU8sQ0FBQztvQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9uRmxELG1CQUFtQkosWUFBWTJCLE1BQU0sR0FBRyxtQkFDdkMsOERBQUNnQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNLO3dCQUFFTCxXQUFVO2tDQUNYLDRFQUFDTTtzQ0FBTzs7Ozs7Ozs7Ozs7a0NBRVYsOERBQUNQO3dCQUFJQyxXQUFVO2tDQUNaNUMsWUFBWXVELEdBQUcsQ0FBQyxDQUFDQyxZQUFZQyxzQkFDNUIsOERBQUNYO2dDQUVDQyxNQUFLO2dDQUNMQyxTQUFTO29DQUNQOUQsWUFBWTZCLENBQUFBLE9BQVM7NENBQUUsR0FBR0EsSUFBSTs0Q0FBRTVCLGNBQWNxRTt3Q0FBVztvQ0FDekRuRCxtQkFBbUI7Z0NBQ3JCO2dDQUNBdUMsV0FBVTswQ0FFVFk7K0JBUklDOzs7Ozs7Ozs7O2tDQVlYLDhEQUFDWDt3QkFDQ0MsTUFBSzt3QkFDTEMsU0FBUyxJQUFNM0MsbUJBQW1CO3dCQUNsQ3VDLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7OztZQU1KbkMsdUJBQ0MsOERBQUNrQztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0s7b0JBQUVMLFdBQVU7OEJBQXdCbkM7Ozs7Ozs7Ozs7OzBCQUl6Qyw4REFBQ2lEO2dCQUFLQyxVQUFVekI7Z0JBQWNVLFdBQVU7O2tDQUV0Qyw4REFBQ0Q7OzBDQUNDLDhEQUFDaUI7Z0NBQU1oQixXQUFVOztrREFDZiw4REFBQ25FLCtHQUFJQTt3Q0FBQ21FLFdBQVU7Ozs7OztvQ0FBd0I7Ozs7Ozs7MENBRzFDLDhEQUFDaUI7Z0NBQ0NkLE1BQUs7Z0NBQ0xqQyxPQUFPN0IsU0FBU0UsWUFBWTtnQ0FDNUIyRSxVQUFVLENBQUMzQixJQUFNdkIsa0JBQWtCLGdCQUFnQnVCLEVBQUU0QixNQUFNLENBQUNqRCxLQUFLO2dDQUNqRThCLFdBQVcsZ0dBRVYsT0FEQzlDLE9BQU9YLFlBQVksR0FBRyxtQkFBbUI7Z0NBRTNDNkUsYUFBWTs7Ozs7OzRCQUVibEUsT0FBT1gsWUFBWSxrQkFDbEIsOERBQUM4RDtnQ0FBRUwsV0FBVTswQ0FBNkI5QyxPQUFPWCxZQUFZOzs7Ozs7Ozs7Ozs7a0NBS2pFLDhEQUFDd0Q7OzBDQUNDLDhEQUFDaUI7Z0NBQU1oQixXQUFVOzBDQUErQzs7Ozs7OzBDQUdoRSw4REFBQ2lCO2dDQUNDZCxNQUFLO2dDQUNMakMsT0FBTzdCLFNBQVNHLE1BQU07Z0NBQ3RCMEUsVUFBVSxDQUFDM0IsSUFBTXZCLGtCQUFrQixVQUFVdUIsRUFBRTRCLE1BQU0sQ0FBQ2pELEtBQUs7Z0NBQzNEOEIsV0FBVyxnR0FFVixPQURDOUMsT0FBT1YsTUFBTSxHQUFHLG1CQUFtQjtnQ0FFckM0RSxhQUFZOzs7Ozs7NEJBRWJsRSxPQUFPVixNQUFNLGtCQUNaLDhEQUFDNkQ7Z0NBQUVMLFdBQVU7MENBQTZCOUMsT0FBT1YsTUFBTTs7Ozs7Ozs7Ozs7O2tDQUszRCw4REFBQ3VEOzswQ0FDQyw4REFBQ2lCO2dDQUFNaEIsV0FBVTs7a0RBQ2YsOERBQUNwRSwrR0FBS0E7d0NBQUNvRSxXQUFVOzs7Ozs7b0NBQXdCOzs7Ozs7OzBDQUczQyw4REFBQ3FCO2dDQUNDbkQsT0FBTzdCLFNBQVNJLEtBQUs7Z0NBQ3JCeUUsVUFBVSxDQUFDM0IsSUFBTXZCLGtCQUFrQixTQUFTdUIsRUFBRTRCLE1BQU0sQ0FBQ2pELEtBQUs7Z0NBQzFEOEIsV0FBVyxnR0FFVixPQURDOUMsT0FBT1QsS0FBSyxHQUFHLG1CQUFtQjs7a0RBR3BDLDhEQUFDNkU7d0NBQU9wRCxPQUFNO2tEQUFHOzs7Ozs7b0NBQ2hCMkIsYUFBYWMsR0FBRyxDQUFDVyxDQUFBQSx1QkFDaEIsOERBQUNBOzRDQUFvQnBELE9BQU9vRDtzREFBU0E7MkNBQXhCQTs7Ozs7Ozs7Ozs7MENBR2pCLDhEQUFDTDtnQ0FDQ2QsTUFBSztnQ0FDTGpDLE9BQU83QixTQUFTSSxLQUFLO2dDQUNyQnlFLFVBQVUsQ0FBQzNCLElBQU12QixrQkFBa0IsU0FBU3VCLEVBQUU0QixNQUFNLENBQUNqRCxLQUFLO2dDQUMxRDhCLFdBQVU7Z0NBQ1ZvQixhQUFZOzs7Ozs7NEJBRWJsRSxPQUFPVCxLQUFLLGtCQUNYLDhEQUFDNEQ7Z0NBQUVMLFdBQVU7MENBQTZCOUMsT0FBT1QsS0FBSzs7Ozs7Ozs7Ozs7O2tDQUsxRCw4REFBQ3NEOzswQ0FDQyw4REFBQ2lCO2dDQUFNaEIsV0FBVTswQ0FBK0M7Ozs7OzswQ0FHaEUsOERBQUNxQjtnQ0FDQ25ELE9BQU83QixTQUFTSyxTQUFTO2dDQUN6QndFLFVBQVUsQ0FBQzNCLElBQU12QixrQkFBa0IsYUFBYXVCLEVBQUU0QixNQUFNLENBQUNqRCxLQUFLO2dDQUM5RDhCLFdBQVcsZ0dBRVYsT0FEQzlDLE9BQU9SLFNBQVMsR0FBRyxtQkFBbUI7O2tEQUd4Qyw4REFBQzRFO3dDQUFPcEQsT0FBTTtrREFBRzs7Ozs7O29DQUNoQjRCLGlCQUFpQmEsR0FBRyxDQUFDVyxDQUFBQSx1QkFDcEIsOERBQUNBOzRDQUFvQnBELE9BQU9vRDtzREFBU0E7MkNBQXhCQTs7Ozs7Ozs7Ozs7MENBR2pCLDhEQUFDTDtnQ0FDQ2QsTUFBSztnQ0FDTGpDLE9BQU83QixTQUFTSyxTQUFTO2dDQUN6QndFLFVBQVUsQ0FBQzNCLElBQU12QixrQkFBa0IsYUFBYXVCLEVBQUU0QixNQUFNLENBQUNqRCxLQUFLO2dDQUM5RDhCLFdBQVU7Z0NBQ1ZvQixhQUFZOzs7Ozs7NEJBRWJsRSxPQUFPUixTQUFTLGtCQUNmLDhEQUFDMkQ7Z0NBQUVMLFdBQVU7MENBQTZCOUMsT0FBT1IsU0FBUzs7Ozs7Ozs7Ozs7O2tDQUs5RCw4REFBQ3FEOzswQ0FDQyw4REFBQ2lCO2dDQUFNaEIsV0FBVTs7a0RBQ2YsOERBQUNsRSwrR0FBUUE7d0NBQUNrRSxXQUFVOzs7Ozs7b0NBQXdCOzs7Ozs7OzBDQUc5Qyw4REFBQ2lCO2dDQUNDZCxNQUFLO2dDQUNMakMsT0FBTzdCLFNBQVNNLFFBQVE7Z0NBQ3hCdUUsVUFBVSxDQUFDM0IsSUFBTXZCLGtCQUFrQixZQUFZdUIsRUFBRTRCLE1BQU0sQ0FBQ2pELEtBQUs7Z0NBQzdEOEIsV0FBVTtnQ0FDVm9CLGFBQVk7Z0NBQ1pHLEtBQUk7Ozs7Ozs7Ozs7OztrQ0FLUiw4REFBQ3hCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQ0NDLE1BQUs7Z0NBQ0xxQixVQUFVNUQ7Z0NBQ1ZvQyxXQUFVOzBDQUVUcEMsd0JBQ0MsOERBQUNtQztvQ0FBSUMsV0FBVTs7Ozs7eURBRWY7O3NEQUNFLDhEQUFDckUsK0dBQUlBOzRDQUFDcUUsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs7NEJBTXRDNUQsMEJBQ0MsOERBQUM4RDtnQ0FDQ0MsTUFBSztnQ0FDTEMsU0FBU2hFO2dDQUNUNEQsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUU5sRCxpQ0FDQyw4REFBQ2lEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3lCO3dCQUFHekIsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNLO3dCQUFFTCxXQUFVO2tDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTy9DO0dBaFpnQi9EOztRQWlCZ0VELDREQUFhQTs7O0tBakI3RUMiLCJzb3VyY2VzIjpbIkU6XFxjYXJlYmFvXFxjYXJlYXBwLXdlYlxcc3JjXFxjb21wb25lbnRzXFxtZWRpY2F0aW9uXFxNZWRpY2luZUlucHV0Rm9ybS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IE1pYywgTWljT2ZmLCBQbHVzLCBDbG9jaywgUGlsbCwgRmlsZVRleHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBzcGVlY2hTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc3BlZWNoLXNlcnZpY2UnXG5pbXBvcnQgeyB1c2VNZWRpY2F0aW9uIH0gZnJvbSAnQC9zdG9yZS9tZWRpY2F0aW9uJ1xuXG5pbnRlcmZhY2UgTWVkaWNpbmVJbnB1dEZvcm1Qcm9wcyB7XG4gIHVzZXJJZDogc3RyaW5nXG4gIG9uU3VjY2Vzcz86ICgpID0+IHZvaWRcbiAgb25DYW5jZWw/OiAoKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBNZWRpY2luZUlucHV0Rm9ybSh7IHVzZXJJZCwgb25TdWNjZXNzLCBvbkNhbmNlbCB9OiBNZWRpY2luZUlucHV0Rm9ybVByb3BzKSB7XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIG1lZGljaW5lTmFtZTogJycsXG4gICAgZG9zYWdlOiAnJyxcbiAgICB1c2FnZTogJycsXG4gICAgZnJlcXVlbmN5OiAnJyxcbiAgICBkdXJhdGlvbjogJydcbiAgfSlcbiAgXG4gIGNvbnN0IFtpc0xpc3RlbmluZywgc2V0SXNMaXN0ZW5pbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzcGVlY2hTdXBwb3J0ZWQsIHNldFNwZWVjaFN1cHBvcnRlZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3ZvaWNlVHJhbnNjcmlwdCwgc2V0Vm9pY2VUcmFuc2NyaXB0XSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbZXJyb3JzLCBzZXRFcnJvcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pXG4gIGNvbnN0IFtzdWdnZXN0aW9ucywgc2V0U3VnZ2VzdGlvbnNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKVxuICBjb25zdCBbY29uZmlkZW5jZSwgc2V0Q29uZmlkZW5jZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApXG4gIGNvbnN0IFtzaG93U3VnZ2VzdGlvbnMsIHNldFNob3dTdWdnZXN0aW9uc10gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCB7IGFkZFJlbWluZGVyLCBjYWxjdWxhdGVNZWRpY2F0aW9uVGltZXMsIGxvYWRpbmcsIGVycm9yLCBjbGVhckVycm9yIH0gPSB1c2VNZWRpY2F0aW9uKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldFNwZWVjaFN1cHBvcnRlZChzcGVlY2hTZXJ2aWNlLmlzU3BlZWNoUmVjb2duaXRpb25TdXBwb3J0ZWQoKSlcbiAgfSwgW10pXG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZmllbGQ6IHN0cmluZywgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogdmFsdWUgfSkpXG4gICAgaWYgKGVycm9yc1tmaWVsZF0pIHtcbiAgICAgIHNldEVycm9ycyhwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06ICcnIH0pKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVZvaWNlSW5wdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzcGVlY2hTdXBwb3J0ZWQpIHtcbiAgICAgIGFsZXJ0KCfmgqjnmoTmtY/op4jlmajkuI3mlK/mjIHor63pn7Por4bliKvlip/og70nKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKGlzTGlzdGVuaW5nKSB7XG4gICAgICBzcGVlY2hTZXJ2aWNlLnN0b3BMaXN0ZW5pbmcoKVxuICAgICAgc2V0SXNMaXN0ZW5pbmcoZmFsc2UpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8g6K+35rGC6bqm5YWL6aOO5p2D6ZmQXG4gICAgICBjb25zdCBoYXNQZXJtaXNzaW9uID0gYXdhaXQgc3BlZWNoU2VydmljZS5yZXF1ZXN0TWljcm9waG9uZVBlcm1pc3Npb24oKVxuICAgICAgaWYgKCFoYXNQZXJtaXNzaW9uKSB7XG4gICAgICAgIGFsZXJ0KCfpnIDopoHpuqblhYvpo47mnYPpmZDmiY3og73kvb/nlKjor63pn7PovpPlhaXlip/og70nKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgc2V0SXNMaXN0ZW5pbmcodHJ1ZSlcbiAgICAgIHNldFZvaWNlVHJhbnNjcmlwdCgnJylcblxuICAgICAgYXdhaXQgc3BlZWNoU2VydmljZS5zdGFydExpc3RlbmluZyhcbiAgICAgICAgKHJlc3VsdCkgPT4ge1xuICAgICAgICAgIHNldFZvaWNlVHJhbnNjcmlwdChyZXN1bHQudHJhbnNjcmlwdClcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAocmVzdWx0LmlzRmluYWwpIHtcbiAgICAgICAgICAgIC8vIOino+aekOivremfs+i+k+WFpe+8iOWinuW8uueJiO+8iVxuICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gc3BlZWNoU2VydmljZS5wYXJzZU1lZGljaW5lSW5wdXQocmVzdWx0LnRyYW5zY3JpcHQpXG5cbiAgICAgICAgICAgIC8vIOiuvue9rue9ruS/oeW6puWSjOW7uuiurlxuICAgICAgICAgICAgc2V0Q29uZmlkZW5jZShwYXJzZWQuY29uZmlkZW5jZSB8fCAwKVxuICAgICAgICAgICAgc2V0U3VnZ2VzdGlvbnMocGFyc2VkLnN1Z2dlc3Rpb25zIHx8IFtdKVxuICAgICAgICAgICAgc2V0U2hvd1N1Z2dlc3Rpb25zKChwYXJzZWQuY29uZmlkZW5jZSB8fCAwKSA8IDAuOCAmJiAocGFyc2VkLnN1Z2dlc3Rpb25zIHx8IFtdKS5sZW5ndGggPiAwKVxuXG4gICAgICAgICAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgIG1lZGljaW5lTmFtZTogcGFyc2VkLm1lZGljaW5lTmFtZSB8fCBwcmV2Lm1lZGljaW5lTmFtZSxcbiAgICAgICAgICAgICAgZG9zYWdlOiBwYXJzZWQuZG9zYWdlIHx8IHByZXYuZG9zYWdlLFxuICAgICAgICAgICAgICB1c2FnZTogcGFyc2VkLnVzYWdlIHx8IHByZXYudXNhZ2UsXG4gICAgICAgICAgICAgIGZyZXF1ZW5jeTogcGFyc2VkLmZyZXF1ZW5jeSB8fCBwcmV2LmZyZXF1ZW5jeVxuICAgICAgICAgICAgfSkpXG5cbiAgICAgICAgICAgIHNldElzTGlzdGVuaW5nKGZhbHNlKVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgKGVycm9yKSA9PiB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcign6K+t6Z+z6K+G5Yir6ZSZ6K+vOicsIGVycm9yKVxuICAgICAgICAgIHNldElzTGlzdGVuaW5nKGZhbHNlKVxuICAgICAgICAgIGFsZXJ0KGDor63pn7Por4bliKvlpLHotKU6ICR7ZXJyb3J9YClcbiAgICAgICAgfVxuICAgICAgKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCflkK/liqjor63pn7Por4bliKvlpLHotKU6JywgZXJyb3IpXG4gICAgICBzZXRJc0xpc3RlbmluZyhmYWxzZSlcbiAgICAgIGFsZXJ0KCflkK/liqjor63pn7Por4bliKvlpLHotKXvvIzor7fmo4Dmn6XpuqblhYvpo47mnYPpmZAnKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHZhbGlkYXRlRm9ybSA9ICgpOiBib29sZWFuID0+IHtcbiAgICBjb25zdCBuZXdFcnJvcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7fVxuXG4gICAgaWYgKCFmb3JtRGF0YS5tZWRpY2luZU5hbWUudHJpbSgpKSB7XG4gICAgICBuZXdFcnJvcnMubWVkaWNpbmVOYW1lID0gJ+ivt+i+k+WFpeiNr+WTgeWQjeensCdcbiAgICB9XG5cbiAgICBpZiAoIWZvcm1EYXRhLmRvc2FnZS50cmltKCkpIHtcbiAgICAgIG5ld0Vycm9ycy5kb3NhZ2UgPSAn6K+36L6T5YWl55So6I2v5YmC6YePJ1xuICAgIH1cblxuICAgIGlmICghZm9ybURhdGEudXNhZ2UudHJpbSgpKSB7XG4gICAgICBuZXdFcnJvcnMudXNhZ2UgPSAn6K+36L6T5YWl55So6I2v6KeE5YiZJ1xuICAgIH1cblxuICAgIGlmICghZm9ybURhdGEuZnJlcXVlbmN5LnRyaW0oKSkge1xuICAgICAgbmV3RXJyb3JzLmZyZXF1ZW5jeSA9ICfor7fovpPlhaXnlKjoja/popHmrKEnXG4gICAgfVxuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycylcbiAgICByZXR1cm4gT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPT09IDBcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBcbiAgICBpZiAoIXZhbGlkYXRlRm9ybSgpKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBjbGVhckVycm9yKClcbiAgICAgIFxuICAgICAgLy8g6K6h566X55So6I2v5pe26Ze0XG4gICAgICBjb25zdCBzY2hlZHVsZWRUaW1lcyA9IGNhbGN1bGF0ZU1lZGljYXRpb25UaW1lcyhmb3JtRGF0YS51c2FnZSwgZm9ybURhdGEuZnJlcXVlbmN5KVxuICAgICAgXG4gICAgICBhd2FpdCBhZGRSZW1pbmRlcih7XG4gICAgICAgIG1lZGljaW5lTmFtZTogZm9ybURhdGEubWVkaWNpbmVOYW1lLnRyaW0oKSxcbiAgICAgICAgZG9zYWdlOiBmb3JtRGF0YS5kb3NhZ2UudHJpbSgpLFxuICAgICAgICB1c2FnZTogZm9ybURhdGEudXNhZ2UudHJpbSgpLFxuICAgICAgICBmcmVxdWVuY3k6IGZvcm1EYXRhLmZyZXF1ZW5jeS50cmltKCksXG4gICAgICAgIGR1cmF0aW9uOiBmb3JtRGF0YS5kdXJhdGlvbiA/IHBhcnNlSW50KGZvcm1EYXRhLmR1cmF0aW9uKSA6IHVuZGVmaW5lZCxcbiAgICAgICAgc2NoZWR1bGVkVGltZXMsXG4gICAgICAgIGlzRW5hYmxlZDogdHJ1ZSxcbiAgICAgICAgdXNlcklkXG4gICAgICB9KVxuXG4gICAgICAvLyDph43nva7ooajljZVcbiAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgbWVkaWNpbmVOYW1lOiAnJyxcbiAgICAgICAgZG9zYWdlOiAnJyxcbiAgICAgICAgdXNhZ2U6ICcnLFxuICAgICAgICBmcmVxdWVuY3k6ICcnLFxuICAgICAgICBkdXJhdGlvbjogJydcbiAgICAgIH0pXG5cbiAgICAgIG9uU3VjY2Vzcz8uKClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5re75Yqg55So6I2v5o+Q6YaS5aSx6LSlOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHVzYWdlT3B0aW9ucyA9IFtcbiAgICAn6aSQ5YmNMzDliIbpkp8nLFxuICAgICfppJDlkI4x5bCP5pe2JyxcbiAgICAn6ZqP6aSQ5pyN55SoJyxcbiAgICAn552h5YmNMzDliIbpkp8nLFxuICAgICfmmajotbfmnI3nlKgnLFxuICAgICfnqbrohbnmnI3nlKgnXG4gIF1cblxuICBjb25zdCBmcmVxdWVuY3lPcHRpb25zID0gW1xuICAgICfmr4/ml6Ux5qyhJyxcbiAgICAn5q+P5pelMuasoScsXG4gICAgJ+avj+aXpTPmrKEnLFxuICAgICfmr4/ml6U05qyhJyxcbiAgICAn5q+P5ZGoMuasoScsXG4gICAgJ+avj+WRqDPmrKEnXG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC02XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPua3u+WKoOeUqOiNr+aPkOmGkjwvaDI+XG4gICAgICAgIHtzcGVlY2hTdXBwb3J0ZWQgJiYgKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVm9pY2VJbnB1dH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgIGlzTGlzdGVuaW5nXG4gICAgICAgICAgICAgICAgPyAnYmctcmVkLTUwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtNTAwIHRleHQtd2hpdGUgaG92ZXI6YmctYmx1ZS02MDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNMaXN0ZW5pbmcgPyA8TWljT2ZmIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+IDogPE1pYyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPn1cbiAgICAgICAgICAgIHtpc0xpc3RlbmluZyA/ICflgZzmraLlvZXpn7MnIDogJ+ivremfs+i+k+WFpSd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAge3ZvaWNlVHJhbnNjcmlwdCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBwLTMgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS04MDBcIj5cbiAgICAgICAgICAgIDxzdHJvbmc+6K+t6Z+z6K+G5Yir57uT5p6c77yaPC9zdHJvbmc+e3ZvaWNlVHJhbnNjcmlwdH1cbiAgICAgICAgICA8L3A+XG4gICAgICAgICAge2NvbmZpZGVuY2UgPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgbXItMlwiPuivhuWIq+e9ruS/oeW6pjo8L3NwYW4+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWJsdWUtMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7Y29uZmlkZW5jZSAqIDEwMH0lYCB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgbWwtMlwiPnsoY29uZmlkZW5jZSAqIDEwMCkudG9GaXhlZCgxKX0lPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog5pm66IO95bu66K6uICovfVxuICAgICAge3Nob3dTdWdnZXN0aW9ucyAmJiBzdWdnZXN0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHAtMyBiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQteWVsbG93LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICA8c3Ryb25nPvCfpJQg6K+G5Yir572u5L+h5bqm6L6D5L2O77yM5oKo5piv5ZCm5oOz6KaB77yaPC9zdHJvbmc+XG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICB7c3VnZ2VzdGlvbnMubWFwKChzdWdnZXN0aW9uLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG1lZGljaW5lTmFtZTogc3VnZ2VzdGlvbiB9KSlcbiAgICAgICAgICAgICAgICAgIHNldFNob3dTdWdnZXN0aW9ucyhmYWxzZSlcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCB0ZXh0LWxlZnQgcHgtMyBweS0yIHRleHQtc20gYmctd2hpdGUgYm9yZGVyIGJvcmRlci15ZWxsb3ctMzAwIHJvdW5kZWQgaG92ZXI6YmcteWVsbG93LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7c3VnZ2VzdGlvbn1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dTdWdnZXN0aW9ucyhmYWxzZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHRleHQteHMgdGV4dC15ZWxsb3ctNjAwIGhvdmVyOnRleHQteWVsbG93LTgwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAg5YWz6Zet5bu66K6uXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHAtMyBiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgdGV4dC1zbVwiPntlcnJvcn08L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHsvKiDoja/lk4HlkI3np7AgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICA8UGlsbCBjbGFzc05hbWU9XCJ3LTQgaC00IGlubGluZSBtci0xXCIgLz5cbiAgICAgICAgICAgIOiNr+WTgeWQjeensCAqXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5tZWRpY2luZU5hbWV9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdtZWRpY2luZU5hbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCAke1xuICAgICAgICAgICAgICBlcnJvcnMubWVkaWNpbmVOYW1lID8gJ2JvcmRlci1yZWQtMzAwJyA6ICdib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5L6L5aaC77ya6Zi/5Y+45Yy55p6X6IKg5rq254mHXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIHtlcnJvcnMubWVkaWNpbmVOYW1lICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLm1lZGljaW5lTmFtZX08L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOeUqOiNr+WJgumHjyAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgIOeUqOiNr+WJgumHjyAqXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kb3NhZ2V9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdkb3NhZ2UnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCAke1xuICAgICAgICAgICAgICBlcnJvcnMuZG9zYWdlID8gJ2JvcmRlci1yZWQtMzAwJyA6ICdib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5L6L5aaC77yaMeeJh+OAgTJtbOOAgTEwMG1nXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIHtlcnJvcnMuZG9zYWdlICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmRvc2FnZX08L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOeUqOiNr+inhOWImSAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJ3LTQgaC00IGlubGluZSBtci0xXCIgLz5cbiAgICAgICAgICAgIOeUqOiNr+inhOWImSAqXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudXNhZ2V9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCd1c2FnZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweC00IHB5LTMgYm9yZGVyIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50ICR7XG4gICAgICAgICAgICAgIGVycm9ycy51c2FnZSA/ICdib3JkZXItcmVkLTMwMCcgOiAnYm9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPuivt+mAieaLqeeUqOiNr+inhOWImTwvb3B0aW9uPlxuICAgICAgICAgICAge3VzYWdlT3B0aW9ucy5tYXAob3B0aW9uID0+IChcbiAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e29wdGlvbn0gdmFsdWU9e29wdGlvbn0+e29wdGlvbn08L29wdGlvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnVzYWdlfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgndXNhZ2UnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0yIG10LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuaIluiHquWumuS5ieeUqOiNr+inhOWImVwiXG4gICAgICAgICAgLz5cbiAgICAgICAgICB7ZXJyb3JzLnVzYWdlICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnVzYWdlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog55So6I2v6aKR5qyhICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAg55So6I2v6aKR5qyhICpcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5mcmVxdWVuY3l9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdmcmVxdWVuY3knLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCAke1xuICAgICAgICAgICAgICBlcnJvcnMuZnJlcXVlbmN5ID8gJ2JvcmRlci1yZWQtMzAwJyA6ICdib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+6K+36YCJ5oup55So6I2v6aKR5qyhPC9vcHRpb24+XG4gICAgICAgICAgICB7ZnJlcXVlbmN5T3B0aW9ucy5tYXAob3B0aW9uID0+IChcbiAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e29wdGlvbn0gdmFsdWU9e29wdGlvbn0+e29wdGlvbn08L29wdGlvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmZyZXF1ZW5jeX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2ZyZXF1ZW5jeScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTIgbXQtMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5oiW6Ieq5a6a5LmJ55So6I2v6aKR5qyhXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIHtlcnJvcnMuZnJlcXVlbmN5ICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmZyZXF1ZW5jeX08L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOeWl+eoi+aXtumVvyAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJ3LTQgaC00IGlubGluZSBtci0xXCIgLz5cbiAgICAgICAgICAgIOeWl+eoi+aXtumVv++8iOWPr+mAie+8iVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kdXJhdGlvbn1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2R1cmF0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl55aX56iL5aSp5pWwXCJcbiAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDmjInpkq7nu4QgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHktMyBweC00IHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy01IGgtNSBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAg5re75Yqg5o+Q6YaSXG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICBcbiAgICAgICAgICB7b25DYW5jZWwgJiYgKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgb25DbGljaz17b25DYW5jZWx9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHRleHQtZ3JheS03MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWdyYXktNTAwIGZvY3VzOnJpbmctb2Zmc2V0LTJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDlj5bmtohcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9mb3JtPlxuXG4gICAgICB7Lyog6K+t6Z+z6L6T5YWl5o+Q56S6ICovfVxuICAgICAge3NwZWVjaFN1cHBvcnRlZCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBtYi0yXCI+6K+t6Z+z6L6T5YWl5o+Q56S677yaPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIOaCqOWPr+S7peivtO+8mlwi6Zi/5Y+45Yy55p6X6IKg5rq254mH77yM5q+P5qyhMeeJh++8jOmkkOWQjjHlsI/ml7bmnI3nlKjvvIzmr4/ml6Uz5qyhXCJcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTWljIiwiTWljT2ZmIiwiUGx1cyIsIkNsb2NrIiwiUGlsbCIsIkZpbGVUZXh0Iiwic3BlZWNoU2VydmljZSIsInVzZU1lZGljYXRpb24iLCJNZWRpY2luZUlucHV0Rm9ybSIsInVzZXJJZCIsIm9uU3VjY2VzcyIsIm9uQ2FuY2VsIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsIm1lZGljaW5lTmFtZSIsImRvc2FnZSIsInVzYWdlIiwiZnJlcXVlbmN5IiwiZHVyYXRpb24iLCJpc0xpc3RlbmluZyIsInNldElzTGlzdGVuaW5nIiwic3BlZWNoU3VwcG9ydGVkIiwic2V0U3BlZWNoU3VwcG9ydGVkIiwidm9pY2VUcmFuc2NyaXB0Iiwic2V0Vm9pY2VUcmFuc2NyaXB0IiwiZXJyb3JzIiwic2V0RXJyb3JzIiwic3VnZ2VzdGlvbnMiLCJzZXRTdWdnZXN0aW9ucyIsImNvbmZpZGVuY2UiLCJzZXRDb25maWRlbmNlIiwic2hvd1N1Z2dlc3Rpb25zIiwic2V0U2hvd1N1Z2dlc3Rpb25zIiwiYWRkUmVtaW5kZXIiLCJjYWxjdWxhdGVNZWRpY2F0aW9uVGltZXMiLCJsb2FkaW5nIiwiZXJyb3IiLCJjbGVhckVycm9yIiwiaXNTcGVlY2hSZWNvZ25pdGlvblN1cHBvcnRlZCIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZmllbGQiLCJ2YWx1ZSIsInByZXYiLCJoYW5kbGVWb2ljZUlucHV0IiwiYWxlcnQiLCJzdG9wTGlzdGVuaW5nIiwiaGFzUGVybWlzc2lvbiIsInJlcXVlc3RNaWNyb3Bob25lUGVybWlzc2lvbiIsInN0YXJ0TGlzdGVuaW5nIiwicmVzdWx0IiwidHJhbnNjcmlwdCIsImlzRmluYWwiLCJwYXJzZWQiLCJwYXJzZU1lZGljaW5lSW5wdXQiLCJsZW5ndGgiLCJjb25zb2xlIiwidmFsaWRhdGVGb3JtIiwibmV3RXJyb3JzIiwidHJpbSIsIk9iamVjdCIsImtleXMiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJzY2hlZHVsZWRUaW1lcyIsInBhcnNlSW50IiwidW5kZWZpbmVkIiwiaXNFbmFibGVkIiwidXNhZ2VPcHRpb25zIiwiZnJlcXVlbmN5T3B0aW9ucyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwiYnV0dG9uIiwidHlwZSIsIm9uQ2xpY2siLCJwIiwic3Ryb25nIiwic3BhbiIsInN0eWxlIiwid2lkdGgiLCJ0b0ZpeGVkIiwibWFwIiwic3VnZ2VzdGlvbiIsImluZGV4IiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJzZWxlY3QiLCJvcHRpb24iLCJtaW4iLCJkaXNhYmxlZCIsImgzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx\n"));

/***/ })

});