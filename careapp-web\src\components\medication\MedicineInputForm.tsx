'use client'

import { useState, useEffect } from 'react'
import { Mi<PERSON>, MicOff, Plus, Clock, Pill, FileText } from 'lucide-react'
import { speechService } from '@/lib/speech-service'
import { useMedication } from '@/store/medication'

interface MedicineInputFormProps {
  userId: string
  onSuccess?: () => void
  onCancel?: () => void
}

export function MedicineInputForm({ userId, onSuccess, onCancel }: MedicineInputFormProps) {
  const [formData, setFormData] = useState({
    medicineName: '',
    dosage: '',
    usage: '',
    frequency: '',
    duration: ''
  })
  
  const [isListening, setIsListening] = useState(false)
  const [speechSupported, setSpeechSupported] = useState(false)
  const [voiceTranscript, setVoiceTranscript] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [confidence, setConfidence] = useState<number>(0)
  const [showSuggestions, setShowSuggestions] = useState(false)

  const { addReminder, calculateMedicationTimes, loading, error, clearError } = useMedication()

  useEffect(() => {
    setSpeechSupported(speechService.isSpeechRecognitionSupported())
  }, [])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleVoiceInput = async () => {
    if (!speechSupported) {
      alert('您的浏览器不支持语音识别功能')
      return
    }

    if (isListening) {
      speechService.stopListening()
      setIsListening(false)
      return
    }

    try {
      // 请求麦克风权限
      const hasPermission = await speechService.requestMicrophonePermission()
      if (!hasPermission) {
        alert('需要麦克风权限才能使用语音输入功能')
        return
      }

      setIsListening(true)
      setVoiceTranscript('')

      await speechService.startListening(
        (result) => {
          setVoiceTranscript(result.transcript)
          
          if (result.isFinal) {
            // 解析语音输入（增强版）
            const parsed = speechService.parseMedicineInput(result.transcript)

            // 设置置信度和建议
            setConfidence(parsed.confidence || 0)
            setSuggestions(parsed.suggestions || [])
            setShowSuggestions((parsed.confidence || 0) < 0.8 && (parsed.suggestions || []).length > 0)

            setFormData(prev => ({
              ...prev,
              medicineName: parsed.medicineName || prev.medicineName,
              dosage: parsed.dosage || prev.dosage,
              usage: parsed.usage || prev.usage,
              frequency: parsed.frequency || prev.frequency
            }))

            setIsListening(false)
          }
        },
        (error) => {
          console.error('语音识别错误:', error)
          setIsListening(false)
          alert(`语音识别失败: ${error}`)
        }
      )
    } catch (error) {
      console.error('启动语音识别失败:', error)
      setIsListening(false)
      alert('启动语音识别失败，请检查麦克风权限')
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.medicineName.trim()) {
      newErrors.medicineName = '请输入药品名称'
    }

    if (!formData.dosage.trim()) {
      newErrors.dosage = '请输入用药剂量'
    }

    if (!formData.usage.trim()) {
      newErrors.usage = '请输入用药规则'
    }

    if (!formData.frequency.trim()) {
      newErrors.frequency = '请输入用药频次'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      clearError()
      
      // 计算用药时间
      const scheduledTimes = calculateMedicationTimes(formData.usage, formData.frequency)
      
      await addReminder({
        medicineName: formData.medicineName.trim(),
        dosage: formData.dosage.trim(),
        usage: formData.usage.trim(),
        frequency: formData.frequency.trim(),
        duration: formData.duration ? parseInt(formData.duration) : undefined,
        scheduledTimes,
        isEnabled: true,
        userId
      })

      // 重置表单
      setFormData({
        medicineName: '',
        dosage: '',
        usage: '',
        frequency: '',
        duration: ''
      })

      onSuccess?.()
    } catch (error) {
      console.error('添加用药提醒失败:', error)
    }
  }

  const usageOptions = [
    '餐前30分钟',
    '餐后1小时',
    '随餐服用',
    '睡前30分钟',
    '晨起服用',
    '空腹服用'
  ]

  const frequencyOptions = [
    '每日1次',
    '每日2次',
    '每日3次',
    '每日4次',
    '每周2次',
    '每周3次'
  ]

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800">添加用药提醒</h2>
        {speechSupported && (
          <button
            type="button"
            onClick={handleVoiceInput}
            className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
              isListening
                ? 'bg-red-500 text-white'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isListening ? <MicOff className="w-4 h-4 mr-2" /> : <Mic className="w-4 h-4 mr-2" />}
            {isListening ? '停止录音' : '语音输入'}
          </button>
        )}
      </div>

      {voiceTranscript && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>语音识别结果：</strong>{voiceTranscript}
          </p>
          {confidence > 0 && (
            <div className="mt-2 flex items-center">
              <span className="text-xs text-blue-600 mr-2">识别置信度:</span>
              <div className="flex-1 bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${confidence * 100}%` }}
                />
              </div>
              <span className="text-xs text-blue-600 ml-2">{(confidence * 100).toFixed(1)}%</span>
            </div>
          )}
        </div>
      )}

      {/* 智能建议 */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800 mb-2">
            <strong>🤔 识别置信度较低，您是否想要：</strong>
          </p>
          <div className="space-y-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, medicineName: suggestion }))
                  setShowSuggestions(false)
                }}
                className="block w-full text-left px-3 py-2 text-sm bg-white border border-yellow-300 rounded hover:bg-yellow-100 transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
          <button
            type="button"
            onClick={() => setShowSuggestions(false)}
            className="mt-2 text-xs text-yellow-600 hover:text-yellow-800"
          >
            关闭建议
          </button>
        </div>
      )}

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 药品名称 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Pill className="w-4 h-4 inline mr-1" />
            药品名称 *
          </label>
          <input
            type="text"
            value={formData.medicineName}
            onChange={(e) => handleInputChange('medicineName', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.medicineName ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="例如：阿司匹林肠溶片"
          />
          {errors.medicineName && (
            <p className="mt-1 text-sm text-red-600">{errors.medicineName}</p>
          )}
        </div>

        {/* 用药剂量 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            用药剂量 *
          </label>
          <input
            type="text"
            value={formData.dosage}
            onChange={(e) => handleInputChange('dosage', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.dosage ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="例如：1片、2ml、100mg"
          />
          {errors.dosage && (
            <p className="mt-1 text-sm text-red-600">{errors.dosage}</p>
          )}
        </div>

        {/* 用药规则 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Clock className="w-4 h-4 inline mr-1" />
            用药规则 *
          </label>
          <select
            value={formData.usage}
            onChange={(e) => handleInputChange('usage', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.usage ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value="">请选择用药规则</option>
            {usageOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          <input
            type="text"
            value={formData.usage}
            onChange={(e) => handleInputChange('usage', e.target.value)}
            className="w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="或自定义用药规则"
          />
          {errors.usage && (
            <p className="mt-1 text-sm text-red-600">{errors.usage}</p>
          )}
        </div>

        {/* 用药频次 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            用药频次 *
          </label>
          <select
            value={formData.frequency}
            onChange={(e) => handleInputChange('frequency', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.frequency ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value="">请选择用药频次</option>
            {frequencyOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          <input
            type="text"
            value={formData.frequency}
            onChange={(e) => handleInputChange('frequency', e.target.value)}
            className="w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="或自定义用药频次"
          />
          {errors.frequency && (
            <p className="mt-1 text-sm text-red-600">{errors.frequency}</p>
          )}
        </div>

        {/* 疗程时长 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FileText className="w-4 h-4 inline mr-1" />
            疗程时长（可选）
          </label>
          <input
            type="number"
            value={formData.duration}
            onChange={(e) => handleInputChange('duration', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入疗程天数"
            min="1"
          />
        </div>

        {/* 按钮组 */}
        <div className="flex space-x-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <>
                <Plus className="w-5 h-5 mr-2" />
                添加提醒
              </>
            )}
          </button>
          
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              取消
            </button>
          )}
        </div>
      </form>

      {/* 语音输入提示 */}
      {speechSupported && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-800 mb-3">💡 语音输入提示：</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <p className="font-medium text-gray-700 mb-1">示例1：</p>
                <p className="italic">"阿司匹林肠溶片，每次1片，餐后1小时服用，每日3次"</p>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">示例2：</p>
                <p className="italic">"二甲双胍，每次2片，餐前30分钟，一日3次"</p>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">示例3：</p>
                <p className="italic">"奥美拉唑，每次1粒，晨起空腹，每天1次"</p>
              </div>
              <div>
                <p className="font-medium text-gray-700 mb-1">示例4：</p>
                <p className="italic">"胰岛素，每次10单位，餐前15分钟注射"</p>
              </div>
            </div>
            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                💡 <strong>提示：</strong>请清晰地说出药品名称、剂量、用法和频次。系统支持智能识别和纠错。
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
