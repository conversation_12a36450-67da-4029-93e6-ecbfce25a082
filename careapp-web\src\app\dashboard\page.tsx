'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/store/auth'
import { Heart, Pill, Activity, Phone, Users, LogOut, Settings } from 'lucide-react'

// 强制动态渲染
export const dynamic = 'force-dynamic'

export default function DashboardPage() {
  const [mounted, setMounted] = useState(false)
  const { user, initialized, signOut, initialize } = useAuth()
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
    initialize()
  }, [initialize])

  const handleSignOut = async () => {
    try {
      await signOut()
      router.replace('/auth')
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  if (!mounted || !initialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600">正在加载仪表板...</p>
        </div>
      </div>
    )
  }

  // 如果未登录，显示登录提示而不是重定向
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-red-600 mb-4">请先登录</p>
          <button
            type="button"
            onClick={() => router.replace('/auth')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            前往登录
          </button>
        </div>
      </div>
    )
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case 'patient': return '患者'
      case 'family': return '家属'
      case 'caregiver': return '护理员'
      default: return '用户'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Heart className="w-8 h-8 text-red-500 mr-3" />
              <h1 className="text-2xl font-bold text-gray-800">照护宝</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-600">欢迎回来</p>
                <p className="font-semibold text-gray-800">
                  {user.user_metadata?.name || user.email}
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {getRoleText(user.user_metadata?.role || 'patient')}
                  </span>
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg">
                  <Settings className="w-5 h-5" />
                </button>
                <button
                  onClick={handleSignOut}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
                >
                  <LogOut className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Card */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            欢迎使用照护宝 WEB 版！
          </h2>
          <p className="text-gray-600 mb-4">
            您已成功登录系统。WEB版本正在开发中，更多功能即将上线。
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800 text-sm">
              💡 提示：当前为开发版本，部分功能可能尚未完善。我们正在努力为您提供更好的体验！
            </p>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4">
              <Pill className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">用药提醒</h3>
            <p className="text-gray-600 text-sm mb-4">智能提醒，按时用药</p>
            <button className="w-full bg-green-100 text-green-700 py-2 px-4 rounded-lg hover:bg-green-200 transition-colors">
              即将上线
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
              <Activity className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">健康监测</h3>
            <p className="text-gray-600 text-sm mb-4">实时监控健康数据</p>
            <button className="w-full bg-blue-100 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-200 transition-colors">
              即将上线
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4">
              <Phone className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">紧急呼叫</h3>
            <p className="text-gray-600 text-sm mb-4">一键求助，及时救援</p>
            <button className="w-full bg-red-100 text-red-700 py-2 px-4 rounded-lg hover:bg-red-200 transition-colors">
              即将上线
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">家庭互动</h3>
            <p className="text-gray-600 text-sm mb-4">家人关爱，温暖陪伴</p>
            <button className="w-full bg-purple-100 text-purple-700 py-2 px-4 rounded-lg hover:bg-purple-200 transition-colors">
              即将上线
            </button>
          </div>
        </div>

        {/* Development Status */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">开发进度</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">✅ 项目架构设计</span>
              <span className="text-green-600 font-medium">已完成</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">✅ WEB项目初始化</span>
              <span className="text-green-600 font-medium">已完成</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">✅ 数据库设计</span>
              <span className="text-green-600 font-medium">已完成</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">✅ 用户认证系统</span>
              <span className="text-green-600 font-medium">已完成</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">🔄 核心功能开发</span>
              <span className="text-blue-600 font-medium">进行中</span>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
