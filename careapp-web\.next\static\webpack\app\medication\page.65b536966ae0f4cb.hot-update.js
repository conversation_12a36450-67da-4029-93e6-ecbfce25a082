"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 初始化音频系统\n   */ initializeAudio() {\n        if (false) {}\n        try {\n            // 预加载提醒音效\n            this.reminderAudio = new Audio();\n            this.reminderAudio.preload = 'auto';\n            this.reminderAudio.volume = 0.7;\n            // 尝试加载多种音效格式\n            const audioSources = [\n                '/sounds/reminder.mp3',\n                '/sounds/reminder.wav',\n                '/sounds/reminder.ogg'\n            ];\n            // 使用第一个可用的音频格式\n            for (const src of audioSources){\n                this.reminderAudio.src = src;\n                break;\n            }\n            // 如果没有音频文件，创建合成音效\n            if (!this.reminderAudio.src) {\n                this.createSyntheticReminderSound();\n            }\n            this.isAudioInitialized = true;\n        } catch (error) {\n            console.warn('音频初始化失败，将使用合成音效:', error);\n            this.createSyntheticReminderSound();\n        }\n    }\n    /**\n   * 创建合成提醒音效\n   */ createSyntheticReminderSound() {\n        if (false) {}\n        try {\n            // 使用Web Audio API创建合成音效\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const createBeep = function(frequency, duration) {\n                let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const oscillator = audioContext.createOscillator();\n                        const gainNode = audioContext.createGain();\n                        oscillator.connect(gainNode);\n                        gainNode.connect(audioContext.destination);\n                        oscillator.frequency.value = frequency;\n                        oscillator.type = 'sine';\n                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n                        oscillator.start(audioContext.currentTime);\n                        oscillator.stop(audioContext.currentTime + duration);\n                        setTimeout(resolve, duration * 1000);\n                    }, delay);\n                });\n            };\n            // 创建自定义提醒音效播放函数\n            this.playCustomReminderSound = async ()=>{\n                try {\n                    await createBeep(800, 0.2, 0) // 第一声\n                    ;\n                    await createBeep(1000, 0.2, 100) // 第二声\n                    ;\n                    await createBeep(800, 0.3, 200) // 第三声\n                    ;\n                } catch (error) {\n                    console.error('播放合成音效失败:', error);\n                }\n            };\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            await this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报和强化音效\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 浏览器通知（更紧急）\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"⚠️ 重要提醒：\".concat(notification.medicineName), {\n                body: \"请立即服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-urgent.png',\n                badge: '/icons/badge-urgent.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 强化音效提醒\n        if (settings.soundEnabled) {\n            await this.playIntensiveReminderSound();\n        }\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 页面弹窗（更显眼的样式）\n        this.notifyUI('reminder-popup-urgent', notification);\n    }\n    /**\n   * 第三级提醒：确认是否已服药（最高级别）\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 最高级别浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"\\uD83D\\uDEA8 紧急提醒：\".concat(notification.medicineName), {\n                body: \"您可能忘记服药了！请确认是否已服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-emergency.png',\n                badge: '/icons/badge-emergency.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                vibrate: [\n                    200,\n                    100,\n                    200\n                ],\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'missed',\n                        title: '忘记了'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后确认'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 连续强化音效\n        if (settings.soundEnabled) {\n            await this.playIntensiveReminderSound();\n            // 间隔后再次播放\n            setTimeout(async ()=>{\n                await this.playIntensiveReminderSound();\n            }, 2000);\n        }\n        // 询问是否已服药\n        const confirmationText = \"重要提醒！您是否已经服用了\".concat(notification.medicineName, \"？如果忘记了，请立即服用。\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText,\n                    rate: 0.9,\n                    pitch: 1.1,\n                    volume: 1.0 // 最大音量\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示紧急确认对话框\n        this.notifyUI('confirmation-dialog-urgent', notification);\n        // 如果支持，尝试使页面闪烁提醒\n        this.triggerPageFlash();\n    }\n    /**\n   * 触发页面闪烁效果\n   */ triggerPageFlash() {\n        if (typeof document === 'undefined') return;\n        try {\n            const originalTitle = document.title;\n            let flashCount = 0;\n            const maxFlashes = 6;\n            const flashInterval = setInterval(()=>{\n                document.title = flashCount % 2 === 0 ? '🚨 用药提醒！' : originalTitle;\n                flashCount++;\n                if (flashCount >= maxFlashes) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                }\n            }, 500);\n            // 页面可见性变化时停止闪烁\n            const handleVisibilityChange = ()=>{\n                if (!document.hidden) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                }\n            };\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n        } catch (error) {\n            console.error('页面闪烁效果失败:', error);\n        }\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ async playReminderSound() {\n        try {\n            // 优先使用预加载的音频\n            if (this.reminderAudio && this.isAudioInitialized) {\n                this.reminderAudio.currentTime = 0 // 重置播放位置\n                ;\n                await this.reminderAudio.play();\n                return;\n            }\n            // 如果预加载音频不可用，使用合成音效\n            if (this.playCustomReminderSound) {\n                await this.playCustomReminderSound();\n                return;\n            }\n            // 最后的备选方案：简单的beep音效\n            this.playFallbackSound();\n        } catch (error) {\n            console.error('播放提醒声音失败:', error);\n            // 如果所有音效都失败，尝试备选方案\n            this.playFallbackSound();\n        }\n    }\n    /**\n   * 备选音效（系统beep）\n   */ playFallbackSound() {\n        try {\n            // 使用系统默认音效\n            if ('speechSynthesis' in window) {\n                const utterance = new SpeechSynthesisUtterance('');\n                utterance.volume = 0.1;\n                speechSynthesis.speak(utterance);\n            }\n        } catch (error) {\n            console.warn('备选音效也无法播放:', error);\n        }\n    }\n    /**\n   * 播放连续提醒音效（用于重要提醒）\n   */ async playIntensiveReminderSound() {\n        for(let i = 0; i < 3; i++){\n            await this.playReminderSound();\n            await new Promise((resolve)=>setTimeout(resolve, 500)) // 间隔500ms\n            ;\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        this.reminderAudio = null;\n        this.isAudioInitialized = false;\n        this.playCustomReminderSound = null;\n        if (true) {\n            this.initializeNotifications();\n            this.initializeAudio();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvbm90aWZpY2F0aW9uLXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnRDtBQWV6QyxNQUFNQztJQWNYOztHQUVDLEdBQ0QsTUFBY0MsMEJBQTBCO1FBQ3RDLElBQUksS0FBNkIsSUFBSSxrQkFBa0JDLFFBQVE7WUFDN0QsSUFBSSxDQUFDQyxzQkFBc0IsR0FBR0MsYUFBYUMsVUFBVTtZQUVyRCxJQUFJLElBQUksQ0FBQ0Ysc0JBQXNCLEtBQUssV0FBVztnQkFDN0MsSUFBSSxDQUFDQSxzQkFBc0IsR0FBRyxNQUFNQyxhQUFhRSxpQkFBaUI7WUFDcEU7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEI7UUFDeEIsSUFBSSxLQUE2QixFQUFFO1FBRW5DLElBQUk7WUFDRixVQUFVO1lBQ1YsSUFBSSxDQUFDRSxhQUFhLEdBQUcsSUFBSUM7WUFDekIsSUFBSSxDQUFDRCxhQUFhLENBQUNFLE9BQU8sR0FBRztZQUM3QixJQUFJLENBQUNGLGFBQWEsQ0FBQ0csTUFBTSxHQUFHO1lBRTVCLGFBQWE7WUFDYixNQUFNQyxlQUFlO2dCQUNuQjtnQkFDQTtnQkFDQTthQUNEO1lBRUQsZUFBZTtZQUNmLEtBQUssTUFBTUMsT0FBT0QsYUFBYztnQkFDOUIsSUFBSSxDQUFDSixhQUFhLENBQUNLLEdBQUcsR0FBR0E7Z0JBQ3pCO1lBQ0Y7WUFFQSxrQkFBa0I7WUFDbEIsSUFBSSxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDSyxHQUFHLEVBQUU7Z0JBQzNCLElBQUksQ0FBQ0MsNEJBQTRCO1lBQ25DO1lBRUEsSUFBSSxDQUFDQyxrQkFBa0IsR0FBRztRQUM1QixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLG9CQUFvQkY7WUFDakMsSUFBSSxDQUFDRiw0QkFBNEI7UUFDbkM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsK0JBQXVDO1FBQ3JDLElBQUksS0FBNkIsRUFBRTtRQUVuQyxJQUFJO1lBQ0Ysd0JBQXdCO1lBQ3hCLE1BQU1LLGVBQWUsSUFBS2pCLENBQUFBLE9BQU9rQixZQUFZLElBQUksT0FBZ0JDLGtCQUFrQjtZQUVuRixNQUFNQyxhQUFhLFNBQUNDLFdBQW1CQztvQkFBa0JDLHlFQUFnQjtnQkFDdkUsT0FBTyxJQUFJQyxRQUFjLENBQUNDO29CQUN4QkMsV0FBVzt3QkFDVCxNQUFNQyxhQUFhVixhQUFhVyxnQkFBZ0I7d0JBQ2hELE1BQU1DLFdBQVdaLGFBQWFhLFVBQVU7d0JBRXhDSCxXQUFXSSxPQUFPLENBQUNGO3dCQUNuQkEsU0FBU0UsT0FBTyxDQUFDZCxhQUFhZSxXQUFXO3dCQUV6Q0wsV0FBV04sU0FBUyxDQUFDWSxLQUFLLEdBQUdaO3dCQUM3Qk0sV0FBV08sSUFBSSxHQUFHO3dCQUVsQkwsU0FBU00sSUFBSSxDQUFDQyxjQUFjLENBQUMsS0FBS25CLGFBQWFvQixXQUFXO3dCQUMxRFIsU0FBU00sSUFBSSxDQUFDRyw0QkFBNEIsQ0FBQyxNQUFNckIsYUFBYW9CLFdBQVcsR0FBR2Y7d0JBRTVFSyxXQUFXWSxLQUFLLENBQUN0QixhQUFhb0IsV0FBVzt3QkFDekNWLFdBQVdhLElBQUksQ0FBQ3ZCLGFBQWFvQixXQUFXLEdBQUdmO3dCQUUzQ0ksV0FBV0QsU0FBU0gsV0FBVztvQkFDakMsR0FBR0M7Z0JBQ0w7WUFDRjtZQUVBLGdCQUFnQjtZQUNoQixJQUFJLENBQUNrQix1QkFBdUIsR0FBRztnQkFDN0IsSUFBSTtvQkFDRixNQUFNckIsV0FBVyxLQUFLLEtBQUssR0FBTSxNQUFNOztvQkFDdkMsTUFBTUEsV0FBVyxNQUFNLEtBQUssS0FBSyxNQUFNOztvQkFDdkMsTUFBTUEsV0FBVyxLQUFLLEtBQUssS0FBTSxNQUFNOztnQkFDekMsRUFBRSxPQUFPTixPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsYUFBYUE7Z0JBQzdCO1lBQ0Y7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLHFCQUFxQkY7UUFDcEM7SUFDRjtJQUlBOztHQUVDLEdBQ0QsTUFBTTRCLGdDQUFrRDtRQUN0RCxJQUFJLENBQUUsbUJBQWtCMUMsTUFBSyxHQUFJO1lBQy9CLE9BQU87UUFDVDtRQUVBLElBQUlFLGFBQWFDLFVBQVUsS0FBSyxXQUFXO1lBQ3pDLE9BQU87UUFDVDtRQUVBLE1BQU1BLGFBQWEsTUFBTUQsYUFBYUUsaUJBQWlCO1FBQ3ZELElBQUksQ0FBQ0gsc0JBQXNCLEdBQUdFO1FBQzlCLE9BQU9BLGVBQWU7SUFDeEI7SUFFQTs7R0FFQyxHQUNEd0MsaUJBQ0VDLFFBQTBCLEVBQzFCQyxRQUEwQixFQUMxQkMsU0FBMkQsRUFDckQ7UUFDTkYsU0FBU0csY0FBYyxDQUFDQyxPQUFPLENBQUNDLENBQUFBO1lBQzlCLE1BQU1DLG9CQUFvQixJQUFJLENBQUNDLHdCQUF3QixDQUFDRjtZQUN4RCxNQUFNRyxhQUFhLEdBQWtCSCxPQUFmTCxTQUFTUyxFQUFFLEVBQUMsS0FBUSxPQUFMSjtZQUVyQyxZQUFZO1lBQ1osTUFBTUssb0JBQW9CLElBQUlDLEtBQUtMLGtCQUFrQk0sT0FBTyxLQUFLWCxTQUFTWSxvQkFBb0IsR0FBRztZQUVqRyxNQUFNQyxVQUFVaEMsV0FBVztnQkFDekIsSUFBSSxDQUFDaUMsZUFBZSxDQUFDZixVQUFVSyxNQUFNSixVQUFVQztZQUNqRCxHQUFHUSxrQkFBa0JFLE9BQU8sS0FBS0QsS0FBS0ssR0FBRztZQUV6QyxJQUFJLENBQUNDLGdCQUFnQixDQUFDQyxHQUFHLENBQUNWLFlBQVlNO1FBQ3hDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNDLGdCQUNaZixRQUEwQixFQUMxQm1CLGFBQXFCLEVBQ3JCbEIsUUFBMEIsRUFDMUJDLFNBQTJELEVBQzVDO1FBQ2YsTUFBTWtCLGlCQUFpQixHQUFrQkQsT0FBZm5CLFNBQVNTLEVBQUUsRUFBQyxLQUFvQkUsT0FBakJRLGVBQWMsS0FBYyxPQUFYUixLQUFLSyxHQUFHO1FBRWxFLE1BQU1LLGVBQXFDO1lBQ3pDWixJQUFJVztZQUNKWixZQUFZUixTQUFTUyxFQUFFO1lBQ3ZCYSxjQUFjdEIsU0FBU3NCLFlBQVk7WUFDbkNDLFFBQVF2QixTQUFTdUIsTUFBTTtZQUN2QkMsT0FBT3hCLFNBQVN3QixLQUFLO1lBQ3JCTDtZQUNBTSxPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsV0FBVyxJQUFJaEI7UUFDakI7UUFFQSxJQUFJLENBQUNpQixlQUFlLENBQUNWLEdBQUcsQ0FBQ0UsZ0JBQWdCQztRQUV6QyxRQUFRO1FBQ1IsTUFBTSxJQUFJLENBQUNRLHNCQUFzQixDQUFDUixjQUFjcEI7UUFFaEQsU0FBUztRQUNULElBQUksQ0FBQzZCLHlCQUF5QixDQUFDVCxjQUFjcEIsVUFBVUM7SUFDekQ7SUFFQTs7R0FFQyxHQUNELE1BQWMyQix1QkFDWlIsWUFBa0MsRUFDbENwQixRQUEwQixFQUNYO1FBQ2YsUUFBUTtRQUNSLElBQUlBLFNBQVM4QixtQkFBbUIsSUFBSSxJQUFJLENBQUMxRSxzQkFBc0IsS0FBSyxXQUFXO1lBQzdFLE1BQU0yRSxzQkFBc0IsSUFBSTFFLGFBQWEsUUFBa0MsT0FBMUIrRCxhQUFhQyxZQUFZLEdBQUk7Z0JBQ2hGVyxNQUFNLE9BQTJCLE9BQXBCWixhQUFhRSxNQUFNO2dCQUNoQ1csTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsS0FBS2YsYUFBYVosRUFBRTtnQkFDcEI0QixvQkFBb0I7Z0JBQ3BCQyxTQUFTO29CQUNQO3dCQUFFQyxRQUFRO3dCQUFXQyxPQUFPO29CQUFNO29CQUNsQzt3QkFBRUQsUUFBUTt3QkFBVUMsT0FBTztvQkFBTztpQkFDbkM7WUFDSDtZQUVBUixvQkFBb0JTLE9BQU8sR0FBRztnQkFDNUIsSUFBSSxDQUFDQywwQkFBMEIsQ0FBQ3JCLGFBQWFaLEVBQUUsRUFBRTtnQkFDakR1QixvQkFBb0JXLEtBQUs7WUFDM0I7UUFDRjtRQUVBLE9BQU87UUFDUCxJQUFJMUMsU0FBUzJDLFlBQVksRUFBRTtZQUN6QixNQUFNLElBQUksQ0FBQ0MsaUJBQWlCO1FBQzlCO1FBRUEsbUJBQW1CO1FBQ25CLElBQUksQ0FBQ0MsUUFBUSxDQUFDLGtCQUFrQnpCO0lBQ2xDO0lBRUE7O0dBRUMsR0FDRCxNQUFjMEIsd0JBQ1oxQixZQUFrQyxFQUNsQ3BCLFFBQTBCLEVBQ1g7UUFDZm9CLGFBQWFJLEtBQUssR0FBRztRQUVyQixhQUFhO1FBQ2IsSUFBSXhCLFNBQVM4QixtQkFBbUIsSUFBSSxJQUFJLENBQUMxRSxzQkFBc0IsS0FBSyxXQUFXO1lBQzdFLE1BQU0yRSxzQkFBc0IsSUFBSTFFLGFBQWEsV0FBcUMsT0FBMUIrRCxhQUFhQyxZQUFZLEdBQUk7Z0JBQ25GVyxNQUFNLFNBQTZCLE9BQXBCWixhQUFhRSxNQUFNO2dCQUNsQ1csTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsS0FBS2YsYUFBYVosRUFBRTtnQkFDcEI0QixvQkFBb0I7Z0JBQ3BCVyxRQUFRO2dCQUNSVixTQUFTO29CQUNQO3dCQUFFQyxRQUFRO3dCQUFXQyxPQUFPO29CQUFNO29CQUNsQzt3QkFBRUQsUUFBUTt3QkFBVUMsT0FBTztvQkFBTztpQkFDbkM7WUFDSDtZQUVBUixvQkFBb0JTLE9BQU8sR0FBRztnQkFDNUIsSUFBSSxDQUFDQywwQkFBMEIsQ0FBQ3JCLGFBQWFaLEVBQUUsRUFBRTtnQkFDakR1QixvQkFBb0JXLEtBQUs7WUFDM0I7UUFDRjtRQUVBLFNBQVM7UUFDVCxJQUFJMUMsU0FBUzJDLFlBQVksRUFBRTtZQUN6QixNQUFNLElBQUksQ0FBQ0ssMEJBQTBCO1FBQ3ZDO1FBRUEsT0FBTztRQUNQLElBQUloRCxTQUFTaUQsWUFBWSxJQUFJakcsMERBQWFBLENBQUNrRywwQkFBMEIsSUFBSTtZQUN2RSxNQUFNQyxhQUFhbkcsMERBQWFBLENBQUNvRyxzQkFBc0IsQ0FDckRoQyxhQUFhQyxZQUFZLEVBQ3pCRCxhQUFhRSxNQUFNLEVBQ25CRixhQUFhRyxLQUFLO1lBR3BCLElBQUk7Z0JBQ0YsTUFBTXZFLDBEQUFhQSxDQUFDcUcsS0FBSyxDQUFDO29CQUFFQyxNQUFNSDtnQkFBVztZQUMvQyxFQUFFLE9BQU9sRixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsV0FBV0E7WUFDM0I7UUFDRjtRQUVBLGVBQWU7UUFDZixJQUFJLENBQUM0RSxRQUFRLENBQUMseUJBQXlCekI7SUFDekM7SUFFQTs7R0FFQyxHQUNELE1BQWNtQyx1QkFDWm5DLFlBQWtDLEVBQ2xDcEIsUUFBMEIsRUFDWDtRQUNmb0IsYUFBYUksS0FBSyxHQUFHO1FBRXJCLFlBQVk7UUFDWixJQUFJeEIsU0FBUzhCLG1CQUFtQixJQUFJLElBQUksQ0FBQzFFLHNCQUFzQixLQUFLLFdBQVc7WUFDN0UsTUFBTTJFLHNCQUFzQixJQUFJMUUsYUFBYSxxQkFBcUMsT0FBMUIrRCxhQUFhQyxZQUFZLEdBQUk7Z0JBQ25GVyxNQUFNLHFCQUF5QyxPQUFwQlosYUFBYUUsTUFBTTtnQkFDOUNXLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLEtBQUtmLGFBQWFaLEVBQUU7Z0JBQ3BCNEIsb0JBQW9CO2dCQUNwQlcsUUFBUTtnQkFDUlMsU0FBUztvQkFBQztvQkFBSztvQkFBSztpQkFBSTtnQkFDeEJuQixTQUFTO29CQUNQO3dCQUFFQyxRQUFRO3dCQUFXQyxPQUFPO29CQUFNO29CQUNsQzt3QkFBRUQsUUFBUTt3QkFBVUMsT0FBTztvQkFBTTtvQkFDakM7d0JBQUVELFFBQVE7d0JBQVVDLE9BQU87b0JBQU87aUJBQ25DO1lBQ0g7WUFFQVIsb0JBQW9CUyxPQUFPLEdBQUc7Z0JBQzVCLElBQUksQ0FBQ0MsMEJBQTBCLENBQUNyQixhQUFhWixFQUFFLEVBQUU7Z0JBQ2pEdUIsb0JBQW9CVyxLQUFLO1lBQzNCO1FBQ0Y7UUFFQSxTQUFTO1FBQ1QsSUFBSTFDLFNBQVMyQyxZQUFZLEVBQUU7WUFDekIsTUFBTSxJQUFJLENBQUNLLDBCQUEwQjtZQUNyQyxVQUFVO1lBQ1ZuRSxXQUFXO2dCQUNULE1BQU0sSUFBSSxDQUFDbUUsMEJBQTBCO1lBQ3ZDLEdBQUc7UUFDTDtRQUVBLFVBQVU7UUFDVixNQUFNUyxtQkFBbUIsZ0JBQTBDLE9BQTFCckMsYUFBYUMsWUFBWSxFQUFDO1FBRW5FLElBQUlyQixTQUFTaUQsWUFBWSxJQUFJakcsMERBQWFBLENBQUNrRywwQkFBMEIsSUFBSTtZQUN2RSxJQUFJO2dCQUNGLE1BQU1sRywwREFBYUEsQ0FBQ3FHLEtBQUssQ0FBQztvQkFDeEJDLE1BQU1HO29CQUNOQyxNQUFNO29CQUNOQyxPQUFPO29CQUNQL0YsUUFBUSxJQUFJLE9BQU87Z0JBQ3JCO1lBQ0YsRUFBRSxPQUFPSyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsV0FBV0E7WUFDM0I7UUFDRjtRQUVBLFlBQVk7UUFDWixJQUFJLENBQUM0RSxRQUFRLENBQUMsOEJBQThCekI7UUFFNUMsaUJBQWlCO1FBQ2pCLElBQUksQ0FBQ3dDLGdCQUFnQjtJQUN2QjtJQUVBOztHQUVDLEdBQ0QsbUJBQWlDO1FBQy9CLElBQUksT0FBT0MsYUFBYSxhQUFhO1FBRXJDLElBQUk7WUFDRixNQUFNQyxnQkFBZ0JELFNBQVN0QixLQUFLO1lBQ3BDLElBQUl3QixhQUFhO1lBQ2pCLE1BQU1DLGFBQWE7WUFFbkIsTUFBTUMsZ0JBQWdCQyxZQUFZO2dCQUNoQ0wsU0FBU3RCLEtBQUssR0FBR3dCLGFBQWEsTUFBTSxJQUFJLGFBQWFEO2dCQUNyREM7Z0JBRUEsSUFBSUEsY0FBY0MsWUFBWTtvQkFDNUJHLGNBQWNGO29CQUNkSixTQUFTdEIsS0FBSyxHQUFHdUI7Z0JBQ25CO1lBQ0YsR0FBRztZQUVILGVBQWU7WUFDZixNQUFNTSx5QkFBeUI7Z0JBQzdCLElBQUksQ0FBQ1AsU0FBU1EsTUFBTSxFQUFFO29CQUNwQkYsY0FBY0Y7b0JBQ2RKLFNBQVN0QixLQUFLLEdBQUd1QjtvQkFDakJELFNBQVNTLG1CQUFtQixDQUFDLG9CQUFvQkY7Z0JBQ25EO1lBQ0Y7WUFFQVAsU0FBU1UsZ0JBQWdCLENBQUMsb0JBQW9CSDtRQUNoRCxFQUFFLE9BQU9uRyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxhQUFhQTtRQUM3QjtJQUNGO0lBRUE7O0dBRUMsR0FDRCwwQkFDRW1ELFlBQWtDLEVBQ2xDcEIsUUFBMEIsRUFDMUJDLFNBQTJELEVBQ3JEO1FBQ04sSUFBSXVFLGdCQUFnQjtRQUNwQixNQUFNQyxlQUFlekUsU0FBU3lFLFlBQVk7UUFFMUMsTUFBTUMsZUFBZTtZQUNuQixJQUFJLENBQUMsSUFBSSxDQUFDL0MsZUFBZSxDQUFDZ0QsR0FBRyxDQUFDdkQsYUFBYVosRUFBRSxLQUFLZ0UsaUJBQWlCQyxjQUFjO2dCQUMvRSxpQkFBaUI7Z0JBQ2pCLElBQUlELGlCQUFpQkMsY0FBYztvQkFDakMsSUFBSSxDQUFDRyxlQUFlLENBQUN4RCxjQUFjcEI7Z0JBQ3JDO2dCQUNBO1lBQ0Y7WUFFQSxNQUFNYSxVQUFVaEMsV0FBVztnQkFDekIsSUFBSSxDQUFDLElBQUksQ0FBQzhDLGVBQWUsQ0FBQ2dELEdBQUcsQ0FBQ3ZELGFBQWFaLEVBQUUsR0FBRztnQkFFaERnRTtnQkFFQSxJQUFJQSxrQkFBa0IsR0FBRztvQkFDdkIsTUFBTSxJQUFJLENBQUMxQix1QkFBdUIsQ0FBQzFCLGNBQWNwQjtnQkFDbkQsT0FBTyxJQUFJd0UsaUJBQWlCLEdBQUc7b0JBQzdCLE1BQU0sSUFBSSxDQUFDakIsc0JBQXNCLENBQUNuQyxjQUFjcEI7Z0JBQ2xEO2dCQUVBMEU7WUFDRixHQUFHMUUsU0FBUzZFLGdCQUFnQixHQUFHO1lBRS9CLElBQUksQ0FBQzdELGdCQUFnQixDQUFDQyxHQUFHLENBQUMsR0FBNkJ1RCxPQUExQnBELGFBQWFaLEVBQUUsRUFBQyxZQUF3QixPQUFkZ0UsZ0JBQWlCM0Q7UUFDMUU7UUFFQTZEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEakMsMkJBQTJCdEIsY0FBc0IsRUFBRTJELFNBQWtCLEVBQVE7UUFDM0UsTUFBTTFELGVBQWUsSUFBSSxDQUFDTyxlQUFlLENBQUNvRCxHQUFHLENBQUM1RDtRQUM5QyxJQUFJLENBQUNDLGNBQWM7UUFFbkJBLGFBQWFLLFFBQVEsR0FBRztRQUN4QixJQUFJLENBQUNFLGVBQWUsQ0FBQ3FELE1BQU0sQ0FBQzdEO1FBRTVCLFdBQVc7UUFDWCxJQUFJLENBQUM4RCxxQkFBcUIsQ0FBQzlEO1FBRTNCLFNBQVM7UUFDVCxJQUFJLENBQUMwQixRQUFRLENBQUMsc0JBQXNCO1lBQUUxQjtZQUFnQjJEO1FBQVU7SUFDbEU7SUFFQTs7R0FFQyxHQUNELE1BQWNGLGdCQUNaeEQsWUFBa0MsRUFDbENwQixRQUEwQixFQUNYO1FBQ2YsVUFBVTtRQUNWbkIsV0FBVztZQUNULElBQUksQ0FBQ2dFLFFBQVEsQ0FBQyx5QkFBeUI7Z0JBQ3JDekI7Z0JBQ0E4RCxTQUFTLFdBQXFDLE9BQTFCOUQsYUFBYUMsWUFBWSxFQUFDO1lBQ2hEO1FBQ0YsR0FBR3JCLFNBQVNtRix5QkFBeUIsR0FBRztJQUMxQztJQUVBOztHQUVDLEdBQ0QsTUFBY3ZDLG9CQUFtQztRQUMvQyxJQUFJO1lBQ0YsYUFBYTtZQUNiLElBQUksSUFBSSxDQUFDbkYsYUFBYSxJQUFJLElBQUksQ0FBQ08sa0JBQWtCLEVBQUU7Z0JBQ2pELElBQUksQ0FBQ1AsYUFBYSxDQUFDK0IsV0FBVyxHQUFHLEVBQUUsU0FBUzs7Z0JBQzVDLE1BQU0sSUFBSSxDQUFDL0IsYUFBYSxDQUFDMkgsSUFBSTtnQkFDN0I7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQixJQUFJLElBQUksQ0FBQ3hGLHVCQUF1QixFQUFFO2dCQUNoQyxNQUFNLElBQUksQ0FBQ0EsdUJBQXVCO2dCQUNsQztZQUNGO1lBRUEsb0JBQW9CO1lBQ3BCLElBQUksQ0FBQ3lGLGlCQUFpQjtRQUN4QixFQUFFLE9BQU9wSCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxhQUFhQTtZQUMzQixtQkFBbUI7WUFDbkIsSUFBSSxDQUFDb0gsaUJBQWlCO1FBQ3hCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELG9CQUFrQztRQUNoQyxJQUFJO1lBQ0YsV0FBVztZQUNYLElBQUkscUJBQXFCbEksUUFBUTtnQkFDL0IsTUFBTW1JLFlBQVksSUFBSUMseUJBQXlCO2dCQUMvQ0QsVUFBVTFILE1BQU0sR0FBRztnQkFDbkI0SCxnQkFBZ0JuQyxLQUFLLENBQUNpQztZQUN4QjtRQUNGLEVBQUUsT0FBT3JILE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLGNBQWNGO1FBQzdCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWMrRSw2QkFBNEM7UUFDeEQsSUFBSyxJQUFJeUMsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7WUFDMUIsTUFBTSxJQUFJLENBQUM3QyxpQkFBaUI7WUFDNUIsTUFBTSxJQUFJakUsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUyxNQUFNLFVBQVU7O1FBQ25FO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELFNBQWlCOEcsS0FBYSxFQUFFQyxJQUFTLEVBQVE7UUFDL0MsSUFBSSxJQUE2QixFQUFFO1lBQ2pDeEksT0FBT3lJLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLGNBQW9CLE9BQU5ILFFBQVM7Z0JBQUVJLFFBQVFIO1lBQUs7UUFDN0U7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsc0JBQThCeEUsY0FBc0IsRUFBUTtRQUMxRCxTQUFTO1FBQ1QsTUFBTTRFLGNBQWMsSUFBSSxDQUFDL0UsZ0JBQWdCLENBQUMrRCxHQUFHLENBQUM1RDtRQUM5QyxJQUFJNEUsYUFBYTtZQUNmQyxhQUFhRDtZQUNiLElBQUksQ0FBQy9FLGdCQUFnQixDQUFDZ0UsTUFBTSxDQUFDN0Q7UUFDL0I7UUFFQSxZQUFZO1FBQ1osS0FBSyxNQUFNLENBQUM4RSxLQUFLcEYsUUFBUSxJQUFJLElBQUksQ0FBQ0csZ0JBQWdCLENBQUNrRixPQUFPLEdBQUk7WUFDNUQsSUFBSUQsSUFBSUUsVUFBVSxDQUFDLEdBQWtCLE9BQWZoRixnQkFBZSxjQUFZO2dCQUMvQzZFLGFBQWFuRjtnQkFDYixJQUFJLENBQUNHLGdCQUFnQixDQUFDZ0UsTUFBTSxDQUFDaUI7WUFDL0I7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCx5QkFBaUM3RixJQUFZLEVBQVE7UUFDbkQsTUFBTSxDQUFDZ0csT0FBT0MsUUFBUSxHQUFHakcsS0FBS2tHLEtBQUssQ0FBQyxLQUFLQyxHQUFHLENBQUNDO1FBQzdDLE1BQU16RixNQUFNLElBQUlMO1FBQ2hCLE1BQU0rRixZQUFZLElBQUkvRjtRQUV0QitGLFVBQVVDLFFBQVEsQ0FBQ04sT0FBT0MsU0FBUyxHQUFHO1FBRXRDLGVBQWU7UUFDZixJQUFJSSxhQUFhMUYsS0FBSztZQUNwQjBGLFVBQVVFLE9BQU8sQ0FBQ0YsVUFBVUcsT0FBTyxLQUFLO1FBQzFDO1FBRUEsT0FBT0g7SUFDVDtJQUVBOztHQUVDLEdBQ0RJLHFCQUEyQjtRQUN6QixJQUFJLENBQUNsRixlQUFlLENBQUNtRixLQUFLO1FBRTFCLEtBQUssTUFBTWpHLFdBQVcsSUFBSSxDQUFDRyxnQkFBZ0IsQ0FBQytGLE1BQU0sR0FBSTtZQUNwRGYsYUFBYW5GO1FBQ2Y7UUFDQSxJQUFJLENBQUNHLGdCQUFnQixDQUFDOEYsS0FBSztJQUM3QjtJQUVBOztHQUVDLEdBQ0RFLGVBQWV6RyxVQUFrQixFQUFRO1FBQ3ZDLGVBQWU7UUFDZixLQUFLLE1BQU0sQ0FBQ0MsSUFBSVksYUFBYSxJQUFJLElBQUksQ0FBQ08sZUFBZSxDQUFDdUUsT0FBTyxHQUFJO1lBQy9ELElBQUk5RSxhQUFhYixVQUFVLEtBQUtBLFlBQVk7Z0JBQzFDLElBQUksQ0FBQ29CLGVBQWUsQ0FBQ3FELE1BQU0sQ0FBQ3hFO2dCQUM1QixJQUFJLENBQUN5RSxxQkFBcUIsQ0FBQ3pFO1lBQzdCO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0R5RyxxQkFBNkM7UUFDM0MsT0FBT0MsTUFBTUMsSUFBSSxDQUFDLElBQUksQ0FBQ3hGLGVBQWUsQ0FBQ29GLE1BQU07SUFDL0M7SUFFQTs7R0FFQyxHQUNESyw0QkFBb0Q7UUFDbEQsT0FBTyxJQUFJLENBQUNoSyxzQkFBc0I7SUFDcEM7SUFua0JBaUssYUFBYzthQU5OMUYsa0JBQWtCLElBQUkyRjthQUN0QnRHLG1CQUFtQixJQUFJc0c7YUFDdkJsSyx5QkFBaUQ7YUFDakRLLGdCQUF5QzthQUN6Q08scUJBQXFCO2FBMkdyQjRCLDBCQUF3RDtRQXhHOUQsSUFBSSxJQUE2QixFQUFFO1lBQ2pDLElBQUksQ0FBQzFDLHVCQUF1QjtZQUM1QixJQUFJLENBQUNNLGVBQWU7UUFDdEI7SUFDRjtBQStqQkY7QUFFQSxtQkFBbUI7QUFDbkIsSUFBSStKLDhCQUEwRDtBQUV2RCxNQUFNQyx5QkFBeUI7SUFDcEMsSUFBSSxLQUE2QixFQUFFLEVBV2xDO0lBRUQsSUFBSSxDQUFDRCw2QkFBNkI7UUFDaENBLDhCQUE4QixJQUFJdEs7SUFDcEM7SUFFQSxPQUFPc0s7QUFDVCxFQUFDO0FBRUQsWUFBWTtBQUNMLE1BQU1FLHNCQUFzQixLQUE2QixHQUFHRCwyQkFBMkIsQ0FBSSxDQUFPIiwic291cmNlcyI6WyJFOlxcY2FyZWJhb1xcY2FyZWFwcC13ZWJcXHNyY1xcbGliXFxub3RpZmljYXRpb24tc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzcGVlY2hTZXJ2aWNlIH0gZnJvbSAnLi9zcGVlY2gtc2VydmljZSdcbmltcG9ydCB0eXBlIHsgTWVkaWNpbmVSZW1pbmRlciwgUmVtaW5kZXJTZXR0aW5ncywgR3VhcmRpYW5Db250YWN0IH0gZnJvbSAnQC90eXBlcydcblxuZXhwb3J0IGludGVyZmFjZSBSZW1pbmRlck5vdGlmaWNhdGlvbiB7XG4gIGlkOiBzdHJpbmdcbiAgcmVtaW5kZXJJZDogc3RyaW5nXG4gIG1lZGljaW5lTmFtZTogc3RyaW5nXG4gIGRvc2FnZTogc3RyaW5nXG4gIHVzYWdlPzogc3RyaW5nXG4gIHNjaGVkdWxlZFRpbWU6IHN0cmluZ1xuICBsZXZlbDogMSB8IDIgfCAzIC8vIOaPkOmGkue6p+WIq1xuICBpc0FjdGl2ZTogYm9vbGVhblxuICBjcmVhdGVkQXQ6IERhdGVcbn1cblxuZXhwb3J0IGNsYXNzIE5vdGlmaWNhdGlvblNlcnZpY2Uge1xuICBwcml2YXRlIGFjdGl2ZVJlbWluZGVycyA9IG5ldyBNYXA8c3RyaW5nLCBSZW1pbmRlck5vdGlmaWNhdGlvbj4oKVxuICBwcml2YXRlIHJlbWluZGVyVGltZW91dHMgPSBuZXcgTWFwPHN0cmluZywgTm9kZUpTLlRpbWVvdXQ+KClcbiAgcHJpdmF0ZSBub3RpZmljYXRpb25QZXJtaXNzaW9uOiBOb3RpZmljYXRpb25QZXJtaXNzaW9uID0gJ2RlZmF1bHQnXG4gIHByaXZhdGUgcmVtaW5kZXJBdWRpbzogSFRNTEF1ZGlvRWxlbWVudCB8IG51bGwgPSBudWxsXG4gIHByaXZhdGUgaXNBdWRpb0luaXRpYWxpemVkID0gZmFsc2VcblxuICBjb25zdHJ1Y3RvcigpIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHRoaXMuaW5pdGlhbGl6ZU5vdGlmaWNhdGlvbnMoKVxuICAgICAgdGhpcy5pbml0aWFsaXplQXVkaW8oKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDliJ3lp4vljJbpgJrnn6XmnYPpmZBcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgaW5pdGlhbGl6ZU5vdGlmaWNhdGlvbnMoKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICdOb3RpZmljYXRpb24nIGluIHdpbmRvdykge1xuICAgICAgdGhpcy5ub3RpZmljYXRpb25QZXJtaXNzaW9uID0gTm90aWZpY2F0aW9uLnBlcm1pc3Npb25cblxuICAgICAgaWYgKHRoaXMubm90aWZpY2F0aW9uUGVybWlzc2lvbiA9PT0gJ2RlZmF1bHQnKSB7XG4gICAgICAgIHRoaXMubm90aWZpY2F0aW9uUGVybWlzc2lvbiA9IGF3YWl0IE5vdGlmaWNhdGlvbi5yZXF1ZXN0UGVybWlzc2lvbigpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWIneWni+WMlumfs+mikeezu+e7n1xuICAgKi9cbiAgcHJpdmF0ZSBpbml0aWFsaXplQXVkaW8oKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICAvLyDpooTliqDovb3mj5DphpLpn7PmlYhcbiAgICAgIHRoaXMucmVtaW5kZXJBdWRpbyA9IG5ldyBBdWRpbygpXG4gICAgICB0aGlzLnJlbWluZGVyQXVkaW8ucHJlbG9hZCA9ICdhdXRvJ1xuICAgICAgdGhpcy5yZW1pbmRlckF1ZGlvLnZvbHVtZSA9IDAuN1xuXG4gICAgICAvLyDlsJ3or5XliqDovb3lpJrnp43pn7PmlYjmoLzlvI9cbiAgICAgIGNvbnN0IGF1ZGlvU291cmNlcyA9IFtcbiAgICAgICAgJy9zb3VuZHMvcmVtaW5kZXIubXAzJyxcbiAgICAgICAgJy9zb3VuZHMvcmVtaW5kZXIud2F2JyxcbiAgICAgICAgJy9zb3VuZHMvcmVtaW5kZXIub2dnJ1xuICAgICAgXVxuXG4gICAgICAvLyDkvb/nlKjnrKzkuIDkuKrlj6/nlKjnmoTpn7PpopHmoLzlvI9cbiAgICAgIGZvciAoY29uc3Qgc3JjIG9mIGF1ZGlvU291cmNlcykge1xuICAgICAgICB0aGlzLnJlbWluZGVyQXVkaW8uc3JjID0gc3JjXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOayoeaciemfs+mikeaWh+S7tu+8jOWIm+W7uuWQiOaIkOmfs+aViFxuICAgICAgaWYgKCF0aGlzLnJlbWluZGVyQXVkaW8uc3JjKSB7XG4gICAgICAgIHRoaXMuY3JlYXRlU3ludGhldGljUmVtaW5kZXJTb3VuZCgpXG4gICAgICB9XG5cbiAgICAgIHRoaXMuaXNBdWRpb0luaXRpYWxpemVkID0gdHJ1ZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+mfs+mikeWIneWni+WMluWksei0pe+8jOWwhuS9v+eUqOWQiOaIkOmfs+aViDonLCBlcnJvcilcbiAgICAgIHRoaXMuY3JlYXRlU3ludGhldGljUmVtaW5kZXJTb3VuZCgpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWIm+W7uuWQiOaIkOaPkOmGkumfs+aViFxuICAgKi9cbiAgcHJpdmF0ZSBjcmVhdGVTeW50aGV0aWNSZW1pbmRlclNvdW5kKCkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgLy8g5L2/55SoV2ViIEF1ZGlvIEFQSeWIm+W7uuWQiOaIkOmfs+aViFxuICAgICAgY29uc3QgYXVkaW9Db250ZXh0ID0gbmV3ICh3aW5kb3cuQXVkaW9Db250ZXh0IHx8ICh3aW5kb3cgYXMgYW55KS53ZWJraXRBdWRpb0NvbnRleHQpKClcblxuICAgICAgY29uc3QgY3JlYXRlQmVlcCA9IChmcmVxdWVuY3k6IG51bWJlciwgZHVyYXRpb246IG51bWJlciwgZGVsYXk6IG51bWJlciA9IDApID0+IHtcbiAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlPHZvaWQ+KChyZXNvbHZlKSA9PiB7XG4gICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBvc2NpbGxhdG9yID0gYXVkaW9Db250ZXh0LmNyZWF0ZU9zY2lsbGF0b3IoKVxuICAgICAgICAgICAgY29uc3QgZ2Fpbk5vZGUgPSBhdWRpb0NvbnRleHQuY3JlYXRlR2FpbigpXG5cbiAgICAgICAgICAgIG9zY2lsbGF0b3IuY29ubmVjdChnYWluTm9kZSlcbiAgICAgICAgICAgIGdhaW5Ob2RlLmNvbm5lY3QoYXVkaW9Db250ZXh0LmRlc3RpbmF0aW9uKVxuXG4gICAgICAgICAgICBvc2NpbGxhdG9yLmZyZXF1ZW5jeS52YWx1ZSA9IGZyZXF1ZW5jeVxuICAgICAgICAgICAgb3NjaWxsYXRvci50eXBlID0gJ3NpbmUnXG5cbiAgICAgICAgICAgIGdhaW5Ob2RlLmdhaW4uc2V0VmFsdWVBdFRpbWUoMC4zLCBhdWRpb0NvbnRleHQuY3VycmVudFRpbWUpXG4gICAgICAgICAgICBnYWluTm9kZS5nYWluLmV4cG9uZW50aWFsUmFtcFRvVmFsdWVBdFRpbWUoMC4wMSwgYXVkaW9Db250ZXh0LmN1cnJlbnRUaW1lICsgZHVyYXRpb24pXG5cbiAgICAgICAgICAgIG9zY2lsbGF0b3Iuc3RhcnQoYXVkaW9Db250ZXh0LmN1cnJlbnRUaW1lKVxuICAgICAgICAgICAgb3NjaWxsYXRvci5zdG9wKGF1ZGlvQ29udGV4dC5jdXJyZW50VGltZSArIGR1cmF0aW9uKVxuXG4gICAgICAgICAgICBzZXRUaW1lb3V0KHJlc29sdmUsIGR1cmF0aW9uICogMTAwMClcbiAgICAgICAgICB9LCBkZWxheSlcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8g5Yib5bu66Ieq5a6a5LmJ5o+Q6YaS6Z+z5pWI5pKt5pS+5Ye95pWwXG4gICAgICB0aGlzLnBsYXlDdXN0b21SZW1pbmRlclNvdW5kID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IGNyZWF0ZUJlZXAoODAwLCAwLjIsIDApICAgIC8vIOesrOS4gOWjsFxuICAgICAgICAgIGF3YWl0IGNyZWF0ZUJlZXAoMTAwMCwgMC4yLCAxMDApIC8vIOesrOS6jOWjsFxuICAgICAgICAgIGF3YWl0IGNyZWF0ZUJlZXAoODAwLCAwLjMsIDIwMCkgIC8vIOesrOS4ieWjsFxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aSreaUvuWQiOaIkOmfs+aViOWksei0pTonLCBlcnJvcilcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ1dlYiBBdWRpbyBBUEnkuI3lj6/nlKg6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBwbGF5Q3VzdG9tUmVtaW5kZXJTb3VuZDogKCgpID0+IFByb21pc2U8dm9pZD4pIHwgbnVsbCA9IG51bGxcblxuICAvKipcbiAgICog6K+35rGC6YCa55+l5p2D6ZmQXG4gICAqL1xuICBhc3luYyByZXF1ZXN0Tm90aWZpY2F0aW9uUGVybWlzc2lvbigpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICBpZiAoISgnTm90aWZpY2F0aW9uJyBpbiB3aW5kb3cpKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG5cbiAgICBpZiAoTm90aWZpY2F0aW9uLnBlcm1pc3Npb24gPT09ICdncmFudGVkJykge1xuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG5cbiAgICBjb25zdCBwZXJtaXNzaW9uID0gYXdhaXQgTm90aWZpY2F0aW9uLnJlcXVlc3RQZXJtaXNzaW9uKClcbiAgICB0aGlzLm5vdGlmaWNhdGlvblBlcm1pc3Npb24gPSBwZXJtaXNzaW9uXG4gICAgcmV0dXJuIHBlcm1pc3Npb24gPT09ICdncmFudGVkJ1xuICB9XG5cbiAgLyoqXG4gICAqIOWIm+W7uueUqOiNr+aPkOmGklxuICAgKi9cbiAgc2NoZWR1bGVSZW1pbmRlcihcbiAgICByZW1pbmRlcjogTWVkaWNpbmVSZW1pbmRlcixcbiAgICBzZXR0aW5nczogUmVtaW5kZXJTZXR0aW5ncyxcbiAgICBvbkNvbmZpcm06IChyZW1pbmRlcklkOiBzdHJpbmcsIGNvbmZpcm1lZDogYm9vbGVhbikgPT4gdm9pZFxuICApOiB2b2lkIHtcbiAgICByZW1pbmRlci5zY2hlZHVsZWRUaW1lcy5mb3JFYWNoKHRpbWUgPT4ge1xuICAgICAgY29uc3Qgc2NoZWR1bGVkRGF0ZVRpbWUgPSB0aGlzLmdldE5leHRTY2hlZHVsZWREYXRlVGltZSh0aW1lKVxuICAgICAgY29uc3QgcmVtaW5kZXJJZCA9IGAke3JlbWluZGVyLmlkfS0ke3RpbWV9YFxuICAgICAgXG4gICAgICAvLyDorqHnrpfnrKzkuIDnuqfmj5DphpLml7bpl7RcbiAgICAgIGNvbnN0IGZpcnN0UmVtaW5kZXJUaW1lID0gbmV3IERhdGUoc2NoZWR1bGVkRGF0ZVRpbWUuZ2V0VGltZSgpIC0gc2V0dGluZ3MuZmlyc3RSZW1pbmRlck1pbnV0ZXMgKiA2MDAwMClcbiAgICAgIFxuICAgICAgY29uc3QgdGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0aGlzLnRyaWdnZXJSZW1pbmRlcihyZW1pbmRlciwgdGltZSwgc2V0dGluZ3MsIG9uQ29uZmlybSlcbiAgICAgIH0sIGZpcnN0UmVtaW5kZXJUaW1lLmdldFRpbWUoKSAtIERhdGUubm93KCkpXG5cbiAgICAgIHRoaXMucmVtaW5kZXJUaW1lb3V0cy5zZXQocmVtaW5kZXJJZCwgdGltZW91dClcbiAgICB9KVxuICB9XG5cbiAgLyoqXG4gICAqIOinpuWPkeaPkOmGklxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyB0cmlnZ2VyUmVtaW5kZXIoXG4gICAgcmVtaW5kZXI6IE1lZGljaW5lUmVtaW5kZXIsXG4gICAgc2NoZWR1bGVkVGltZTogc3RyaW5nLFxuICAgIHNldHRpbmdzOiBSZW1pbmRlclNldHRpbmdzLFxuICAgIG9uQ29uZmlybTogKHJlbWluZGVySWQ6IHN0cmluZywgY29uZmlybWVkOiBib29sZWFuKSA9PiB2b2lkXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IG5vdGlmaWNhdGlvbklkID0gYCR7cmVtaW5kZXIuaWR9LSR7c2NoZWR1bGVkVGltZX0tJHtEYXRlLm5vdygpfWBcbiAgICBcbiAgICBjb25zdCBub3RpZmljYXRpb246IFJlbWluZGVyTm90aWZpY2F0aW9uID0ge1xuICAgICAgaWQ6IG5vdGlmaWNhdGlvbklkLFxuICAgICAgcmVtaW5kZXJJZDogcmVtaW5kZXIuaWQsXG4gICAgICBtZWRpY2luZU5hbWU6IHJlbWluZGVyLm1lZGljaW5lTmFtZSxcbiAgICAgIGRvc2FnZTogcmVtaW5kZXIuZG9zYWdlLFxuICAgICAgdXNhZ2U6IHJlbWluZGVyLnVzYWdlLFxuICAgICAgc2NoZWR1bGVkVGltZSxcbiAgICAgIGxldmVsOiAxLFxuICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKClcbiAgICB9XG5cbiAgICB0aGlzLmFjdGl2ZVJlbWluZGVycy5zZXQobm90aWZpY2F0aW9uSWQsIG5vdGlmaWNhdGlvbilcblxuICAgIC8vIOesrOS4gOe6p+aPkOmGklxuICAgIGF3YWl0IHRoaXMuc2hvd0ZpcnN0TGV2ZWxSZW1pbmRlcihub3RpZmljYXRpb24sIHNldHRpbmdzKVxuXG4gICAgLy8g6K6+572u6YeN5aSN5o+Q6YaSXG4gICAgdGhpcy5zY2hlZHVsZVJlcGVhdGVkUmVtaW5kZXJzKG5vdGlmaWNhdGlvbiwgc2V0dGluZ3MsIG9uQ29uZmlybSlcbiAgfVxuXG4gIC8qKlxuICAgKiDnrKzkuIDnuqfmj5DphpLvvJrlvLnnqpcgKyDlo7Dpn7NcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgc2hvd0ZpcnN0TGV2ZWxSZW1pbmRlcihcbiAgICBub3RpZmljYXRpb246IFJlbWluZGVyTm90aWZpY2F0aW9uLFxuICAgIHNldHRpbmdzOiBSZW1pbmRlclNldHRpbmdzXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIC8vIOa1j+iniOWZqOmAmuefpVxuICAgIGlmIChzZXR0aW5ncy5ub3RpZmljYXRpb25FbmFibGVkICYmIHRoaXMubm90aWZpY2F0aW9uUGVybWlzc2lvbiA9PT0gJ2dyYW50ZWQnKSB7XG4gICAgICBjb25zdCBicm93c2VyTm90aWZpY2F0aW9uID0gbmV3IE5vdGlmaWNhdGlvbihg55So6I2v5o+Q6YaS77yaJHtub3RpZmljYXRpb24ubWVkaWNpbmVOYW1lfWAsIHtcbiAgICAgICAgYm9keTogYOivt+acjeeUqCAke25vdGlmaWNhdGlvbi5kb3NhZ2V9YCxcbiAgICAgICAgaWNvbjogJy9pY29ucy9tZWRpY2luZS5wbmcnLFxuICAgICAgICBiYWRnZTogJy9pY29ucy9iYWRnZS5wbmcnLFxuICAgICAgICB0YWc6IG5vdGlmaWNhdGlvbi5pZCxcbiAgICAgICAgcmVxdWlyZUludGVyYWN0aW9uOiB0cnVlLFxuICAgICAgICBhY3Rpb25zOiBbXG4gICAgICAgICAgeyBhY3Rpb246ICdjb25maXJtJywgdGl0bGU6ICflt7LmnI3oja8nIH0sXG4gICAgICAgICAgeyBhY3Rpb246ICdzbm9vemUnLCB0aXRsZTogJ+eojeWQjuaPkOmGkicgfVxuICAgICAgICBdXG4gICAgICB9KVxuXG4gICAgICBicm93c2VyTm90aWZpY2F0aW9uLm9uY2xpY2sgPSAoKSA9PiB7XG4gICAgICAgIHRoaXMuaGFuZGxlUmVtaW5kZXJDb25maXJtYXRpb24obm90aWZpY2F0aW9uLmlkLCB0cnVlKVxuICAgICAgICBicm93c2VyTm90aWZpY2F0aW9uLmNsb3NlKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDlo7Dpn7Pmj5DphpJcbiAgICBpZiAoc2V0dGluZ3Muc291bmRFbmFibGVkKSB7XG4gICAgICBhd2FpdCB0aGlzLnBsYXlSZW1pbmRlclNvdW5kKClcbiAgICB9XG5cbiAgICAvLyDpobXpnaLlvLnnqpfvvIjpgJrov4fkuovku7bpgJrnn6VVSee7hOS7tu+8iVxuICAgIHRoaXMubm90aWZ5VUkoJ3JlbWluZGVyLXBvcHVwJywgbm90aWZpY2F0aW9uKVxuICB9XG5cbiAgLyoqXG4gICAqIOesrOS6jOe6p+aPkOmGku+8muWinuWKoOivremfs+aSreaKpeWSjOW8uuWMlumfs+aViFxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBzaG93U2Vjb25kTGV2ZWxSZW1pbmRlcihcbiAgICBub3RpZmljYXRpb246IFJlbWluZGVyTm90aWZpY2F0aW9uLFxuICAgIHNldHRpbmdzOiBSZW1pbmRlclNldHRpbmdzXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIG5vdGlmaWNhdGlvbi5sZXZlbCA9IDJcblxuICAgIC8vIOa1j+iniOWZqOmAmuefpe+8iOabtOe0p+aApe+8iVxuICAgIGlmIChzZXR0aW5ncy5ub3RpZmljYXRpb25FbmFibGVkICYmIHRoaXMubm90aWZpY2F0aW9uUGVybWlzc2lvbiA9PT0gJ2dyYW50ZWQnKSB7XG4gICAgICBjb25zdCBicm93c2VyTm90aWZpY2F0aW9uID0gbmV3IE5vdGlmaWNhdGlvbihg4pqg77iPIOmHjeimgeaPkOmGku+8miR7bm90aWZpY2F0aW9uLm1lZGljaW5lTmFtZX1gLCB7XG4gICAgICAgIGJvZHk6IGDor7fnq4vljbPmnI3nlKggJHtub3RpZmljYXRpb24uZG9zYWdlfWAsXG4gICAgICAgIGljb246ICcvaWNvbnMvbWVkaWNpbmUtdXJnZW50LnBuZycsXG4gICAgICAgIGJhZGdlOiAnL2ljb25zL2JhZGdlLXVyZ2VudC5wbmcnLFxuICAgICAgICB0YWc6IG5vdGlmaWNhdGlvbi5pZCxcbiAgICAgICAgcmVxdWlyZUludGVyYWN0aW9uOiB0cnVlLFxuICAgICAgICBzaWxlbnQ6IGZhbHNlLFxuICAgICAgICBhY3Rpb25zOiBbXG4gICAgICAgICAgeyBhY3Rpb246ICdjb25maXJtJywgdGl0bGU6ICflt7LmnI3oja8nIH0sXG4gICAgICAgICAgeyBhY3Rpb246ICdzbm9vemUnLCB0aXRsZTogJ+eojeWQjuaPkOmGkicgfVxuICAgICAgICBdXG4gICAgICB9KVxuXG4gICAgICBicm93c2VyTm90aWZpY2F0aW9uLm9uY2xpY2sgPSAoKSA9PiB7XG4gICAgICAgIHRoaXMuaGFuZGxlUmVtaW5kZXJDb25maXJtYXRpb24obm90aWZpY2F0aW9uLmlkLCB0cnVlKVxuICAgICAgICBicm93c2VyTm90aWZpY2F0aW9uLmNsb3NlKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDlvLrljJbpn7PmlYjmj5DphpJcbiAgICBpZiAoc2V0dGluZ3Muc291bmRFbmFibGVkKSB7XG4gICAgICBhd2FpdCB0aGlzLnBsYXlJbnRlbnNpdmVSZW1pbmRlclNvdW5kKClcbiAgICB9XG5cbiAgICAvLyDor63pn7Pmkq3miqVcbiAgICBpZiAoc2V0dGluZ3Mudm9pY2VFbmFibGVkICYmIHNwZWVjaFNlcnZpY2UuaXNTcGVlY2hTeW50aGVzaXNTdXBwb3J0ZWQoKSkge1xuICAgICAgY29uc3Qgc3BlZWNoVGV4dCA9IHNwZWVjaFNlcnZpY2UuZ2VuZXJhdGVSZW1pbmRlclNwZWVjaChcbiAgICAgICAgbm90aWZpY2F0aW9uLm1lZGljaW5lTmFtZSxcbiAgICAgICAgbm90aWZpY2F0aW9uLmRvc2FnZSxcbiAgICAgICAgbm90aWZpY2F0aW9uLnVzYWdlXG4gICAgICApXG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHNwZWVjaFNlcnZpY2Uuc3BlYWsoeyB0ZXh0OiBzcGVlY2hUZXh0IH0pXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfor63pn7Pmkq3miqXlpLHotKU6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g6aG16Z2i5by556qX77yI5pu05pi+55y855qE5qC35byP77yJXG4gICAgdGhpcy5ub3RpZnlVSSgncmVtaW5kZXItcG9wdXAtdXJnZW50Jywgbm90aWZpY2F0aW9uKVxuICB9XG5cbiAgLyoqXG4gICAqIOesrOS4iee6p+aPkOmGku+8muehruiupOaYr+WQpuW3suacjeiNr++8iOacgOmrmOe6p+WIq++8iVxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBzaG93VGhpcmRMZXZlbFJlbWluZGVyKFxuICAgIG5vdGlmaWNhdGlvbjogUmVtaW5kZXJOb3RpZmljYXRpb24sXG4gICAgc2V0dGluZ3M6IFJlbWluZGVyU2V0dGluZ3NcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgbm90aWZpY2F0aW9uLmxldmVsID0gM1xuXG4gICAgLy8g5pyA6auY57qn5Yir5rWP6KeI5Zmo6YCa55+lXG4gICAgaWYgKHNldHRpbmdzLm5vdGlmaWNhdGlvbkVuYWJsZWQgJiYgdGhpcy5ub3RpZmljYXRpb25QZXJtaXNzaW9uID09PSAnZ3JhbnRlZCcpIHtcbiAgICAgIGNvbnN0IGJyb3dzZXJOb3RpZmljYXRpb24gPSBuZXcgTm90aWZpY2F0aW9uKGDwn5qoIOe0p+aApeaPkOmGku+8miR7bm90aWZpY2F0aW9uLm1lZGljaW5lTmFtZX1gLCB7XG4gICAgICAgIGJvZHk6IGDmgqjlj6/og73lv5jorrDmnI3oja/kuobvvIHor7fnoa7orqTmmK/lkKblt7LmnI3nlKggJHtub3RpZmljYXRpb24uZG9zYWdlfWAsXG4gICAgICAgIGljb246ICcvaWNvbnMvbWVkaWNpbmUtZW1lcmdlbmN5LnBuZycsXG4gICAgICAgIGJhZGdlOiAnL2ljb25zL2JhZGdlLWVtZXJnZW5jeS5wbmcnLFxuICAgICAgICB0YWc6IG5vdGlmaWNhdGlvbi5pZCxcbiAgICAgICAgcmVxdWlyZUludGVyYWN0aW9uOiB0cnVlLFxuICAgICAgICBzaWxlbnQ6IGZhbHNlLFxuICAgICAgICB2aWJyYXRlOiBbMjAwLCAxMDAsIDIwMF0sIC8vIOmch+WKqOaooeW8j1xuICAgICAgICBhY3Rpb25zOiBbXG4gICAgICAgICAgeyBhY3Rpb246ICdjb25maXJtJywgdGl0bGU6ICflt7LmnI3oja8nIH0sXG4gICAgICAgICAgeyBhY3Rpb246ICdtaXNzZWQnLCB0aXRsZTogJ+W/mOiusOS6hicgfSxcbiAgICAgICAgICB7IGFjdGlvbjogJ3Nub296ZScsIHRpdGxlOiAn56iN5ZCO56Gu6K6kJyB9XG4gICAgICAgIF1cbiAgICAgIH0pXG5cbiAgICAgIGJyb3dzZXJOb3RpZmljYXRpb24ub25jbGljayA9ICgpID0+IHtcbiAgICAgICAgdGhpcy5oYW5kbGVSZW1pbmRlckNvbmZpcm1hdGlvbihub3RpZmljYXRpb24uaWQsIHRydWUpXG4gICAgICAgIGJyb3dzZXJOb3RpZmljYXRpb24uY2xvc2UoKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOi/nue7reW8uuWMlumfs+aViFxuICAgIGlmIChzZXR0aW5ncy5zb3VuZEVuYWJsZWQpIHtcbiAgICAgIGF3YWl0IHRoaXMucGxheUludGVuc2l2ZVJlbWluZGVyU291bmQoKVxuICAgICAgLy8g6Ze06ZqU5ZCO5YaN5qyh5pKt5pS+XG4gICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHtcbiAgICAgICAgYXdhaXQgdGhpcy5wbGF5SW50ZW5zaXZlUmVtaW5kZXJTb3VuZCgpXG4gICAgICB9LCAyMDAwKVxuICAgIH1cblxuICAgIC8vIOivoumXruaYr+WQpuW3suacjeiNr1xuICAgIGNvbnN0IGNvbmZpcm1hdGlvblRleHQgPSBg6YeN6KaB5o+Q6YaS77yB5oKo5piv5ZCm5bey57uP5pyN55So5LqGJHtub3RpZmljYXRpb24ubWVkaWNpbmVOYW1lfe+8n+WmguaenOW/mOiusOS6hu+8jOivt+eri+WNs+acjeeUqOOAgmBcblxuICAgIGlmIChzZXR0aW5ncy52b2ljZUVuYWJsZWQgJiYgc3BlZWNoU2VydmljZS5pc1NwZWVjaFN5bnRoZXNpc1N1cHBvcnRlZCgpKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBzcGVlY2hTZXJ2aWNlLnNwZWFrKHtcbiAgICAgICAgICB0ZXh0OiBjb25maXJtYXRpb25UZXh0LFxuICAgICAgICAgIHJhdGU6IDAuOSwgLy8g56iN5oWi55qE6K+t6YCfXG4gICAgICAgICAgcGl0Y2g6IDEuMSwgLy8g56iN6auY55qE6Z+z6LCDXG4gICAgICAgICAgdm9sdW1lOiAxLjAgLy8g5pyA5aSn6Z+z6YePXG4gICAgICAgIH0pXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfor63pn7Pmkq3miqXlpLHotKU6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g5pi+56S657Sn5oCl56Gu6K6k5a+56K+d5qGGXG4gICAgdGhpcy5ub3RpZnlVSSgnY29uZmlybWF0aW9uLWRpYWxvZy11cmdlbnQnLCBub3RpZmljYXRpb24pXG5cbiAgICAvLyDlpoLmnpzmlK/mjIHvvIzlsJ3or5Xkvb/pobXpnaLpl6rng4Hmj5DphpJcbiAgICB0aGlzLnRyaWdnZXJQYWdlRmxhc2goKVxuICB9XG5cbiAgLyoqXG4gICAqIOinpuWPkemhtemdoumXqueDgeaViOaenFxuICAgKi9cbiAgcHJpdmF0ZSB0cmlnZ2VyUGFnZUZsYXNoKCk6IHZvaWQge1xuICAgIGlmICh0eXBlb2YgZG9jdW1lbnQgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBvcmlnaW5hbFRpdGxlID0gZG9jdW1lbnQudGl0bGVcbiAgICAgIGxldCBmbGFzaENvdW50ID0gMFxuICAgICAgY29uc3QgbWF4Rmxhc2hlcyA9IDZcblxuICAgICAgY29uc3QgZmxhc2hJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgZG9jdW1lbnQudGl0bGUgPSBmbGFzaENvdW50ICUgMiA9PT0gMCA/ICfwn5qoIOeUqOiNr+aPkOmGku+8gScgOiBvcmlnaW5hbFRpdGxlXG4gICAgICAgIGZsYXNoQ291bnQrK1xuXG4gICAgICAgIGlmIChmbGFzaENvdW50ID49IG1heEZsYXNoZXMpIHtcbiAgICAgICAgICBjbGVhckludGVydmFsKGZsYXNoSW50ZXJ2YWwpXG4gICAgICAgICAgZG9jdW1lbnQudGl0bGUgPSBvcmlnaW5hbFRpdGxlXG4gICAgICAgIH1cbiAgICAgIH0sIDUwMClcblxuICAgICAgLy8g6aG16Z2i5Y+v6KeB5oCn5Y+Y5YyW5pe25YGc5q2i6Zeq54OBXG4gICAgICBjb25zdCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlID0gKCkgPT4ge1xuICAgICAgICBpZiAoIWRvY3VtZW50LmhpZGRlbikge1xuICAgICAgICAgIGNsZWFySW50ZXJ2YWwoZmxhc2hJbnRlcnZhbClcbiAgICAgICAgICBkb2N1bWVudC50aXRsZSA9IG9yaWdpbmFsVGl0bGVcbiAgICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCd2aXNpYmlsaXR5Y2hhbmdlJywgaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCd2aXNpYmlsaXR5Y2hhbmdlJywgaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign6aG16Z2i6Zeq54OB5pWI5p6c5aSx6LSlOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDorr7nva7ph43lpI3mj5DphpJcbiAgICovXG4gIHByaXZhdGUgc2NoZWR1bGVSZXBlYXRlZFJlbWluZGVycyhcbiAgICBub3RpZmljYXRpb246IFJlbWluZGVyTm90aWZpY2F0aW9uLFxuICAgIHNldHRpbmdzOiBSZW1pbmRlclNldHRpbmdzLFxuICAgIG9uQ29uZmlybTogKHJlbWluZGVySWQ6IHN0cmluZywgY29uZmlybWVkOiBib29sZWFuKSA9PiB2b2lkXG4gICk6IHZvaWQge1xuICAgIGxldCByZW1pbmRlckNvdW50ID0gMVxuICAgIGNvbnN0IG1heFJlbWluZGVycyA9IHNldHRpbmdzLm1heFJlbWluZGVyc1xuXG4gICAgY29uc3Qgc2NoZWR1bGVOZXh0ID0gKCkgPT4ge1xuICAgICAgaWYgKCF0aGlzLmFjdGl2ZVJlbWluZGVycy5oYXMobm90aWZpY2F0aW9uLmlkKSB8fCByZW1pbmRlckNvdW50ID49IG1heFJlbWluZGVycykge1xuICAgICAgICAvLyDovr7liLDmnIDlpKfmj5DphpLmrKHmlbDvvIzpgJrnn6Xnm5HmiqTkurpcbiAgICAgICAgaWYgKHJlbWluZGVyQ291bnQgPj0gbWF4UmVtaW5kZXJzKSB7XG4gICAgICAgICAgdGhpcy5ub3RpZnlHdWFyZGlhbnMobm90aWZpY2F0aW9uLCBzZXR0aW5ncylcbiAgICAgICAgfVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgY29uc3QgdGltZW91dCA9IHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgICAgICBpZiAoIXRoaXMuYWN0aXZlUmVtaW5kZXJzLmhhcyhub3RpZmljYXRpb24uaWQpKSByZXR1cm5cblxuICAgICAgICByZW1pbmRlckNvdW50KytcblxuICAgICAgICBpZiAocmVtaW5kZXJDb3VudCA9PT0gMikge1xuICAgICAgICAgIGF3YWl0IHRoaXMuc2hvd1NlY29uZExldmVsUmVtaW5kZXIobm90aWZpY2F0aW9uLCBzZXR0aW5ncylcbiAgICAgICAgfSBlbHNlIGlmIChyZW1pbmRlckNvdW50ID49IDMpIHtcbiAgICAgICAgICBhd2FpdCB0aGlzLnNob3dUaGlyZExldmVsUmVtaW5kZXIobm90aWZpY2F0aW9uLCBzZXR0aW5ncylcbiAgICAgICAgfVxuXG4gICAgICAgIHNjaGVkdWxlTmV4dCgpXG4gICAgICB9LCBzZXR0aW5ncy5yZW1pbmRlckludGVydmFsICogNjAwMDApXG5cbiAgICAgIHRoaXMucmVtaW5kZXJUaW1lb3V0cy5zZXQoYCR7bm90aWZpY2F0aW9uLmlkfS1yZXBlYXQtJHtyZW1pbmRlckNvdW50fWAsIHRpbWVvdXQpXG4gICAgfVxuXG4gICAgc2NoZWR1bGVOZXh0KClcbiAgfVxuXG4gIC8qKlxuICAgKiDlpITnkIbmj5DphpLnoa7orqRcbiAgICovXG4gIGhhbmRsZVJlbWluZGVyQ29uZmlybWF0aW9uKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcsIGNvbmZpcm1lZDogYm9vbGVhbik6IHZvaWQge1xuICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IHRoaXMuYWN0aXZlUmVtaW5kZXJzLmdldChub3RpZmljYXRpb25JZClcbiAgICBpZiAoIW5vdGlmaWNhdGlvbikgcmV0dXJuXG5cbiAgICBub3RpZmljYXRpb24uaXNBY3RpdmUgPSBmYWxzZVxuICAgIHRoaXMuYWN0aXZlUmVtaW5kZXJzLmRlbGV0ZShub3RpZmljYXRpb25JZClcblxuICAgIC8vIOa4hemZpOebuOWFs+eahOWumuaXtuWZqFxuICAgIHRoaXMuY2xlYXJSZW1pbmRlclRpbWVvdXRzKG5vdGlmaWNhdGlvbklkKVxuXG4gICAgLy8g6YCa55+lVUnmm7TmlrBcbiAgICB0aGlzLm5vdGlmeVVJKCdyZW1pbmRlci1jb25maXJtZWQnLCB7IG5vdGlmaWNhdGlvbklkLCBjb25maXJtZWQgfSlcbiAgfVxuXG4gIC8qKlxuICAgKiDpgJrnn6Xnm5HmiqTkurpcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgbm90aWZ5R3VhcmRpYW5zKFxuICAgIG5vdGlmaWNhdGlvbjogUmVtaW5kZXJOb3RpZmljYXRpb24sXG4gICAgc2V0dGluZ3M6IFJlbWluZGVyU2V0dGluZ3NcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgLy8g5bu26L+f6YCa55+l55uR5oqk5Lq6XG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICB0aGlzLm5vdGlmeVVJKCdndWFyZGlhbi1ub3RpZmljYXRpb24nLCB7XG4gICAgICAgIG5vdGlmaWNhdGlvbixcbiAgICAgICAgbWVzc2FnZTogYOaCo+iAheWPr+iDveW/mOiusOacjeeUqCR7bm90aWZpY2F0aW9uLm1lZGljaW5lTmFtZX3vvIzor7flj4rml7blhbPms6jjgIJgXG4gICAgICB9KVxuICAgIH0sIHNldHRpbmdzLmd1YXJkaWFuTm90aWZpY2F0aW9uRGVsYXkgKiA2MDAwMClcbiAgfVxuXG4gIC8qKlxuICAgKiDmkq3mlL7mj5DphpLlo7Dpn7NcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgcGxheVJlbWluZGVyU291bmQoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIOS8mOWFiOS9v+eUqOmihOWKoOi9veeahOmfs+mikVxuICAgICAgaWYgKHRoaXMucmVtaW5kZXJBdWRpbyAmJiB0aGlzLmlzQXVkaW9Jbml0aWFsaXplZCkge1xuICAgICAgICB0aGlzLnJlbWluZGVyQXVkaW8uY3VycmVudFRpbWUgPSAwIC8vIOmHjee9ruaSreaUvuS9jee9rlxuICAgICAgICBhd2FpdCB0aGlzLnJlbWluZGVyQXVkaW8ucGxheSgpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyDlpoLmnpzpooTliqDovb3pn7PpopHkuI3lj6/nlKjvvIzkvb/nlKjlkIjmiJDpn7PmlYhcbiAgICAgIGlmICh0aGlzLnBsYXlDdXN0b21SZW1pbmRlclNvdW5kKSB7XG4gICAgICAgIGF3YWl0IHRoaXMucGxheUN1c3RvbVJlbWluZGVyU291bmQoKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8g5pyA5ZCO55qE5aSH6YCJ5pa55qGI77ya566A5Y2V55qEYmVlcOmfs+aViFxuICAgICAgdGhpcy5wbGF5RmFsbGJhY2tTb3VuZCgpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aSreaUvuaPkOmGkuWjsOmfs+Wksei0pTonLCBlcnJvcilcbiAgICAgIC8vIOWmguaenOaJgOaciemfs+aViOmDveWksei0pe+8jOWwneivleWkh+mAieaWueahiFxuICAgICAgdGhpcy5wbGF5RmFsbGJhY2tTb3VuZCgpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWkh+mAiemfs+aViO+8iOezu+e7n2JlZXDvvIlcbiAgICovXG4gIHByaXZhdGUgcGxheUZhbGxiYWNrU291bmQoKTogdm9pZCB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIOS9v+eUqOezu+e7n+m7mOiupOmfs+aViFxuICAgICAgaWYgKCdzcGVlY2hTeW50aGVzaXMnIGluIHdpbmRvdykge1xuICAgICAgICBjb25zdCB1dHRlcmFuY2UgPSBuZXcgU3BlZWNoU3ludGhlc2lzVXR0ZXJhbmNlKCcnKVxuICAgICAgICB1dHRlcmFuY2Uudm9sdW1lID0gMC4xXG4gICAgICAgIHNwZWVjaFN5bnRoZXNpcy5zcGVhayh1dHRlcmFuY2UpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2Fybign5aSH6YCJ6Z+z5pWI5Lmf5peg5rOV5pKt5pS+OicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDmkq3mlL7ov57nu63mj5DphpLpn7PmlYjvvIjnlKjkuo7ph43opoHmj5DphpLvvIlcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgcGxheUludGVuc2l2ZVJlbWluZGVyU291bmQoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCAzOyBpKyspIHtcbiAgICAgIGF3YWl0IHRoaXMucGxheVJlbWluZGVyU291bmQoKVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwMCkpIC8vIOmXtOmalDUwMG1zXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOmAmuefpVVJ57uE5Lu2XG4gICAqL1xuICBwcml2YXRlIG5vdGlmeVVJKGV2ZW50OiBzdHJpbmcsIGRhdGE6IGFueSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KGBtZWRpY2F0aW9uLSR7ZXZlbnR9YCwgeyBkZXRhaWw6IGRhdGEgfSkpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOa4hemZpOaPkOmGkuWumuaXtuWZqFxuICAgKi9cbiAgcHJpdmF0ZSBjbGVhclJlbWluZGVyVGltZW91dHMobm90aWZpY2F0aW9uSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIC8vIOa4hemZpOS4u+WumuaXtuWZqFxuICAgIGNvbnN0IG1haW5UaW1lb3V0ID0gdGhpcy5yZW1pbmRlclRpbWVvdXRzLmdldChub3RpZmljYXRpb25JZClcbiAgICBpZiAobWFpblRpbWVvdXQpIHtcbiAgICAgIGNsZWFyVGltZW91dChtYWluVGltZW91dClcbiAgICAgIHRoaXMucmVtaW5kZXJUaW1lb3V0cy5kZWxldGUobm90aWZpY2F0aW9uSWQpXG4gICAgfVxuXG4gICAgLy8g5riF6Zmk6YeN5aSN5o+Q6YaS5a6a5pe25ZmoXG4gICAgZm9yIChjb25zdCBba2V5LCB0aW1lb3V0XSBvZiB0aGlzLnJlbWluZGVyVGltZW91dHMuZW50cmllcygpKSB7XG4gICAgICBpZiAoa2V5LnN0YXJ0c1dpdGgoYCR7bm90aWZpY2F0aW9uSWR9LXJlcGVhdC1gKSkge1xuICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dClcbiAgICAgICAgdGhpcy5yZW1pbmRlclRpbWVvdXRzLmRlbGV0ZShrZXkpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluS4i+asoeiuoeWIkuaXtumXtFxuICAgKi9cbiAgcHJpdmF0ZSBnZXROZXh0U2NoZWR1bGVkRGF0ZVRpbWUodGltZTogc3RyaW5nKTogRGF0ZSB7XG4gICAgY29uc3QgW2hvdXJzLCBtaW51dGVzXSA9IHRpbWUuc3BsaXQoJzonKS5tYXAoTnVtYmVyKVxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBzY2hlZHVsZWQgPSBuZXcgRGF0ZSgpXG4gICAgXG4gICAgc2NoZWR1bGVkLnNldEhvdXJzKGhvdXJzLCBtaW51dGVzLCAwLCAwKVxuICAgIFxuICAgIC8vIOWmguaenOaXtumXtOW3sui/h++8jOiuvue9ruS4uuaYjuWkqVxuICAgIGlmIChzY2hlZHVsZWQgPD0gbm93KSB7XG4gICAgICBzY2hlZHVsZWQuc2V0RGF0ZShzY2hlZHVsZWQuZ2V0RGF0ZSgpICsgMSlcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHNjaGVkdWxlZFxuICB9XG5cbiAgLyoqXG4gICAqIOWPlua2iOaJgOaciea0u+WKqOaPkOmGklxuICAgKi9cbiAgY2FuY2VsQWxsUmVtaW5kZXJzKCk6IHZvaWQge1xuICAgIHRoaXMuYWN0aXZlUmVtaW5kZXJzLmNsZWFyKClcbiAgICBcbiAgICBmb3IgKGNvbnN0IHRpbWVvdXQgb2YgdGhpcy5yZW1pbmRlclRpbWVvdXRzLnZhbHVlcygpKSB7XG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dClcbiAgICB9XG4gICAgdGhpcy5yZW1pbmRlclRpbWVvdXRzLmNsZWFyKClcbiAgfVxuXG4gIC8qKlxuICAgKiDlj5bmtojnibnlrprmj5DphpJcbiAgICovXG4gIGNhbmNlbFJlbWluZGVyKHJlbWluZGVySWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIC8vIOaJvuWIsOW5tuWIoOmZpOebuOWFs+eahOa0u+WKqOaPkOmGklxuICAgIGZvciAoY29uc3QgW2lkLCBub3RpZmljYXRpb25dIG9mIHRoaXMuYWN0aXZlUmVtaW5kZXJzLmVudHJpZXMoKSkge1xuICAgICAgaWYgKG5vdGlmaWNhdGlvbi5yZW1pbmRlcklkID09PSByZW1pbmRlcklkKSB7XG4gICAgICAgIHRoaXMuYWN0aXZlUmVtaW5kZXJzLmRlbGV0ZShpZClcbiAgICAgICAgdGhpcy5jbGVhclJlbWluZGVyVGltZW91dHMoaWQpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPlua0u+WKqOaPkOmGkuWIl+ihqFxuICAgKi9cbiAgZ2V0QWN0aXZlUmVtaW5kZXJzKCk6IFJlbWluZGVyTm90aWZpY2F0aW9uW10ge1xuICAgIHJldHVybiBBcnJheS5mcm9tKHRoaXMuYWN0aXZlUmVtaW5kZXJzLnZhbHVlcygpKVxuICB9XG5cbiAgLyoqXG4gICAqIOajgOafpemAmuefpeadg+mZkOeKtuaAgVxuICAgKi9cbiAgZ2V0Tm90aWZpY2F0aW9uUGVybWlzc2lvbigpOiBOb3RpZmljYXRpb25QZXJtaXNzaW9uIHtcbiAgICByZXR1cm4gdGhpcy5ub3RpZmljYXRpb25QZXJtaXNzaW9uXG4gIH1cbn1cblxuLy8g5bu26L+f5Yid5aeL5YyW77yM6YG/5YWN5pyN5Yqh5Zmo56uv5riy5p+T6Zeu6aKYXG5sZXQgbm90aWZpY2F0aW9uU2VydmljZUluc3RhbmNlOiBOb3RpZmljYXRpb25TZXJ2aWNlIHwgbnVsbCA9IG51bGxcblxuZXhwb3J0IGNvbnN0IGdldE5vdGlmaWNhdGlvblNlcnZpY2UgPSAoKTogTm90aWZpY2F0aW9uU2VydmljZSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xuICAgIC8vIOacjeWKoeWZqOerr+i/lOWbnuS4gOS4quepuueahOWunueOsFxuICAgIHJldHVybiB7XG4gICAgICByZXF1ZXN0Tm90aWZpY2F0aW9uUGVybWlzc2lvbjogYXN5bmMgKCkgPT4gZmFsc2UsXG4gICAgICBzY2hlZHVsZVJlbWluZGVyOiAoKSA9PiB7fSxcbiAgICAgIGNhbmNlbEFsbFJlbWluZGVyczogKCkgPT4ge30sXG4gICAgICBjYW5jZWxSZW1pbmRlcjogKCkgPT4ge30sXG4gICAgICBnZXRBY3RpdmVSZW1pbmRlcnM6ICgpID0+IFtdLFxuICAgICAgZ2V0Tm90aWZpY2F0aW9uUGVybWlzc2lvbjogKCkgPT4gJ2RlZmF1bHQnIGFzIE5vdGlmaWNhdGlvblBlcm1pc3Npb24sXG4gICAgICBoYW5kbGVSZW1pbmRlckNvbmZpcm1hdGlvbjogKCkgPT4ge31cbiAgICB9IGFzIGFueVxuICB9XG5cbiAgaWYgKCFub3RpZmljYXRpb25TZXJ2aWNlSW5zdGFuY2UpIHtcbiAgICBub3RpZmljYXRpb25TZXJ2aWNlSW5zdGFuY2UgPSBuZXcgTm90aWZpY2F0aW9uU2VydmljZSgpXG4gIH1cblxuICByZXR1cm4gbm90aWZpY2F0aW9uU2VydmljZUluc3RhbmNlXG59XG5cbi8vIOWPquWcqOWuouaIt+err+WvvOWHuuWunuS+i1xuZXhwb3J0IGNvbnN0IG5vdGlmaWNhdGlvblNlcnZpY2UgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IGdldE5vdGlmaWNhdGlvblNlcnZpY2UoKSA6IG51bGwgYXMgYW55XG4iXSwibmFtZXMiOlsic3BlZWNoU2VydmljZSIsIk5vdGlmaWNhdGlvblNlcnZpY2UiLCJpbml0aWFsaXplTm90aWZpY2F0aW9ucyIsIndpbmRvdyIsIm5vdGlmaWNhdGlvblBlcm1pc3Npb24iLCJOb3RpZmljYXRpb24iLCJwZXJtaXNzaW9uIiwicmVxdWVzdFBlcm1pc3Npb24iLCJpbml0aWFsaXplQXVkaW8iLCJyZW1pbmRlckF1ZGlvIiwiQXVkaW8iLCJwcmVsb2FkIiwidm9sdW1lIiwiYXVkaW9Tb3VyY2VzIiwic3JjIiwiY3JlYXRlU3ludGhldGljUmVtaW5kZXJTb3VuZCIsImlzQXVkaW9Jbml0aWFsaXplZCIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJhdWRpb0NvbnRleHQiLCJBdWRpb0NvbnRleHQiLCJ3ZWJraXRBdWRpb0NvbnRleHQiLCJjcmVhdGVCZWVwIiwiZnJlcXVlbmN5IiwiZHVyYXRpb24iLCJkZWxheSIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsIm9zY2lsbGF0b3IiLCJjcmVhdGVPc2NpbGxhdG9yIiwiZ2Fpbk5vZGUiLCJjcmVhdGVHYWluIiwiY29ubmVjdCIsImRlc3RpbmF0aW9uIiwidmFsdWUiLCJ0eXBlIiwiZ2FpbiIsInNldFZhbHVlQXRUaW1lIiwiY3VycmVudFRpbWUiLCJleHBvbmVudGlhbFJhbXBUb1ZhbHVlQXRUaW1lIiwic3RhcnQiLCJzdG9wIiwicGxheUN1c3RvbVJlbWluZGVyU291bmQiLCJyZXF1ZXN0Tm90aWZpY2F0aW9uUGVybWlzc2lvbiIsInNjaGVkdWxlUmVtaW5kZXIiLCJyZW1pbmRlciIsInNldHRpbmdzIiwib25Db25maXJtIiwic2NoZWR1bGVkVGltZXMiLCJmb3JFYWNoIiwidGltZSIsInNjaGVkdWxlZERhdGVUaW1lIiwiZ2V0TmV4dFNjaGVkdWxlZERhdGVUaW1lIiwicmVtaW5kZXJJZCIsImlkIiwiZmlyc3RSZW1pbmRlclRpbWUiLCJEYXRlIiwiZ2V0VGltZSIsImZpcnN0UmVtaW5kZXJNaW51dGVzIiwidGltZW91dCIsInRyaWdnZXJSZW1pbmRlciIsIm5vdyIsInJlbWluZGVyVGltZW91dHMiLCJzZXQiLCJzY2hlZHVsZWRUaW1lIiwibm90aWZpY2F0aW9uSWQiLCJub3RpZmljYXRpb24iLCJtZWRpY2luZU5hbWUiLCJkb3NhZ2UiLCJ1c2FnZSIsImxldmVsIiwiaXNBY3RpdmUiLCJjcmVhdGVkQXQiLCJhY3RpdmVSZW1pbmRlcnMiLCJzaG93Rmlyc3RMZXZlbFJlbWluZGVyIiwic2NoZWR1bGVSZXBlYXRlZFJlbWluZGVycyIsIm5vdGlmaWNhdGlvbkVuYWJsZWQiLCJicm93c2VyTm90aWZpY2F0aW9uIiwiYm9keSIsImljb24iLCJiYWRnZSIsInRhZyIsInJlcXVpcmVJbnRlcmFjdGlvbiIsImFjdGlvbnMiLCJhY3Rpb24iLCJ0aXRsZSIsIm9uY2xpY2siLCJoYW5kbGVSZW1pbmRlckNvbmZpcm1hdGlvbiIsImNsb3NlIiwic291bmRFbmFibGVkIiwicGxheVJlbWluZGVyU291bmQiLCJub3RpZnlVSSIsInNob3dTZWNvbmRMZXZlbFJlbWluZGVyIiwic2lsZW50IiwicGxheUludGVuc2l2ZVJlbWluZGVyU291bmQiLCJ2b2ljZUVuYWJsZWQiLCJpc1NwZWVjaFN5bnRoZXNpc1N1cHBvcnRlZCIsInNwZWVjaFRleHQiLCJnZW5lcmF0ZVJlbWluZGVyU3BlZWNoIiwic3BlYWsiLCJ0ZXh0Iiwic2hvd1RoaXJkTGV2ZWxSZW1pbmRlciIsInZpYnJhdGUiLCJjb25maXJtYXRpb25UZXh0IiwicmF0ZSIsInBpdGNoIiwidHJpZ2dlclBhZ2VGbGFzaCIsImRvY3VtZW50Iiwib3JpZ2luYWxUaXRsZSIsImZsYXNoQ291bnQiLCJtYXhGbGFzaGVzIiwiZmxhc2hJbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImhhbmRsZVZpc2liaWxpdHlDaGFuZ2UiLCJoaWRkZW4iLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbWluZGVyQ291bnQiLCJtYXhSZW1pbmRlcnMiLCJzY2hlZHVsZU5leHQiLCJoYXMiLCJub3RpZnlHdWFyZGlhbnMiLCJyZW1pbmRlckludGVydmFsIiwiY29uZmlybWVkIiwiZ2V0IiwiZGVsZXRlIiwiY2xlYXJSZW1pbmRlclRpbWVvdXRzIiwibWVzc2FnZSIsImd1YXJkaWFuTm90aWZpY2F0aW9uRGVsYXkiLCJwbGF5IiwicGxheUZhbGxiYWNrU291bmQiLCJ1dHRlcmFuY2UiLCJTcGVlY2hTeW50aGVzaXNVdHRlcmFuY2UiLCJzcGVlY2hTeW50aGVzaXMiLCJpIiwiZXZlbnQiLCJkYXRhIiwiZGlzcGF0Y2hFdmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwibWFpblRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJrZXkiLCJlbnRyaWVzIiwic3RhcnRzV2l0aCIsImhvdXJzIiwibWludXRlcyIsInNwbGl0IiwibWFwIiwiTnVtYmVyIiwic2NoZWR1bGVkIiwic2V0SG91cnMiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsImNhbmNlbEFsbFJlbWluZGVycyIsImNsZWFyIiwidmFsdWVzIiwiY2FuY2VsUmVtaW5kZXIiLCJnZXRBY3RpdmVSZW1pbmRlcnMiLCJBcnJheSIsImZyb20iLCJnZXROb3RpZmljYXRpb25QZXJtaXNzaW9uIiwiY29uc3RydWN0b3IiLCJNYXAiLCJub3RpZmljYXRpb25TZXJ2aWNlSW5zdGFuY2UiLCJnZXROb3RpZmljYXRpb25TZXJ2aWNlIiwibm90aWZpY2F0aW9uU2VydmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});