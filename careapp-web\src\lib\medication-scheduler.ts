import type { DailySchedule } from '@/types'

export interface MedicationRule {
  type: 'before_meal' | 'after_meal' | 'with_meal' | 'bedtime' | 'morning' | 'custom'
  meal?: 'breakfast' | 'lunch' | 'dinner'
  offsetMinutes?: number // 相对于参考时间的偏移量（分钟）
  customTime?: string // 自定义时间 HH:MM
}

export class MedicationScheduler {
  /**
   * 根据用药规则和作息时间计算用药时间
   */
  calculateMedicationTimes(
    usage: string,
    frequency: string,
    schedule: DailySchedule
  ): string[] {
    const rules = this.parseUsageToRules(usage)
    const timesPerDay = this.parseFrequency(frequency)
    
    const medicationTimes: string[] = []

    rules.forEach(rule => {
      const times = this.calculateTimesForRule(rule, schedule, timesPerDay)
      medicationTimes.push(...times)
    })

    // 去重并排序
    const uniqueTimes = [...new Set(medicationTimes)]
    return uniqueTimes.sort()
  }

  /**
   * 解析用法说明为规则
   */
  private parseUsageToRules(usage: string): MedicationRule[] {
    const rules: MedicationRule[] = []
    const lowerUsage = usage.toLowerCase()

    // 餐前用药
    if (lowerUsage.includes('餐前') || lowerUsage.includes('饭前')) {
      const minutes = this.extractMinutes(usage) || 30
      if (lowerUsage.includes('早餐') || lowerUsage.includes('早饭')) {
        rules.push({ type: 'before_meal', meal: 'breakfast', offsetMinutes: -minutes })
      } else if (lowerUsage.includes('午餐') || lowerUsage.includes('午饭')) {
        rules.push({ type: 'before_meal', meal: 'lunch', offsetMinutes: -minutes })
      } else if (lowerUsage.includes('晚餐') || lowerUsage.includes('晚饭')) {
        rules.push({ type: 'before_meal', meal: 'dinner', offsetMinutes: -minutes })
      } else {
        // 三餐前都要服用
        rules.push(
          { type: 'before_meal', meal: 'breakfast', offsetMinutes: -minutes },
          { type: 'before_meal', meal: 'lunch', offsetMinutes: -minutes },
          { type: 'before_meal', meal: 'dinner', offsetMinutes: -minutes }
        )
      }
    }
    
    // 餐后用药
    else if (lowerUsage.includes('餐后') || lowerUsage.includes('饭后')) {
      const minutes = this.extractMinutes(usage) || 60
      if (lowerUsage.includes('早餐') || lowerUsage.includes('早饭')) {
        rules.push({ type: 'after_meal', meal: 'breakfast', offsetMinutes: minutes })
      } else if (lowerUsage.includes('午餐') || lowerUsage.includes('午饭')) {
        rules.push({ type: 'after_meal', meal: 'lunch', offsetMinutes: minutes })
      } else if (lowerUsage.includes('晚餐') || lowerUsage.includes('晚饭')) {
        rules.push({ type: 'after_meal', meal: 'dinner', offsetMinutes: minutes })
      } else {
        // 三餐后都要服用
        rules.push(
          { type: 'after_meal', meal: 'breakfast', offsetMinutes: minutes },
          { type: 'after_meal', meal: 'lunch', offsetMinutes: minutes },
          { type: 'after_meal', meal: 'dinner', offsetMinutes: minutes }
        )
      }
    }
    
    // 随餐服用
    else if (lowerUsage.includes('随餐') || lowerUsage.includes('用餐时')) {
      rules.push(
        { type: 'with_meal', meal: 'breakfast', offsetMinutes: 0 },
        { type: 'with_meal', meal: 'lunch', offsetMinutes: 0 },
        { type: 'with_meal', meal: 'dinner', offsetMinutes: 0 }
      )
    }
    
    // 睡前用药
    else if (lowerUsage.includes('睡前') || lowerUsage.includes('就寝前')) {
      const minutes = this.extractMinutes(usage) || 30
      rules.push({ type: 'bedtime', offsetMinutes: -minutes })
    }
    
    // 晨起用药
    else if (lowerUsage.includes('晨起') || lowerUsage.includes('起床后')) {
      const minutes = this.extractMinutes(usage) || 30
      rules.push({ type: 'morning', offsetMinutes: minutes })
    }
    
    // 自定义时间
    else {
      const timeMatch = usage.match(/(\d{1,2}):(\d{2})/)
      if (timeMatch) {
        rules.push({ type: 'custom', customTime: `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}` })
      }
    }

    return rules.length > 0 ? rules : [{ type: 'custom', customTime: '08:00' }] // 默认早上8点
  }

  /**
   * 解析用药频次
   */
  private parseFrequency(frequency: string): number {
    const match = frequency.match(/每日(\d+)次|一日(\d+)次|(\d+)次\/日/)
    if (match) {
      return parseInt(match[1] || match[2] || match[3])
    }
    
    if (frequency.includes('每周') || frequency.includes('一周')) {
      const weekMatch = frequency.match(/每周(\d+)次|一周(\d+)次|(\d+)次\/周/)
      if (weekMatch) {
        return Math.ceil(parseInt(weekMatch[1] || weekMatch[2] || weekMatch[3]) / 7)
      }
    }
    
    return 1 // 默认每日1次
  }

  /**
   * 提取时间中的分钟数
   */
  private extractMinutes(text: string): number | null {
    const match = text.match(/(\d+)\s*分钟/)
    return match ? parseInt(match[1]) : null
  }

  /**
   * 根据规则计算具体时间
   */
  private calculateTimesForRule(
    rule: MedicationRule,
    schedule: DailySchedule,
    timesPerDay: number
  ): string[] {
    const times: string[] = []

    switch (rule.type) {
      case 'before_meal':
      case 'after_meal':
      case 'with_meal':
        if (rule.meal) {
          const mealTime = this.getMealTime(rule.meal, schedule)
          if (mealTime) {
            const adjustedTime = this.addMinutes(mealTime, rule.offsetMinutes || 0)
            times.push(adjustedTime)
          }
        }
        break

      case 'bedtime':
        const bedTime = this.addMinutes(schedule.bedTime, rule.offsetMinutes || 0)
        times.push(bedTime)
        break

      case 'morning':
        const morningTime = this.addMinutes(schedule.wakeUpTime, rule.offsetMinutes || 0)
        times.push(morningTime)
        break

      case 'custom':
        if (rule.customTime) {
          times.push(rule.customTime)
        }
        break
    }

    // 如果需要多次服药但只有一个规则，则平均分配时间
    if (times.length === 1 && timesPerDay > 1) {
      return this.distributeTimesEvenly(times[0], timesPerDay, schedule)
    }

    return times
  }

  /**
   * 获取用餐时间
   */
  private getMealTime(meal: 'breakfast' | 'lunch' | 'dinner', schedule: DailySchedule): string {
    switch (meal) {
      case 'breakfast':
        return schedule.breakfastTime
      case 'lunch':
        return schedule.lunchTime
      case 'dinner':
        return schedule.dinnerTime
    }
  }

  /**
   * 时间加减分钟
   */
  private addMinutes(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number)
    const date = new Date()
    date.setHours(hours, mins + minutes, 0, 0)
    
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  }

  /**
   * 平均分配用药时间
   */
  private distributeTimesEvenly(
    baseTime: string,
    count: number,
    schedule: DailySchedule
  ): string[] {
    if (count === 1) return [baseTime]

    const times: string[] = []
    const wakeUpMinutes = this.timeToMinutes(schedule.wakeUpTime)
    const bedTimeMinutes = this.timeToMinutes(schedule.bedTime)
    const interval = Math.floor((bedTimeMinutes - wakeUpMinutes) / count)

    for (let i = 0; i < count; i++) {
      const minutes = wakeUpMinutes + (interval * i) + Math.floor(interval / 2)
      times.push(this.minutesToTime(minutes))
    }

    return times
  }

  /**
   * 时间转换为分钟数
   */
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number)
    return hours * 60 + minutes
  }

  /**
   * 分钟数转换为时间
   */
  private minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  }

  /**
   * 验证和调整用药时间
   */
  validateAndAdjustTimes(times: string[]): string[] {
    return times
      .map(time => {
        // 确保时间格式正确
        const [hours, minutes] = time.split(':').map(Number)
        if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
          return '08:00' // 默认时间
        }
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      })
      .filter((time, index, arr) => arr.indexOf(time) === index) // 去重
      .sort() // 排序
  }
}

export const medicationScheduler = new MedicationScheduler()
