"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/app/medication/page.tsx":
/*!*************************************!*\
  !*** ./src/app/medication/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MedicationPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _store_medication__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/medication */ \"(app-pages-browser)/./src/store/medication.ts\");\n/* harmony import */ var _lib_notification_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/notification-service */ \"(app-pages-browser)/./src/lib/notification-service.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BellOff,Clock,History,Pill,Plus,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell-off.mjs\");\n/* harmony import */ var _components_medication_MedicineInputForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/medication/MedicineInputForm */ \"(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx\");\n/* harmony import */ var _components_medication_DailyScheduleForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/medication/DailyScheduleForm */ \"(app-pages-browser)/./src/components/medication/DailyScheduleForm.tsx\");\n/* harmony import */ var _components_medication_MedicineReminderList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/medication/MedicineReminderList */ \"(app-pages-browser)/./src/components/medication/MedicineReminderList.tsx\");\n/* harmony import */ var _components_medication_ReminderManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/medication/ReminderManager */ \"(app-pages-browser)/./src/components/medication/ReminderManager.tsx\");\n/* harmony import */ var _components_medication_MedicationStatistics__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/medication/MedicationStatistics */ \"(app-pages-browser)/./src/components/medication/MedicationStatistics.tsx\");\n/* harmony import */ var _components_medication_MedicationHistory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/medication/MedicationHistory */ \"(app-pages-browser)/./src/components/medication/MedicationHistory.tsx\");\n/* harmony import */ var _components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/medication/ReminderPopup */ \"(app-pages-browser)/./src/components/medication/ReminderPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 强制动态渲染\nconst dynamic = 'force-dynamic';\nfunction MedicationPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [showReminderPopup, setShowReminderPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConfirmationDialog, setShowConfirmationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [reminderSystemActive, setReminderSystemActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingReminder, setEditingReminder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, initialized, initialize } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { reminders, dailySchedule, reminderSettings, startReminderSystem, stopReminderSystem, loadDailySchedule, loadReminderSettings } = (0,_store_medication__WEBPACK_IMPORTED_MODULE_4__.useMedication)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            setMounted(true);\n            initialize();\n        }\n    }[\"MedicationPage.useEffect\"], [\n        initialize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            if (mounted && initialized && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"MedicationPage.useEffect\"], [\n        user,\n        initialized,\n        router,\n        mounted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            if (user) {\n                loadDailySchedule(user.id);\n                loadReminderSettings(user.id);\n            }\n        }\n    }[\"MedicationPage.useEffect\"], [\n        user,\n        loadDailySchedule,\n        loadReminderSettings\n    ]);\n    // 监听提醒事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicationPage.useEffect\": ()=>{\n            const handleReminderPopup = {\n                \"MedicationPage.useEffect.handleReminderPopup\": (event)=>{\n                    setShowReminderPopup(event.detail);\n                }\n            }[\"MedicationPage.useEffect.handleReminderPopup\"];\n            const handleConfirmationDialog = {\n                \"MedicationPage.useEffect.handleConfirmationDialog\": (event)=>{\n                    setShowConfirmationDialog(event.detail);\n                }\n            }[\"MedicationPage.useEffect.handleConfirmationDialog\"];\n            const handleReminderConfirmed = {\n                \"MedicationPage.useEffect.handleReminderConfirmed\": (event)=>{\n                    setShowReminderPopup(null);\n                    setShowConfirmationDialog(null);\n                }\n            }[\"MedicationPage.useEffect.handleReminderConfirmed\"];\n            window.addEventListener('medication-reminder-popup', handleReminderPopup);\n            window.addEventListener('medication-confirmation-dialog', handleConfirmationDialog);\n            window.addEventListener('medication-reminder-confirmed', handleReminderConfirmed);\n            return ({\n                \"MedicationPage.useEffect\": ()=>{\n                    window.removeEventListener('medication-reminder-popup', handleReminderPopup);\n                    window.removeEventListener('medication-confirmation-dialog', handleConfirmationDialog);\n                    window.removeEventListener('medication-reminder-confirmed', handleReminderConfirmed);\n                }\n            })[\"MedicationPage.useEffect\"];\n        }\n    }[\"MedicationPage.useEffect\"], []);\n    const handleStartReminderSystem = async ()=>{\n        if (!user) return;\n        try {\n            // 请求通知权限\n            const notificationService = (0,_lib_notification_service__WEBPACK_IMPORTED_MODULE_5__.getNotificationService)();\n            const hasPermission = await notificationService.requestNotificationPermission();\n            if (!hasPermission) {\n                alert('需要通知权限才能启用提醒系统');\n                return;\n            }\n            await startReminderSystem(user.id);\n            setReminderSystemActive(true);\n        } catch (error) {\n            console.error('启动提醒系统失败:', error);\n            alert('启动提醒系统失败，请检查设置');\n        }\n    };\n    const handleStopReminderSystem = ()=>{\n        stopReminderSystem();\n        setReminderSystemActive(false);\n    };\n    const handleReminderConfirm = (confirmed)=>{\n        if (showReminderPopup && user) {\n            // 这里可以记录用药状态\n            console.log(\"用药确认: \".concat(showReminderPopup.medicineName, \" - \").concat(confirmed ? '已服药' : '未服药'));\n        }\n        setShowReminderPopup(null);\n    };\n    const handleConfirmationSubmit = (confirmed)=>{\n        if (showConfirmationDialog && user) {\n            // 记录用药状态\n            console.log(\"用药确认: \".concat(showConfirmationDialog.medicineName, \" - \").concat(confirmed ? '已服药' : '错过'));\n        }\n        setShowConfirmationDialog(null);\n    };\n    // 测试提醒功能\n    const testReminder = (level)=>{\n        const testNotification = {\n            id: \"test-\".concat(Date.now()),\n            reminderId: 'test-reminder',\n            medicineName: '测试药物',\n            dosage: '1片',\n            usage: '餐后服用',\n            scheduledTime: new Date().toLocaleTimeString('zh-CN', {\n                hour: '2-digit',\n                minute: '2-digit'\n            }),\n            level,\n            isActive: true,\n            createdAt: new Date()\n        };\n        // 触发相应级别的提醒\n        const eventName = level === 3 ? 'confirmation-dialog-urgent' : level === 2 ? 'reminder-popup-urgent' : 'reminder-popup';\n        window.dispatchEvent(new CustomEvent(\"medication-\".concat(eventName), {\n            detail: testNotification\n        }));\n    };\n    if (!mounted || !initialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载用药提醒系统...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    const tabs = [\n        {\n            id: 'list',\n            label: '提醒列表',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'add',\n            label: '添加提醒',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: 'schedule',\n            label: '作息设置',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: 'statistics',\n            label: '用药统计',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'history',\n            label: '用药历史',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        },\n        {\n            id: 'settings',\n            label: '提醒设置',\n            icon: _barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-500 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                children: \"用药提醒系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"智能用药管理，健康生活助手\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"提醒系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: reminderSystemActive ? handleStopReminderSystem : handleStartReminderSystem,\n                                                className: \"flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors \".concat(reminderSystemActive ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'),\n                                                children: reminderSystemActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"已启用\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BellOff_Clock_History_Pill_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"已禁用\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/dashboard'),\n                                        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: \"返回主页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-8\",\n                        children: tabs.map((tab)=>{\n                            const IconComponent = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center px-4 py-4 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this),\n                                    tab.label\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    activeTab === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicineReminderList__WEBPACK_IMPORTED_MODULE_8__.MedicineReminderList, {\n                        userId: user.id,\n                        onEdit: (reminder)=>{\n                            setEditingReminder(reminder);\n                            setActiveTab('add');\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicineInputForm__WEBPACK_IMPORTED_MODULE_6__.MedicineInputForm, {\n                        userId: user.id,\n                        onSuccess: ()=>{\n                            setActiveTab('list');\n                            setEditingReminder(null);\n                        },\n                        onCancel: ()=>{\n                            setActiveTab('list');\n                            setEditingReminder(null);\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'schedule' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_DailyScheduleForm__WEBPACK_IMPORTED_MODULE_7__.DailyScheduleForm, {\n                        userId: user.id,\n                        onSuccess: ()=>{\n                        // 可以选择切换到其他标签页或显示成功消息\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'statistics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicationStatistics__WEBPACK_IMPORTED_MODULE_10__.MedicationStatistics, {\n                        userId: user.id\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_MedicationHistory__WEBPACK_IMPORTED_MODULE_11__.MedicationHistory, {\n                        userId: user.id\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                children: \"提醒设置\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-700 mb-3\",\n                                        children: \"测试提醒功能\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(1),\n                                                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700\",\n                                                children: \"测试第一级提醒（普通）\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(2),\n                                                className: \"w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700\",\n                                                children: \"测试第二级提醒（重要）\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>testReminder(3),\n                                                className: \"w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700\",\n                                                children: \"测试第三级提醒（紧急）\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"更多提醒设置功能开发中...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            showReminderPopup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_12__.ReminderPopup, {\n                notification: showReminderPopup,\n                onConfirm: handleReminderConfirm,\n                onClose: ()=>setShowReminderPopup(null)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 9\n            }, this),\n            showConfirmationDialog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderPopup__WEBPACK_IMPORTED_MODULE_12__.ConfirmationDialog, {\n                notification: showConfirmationDialog,\n                onConfirm: handleConfirmationSubmit,\n                onClose: ()=>setShowConfirmationDialog(null)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_medication_ReminderManager__WEBPACK_IMPORTED_MODULE_9__.ReminderManager, {}, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\medication\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicationPage, \"4HKtr2YmjS0dqLXuR7pouWYelUw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _store_medication__WEBPACK_IMPORTED_MODULE_4__.useMedication,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = MedicationPage;\nvar _c;\n$RefreshReg$(_c, \"MedicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/medication/page.tsx\n"));

/***/ })

});