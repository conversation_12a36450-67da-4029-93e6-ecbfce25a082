"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 重复第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n    }\n    /**\n   * 第三级提醒：确认是否已服药\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 询问是否已服药\n        const confirmationText = \"您是否已经服用了\".concat(notification.medicineName, \"？\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示确认对话框\n        this.notifyUI('confirmation-dialog', notification);\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ playReminderSound() {\n        try {\n            const audio = new Audio('/sounds/reminder.mp3');\n            audio.volume = 0.7;\n            audio.play().catch((error)=>{\n                console.error('播放提醒声音失败:', error);\n            });\n        } catch (error) {\n            console.error('创建音频对象失败:', error);\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        if (true) {\n            this.initializeNotifications();\n        }\n    }\n}\nconst notificationService = new NotificationService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});