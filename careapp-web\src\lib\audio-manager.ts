export interface AudioSettings {
  volume: number
  soundType: 'gentle' | 'standard' | 'urgent' | 'custom'
  customSoundUrl?: string
  enableVibration: boolean
  enableFlash: boolean
}

export interface SoundEffect {
  id: string
  name: string
  description: string
  url?: string
  generator?: () => Promise<void>
}

export class AudioManager {
  private audioContext: AudioContext | null = null
  private currentAudio: HTMLAudioElement | null = null
  private settings: AudioSettings = {
    volume: 0.7,
    soundType: 'standard',
    enableVibration: true,
    enableFlash: true
  }

  constructor() {
    this.initializeAudioContext()
  }

  /**
   * 初始化音频上下文
   */
  private initializeAudioContext() {
    if (typeof window === 'undefined') return

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    } catch (error) {
      console.warn('Web Audio API不可用:', error)
    }
  }

  /**
   * 获取预设音效列表
   */
  getAvailableSounds(): SoundEffect[] {
    return [
      {
        id: 'gentle',
        name: '轻柔提醒',
        description: '温和的铃声，适合日常提醒',
        generator: () => this.generateGentleSound()
      },
      {
        id: 'standard',
        name: '标准提醒',
        description: '清晰的提醒音，平衡音量',
        generator: () => this.generateStandardSound()
      },
      {
        id: 'urgent',
        name: '紧急提醒',
        description: '响亮的警示音，用于重要提醒',
        generator: () => this.generateUrgentSound()
      },
      {
        id: 'chime',
        name: '钟声',
        description: '悦耳的钟声效果',
        generator: () => this.generateChimeSound()
      },
      {
        id: 'beep',
        name: '蜂鸣声',
        description: '简单的蜂鸣提醒',
        generator: () => this.generateBeepSound()
      }
    ]
  }

  /**
   * 播放提醒音效
   */
  async playReminderSound(level: 1 | 2 | 3 = 1): Promise<void> {
    try {
      // 根据提醒级别选择音效
      const soundType = level === 3 ? 'urgent' : level === 2 ? 'standard' : this.settings.soundType

      // 停止当前播放的音频
      this.stopCurrentAudio()

      // 播放音效
      await this.playSound(soundType)

      // 触发震动
      if (this.settings.enableVibration && level >= 2) {
        this.triggerVibration(level)
      }

      // 触发闪烁效果
      if (this.settings.enableFlash && level >= 2) {
        this.triggerFlashEffect(level)
      }

    } catch (error) {
      console.error('播放提醒音效失败:', error)
    }
  }

  /**
   * 播放指定音效
   */
  private async playSound(soundType: string): Promise<void> {
    const sound = this.getAvailableSounds().find(s => s.id === soundType)
    
    if (sound?.generator) {
      await sound.generator()
    } else if (sound?.url) {
      await this.playAudioFile(sound.url)
    } else {
      // 默认音效
      await this.generateStandardSound()
    }
  }

  /**
   * 播放音频文件
   */
  private async playAudioFile(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(url)
      audio.volume = this.settings.volume
      
      audio.onended = () => resolve()
      audio.onerror = () => reject(new Error('音频播放失败'))
      
      this.currentAudio = audio
      audio.play().catch(reject)
    })
  }

  /**
   * 生成轻柔音效
   */
  private async generateGentleSound(): Promise<void> {
    if (!this.audioContext) return

    const oscillator = this.audioContext.createOscillator()
    const gainNode = this.audioContext.createGain()
    
    oscillator.connect(gainNode)
    gainNode.connect(this.audioContext.destination)
    
    oscillator.frequency.setValueAtTime(440, this.audioContext.currentTime)
    oscillator.frequency.exponentialRampToValueAtTime(880, this.audioContext.currentTime + 0.5)
    
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
    gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.3, this.audioContext.currentTime + 0.1)
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 1)
    
    oscillator.type = 'sine'
    oscillator.start(this.audioContext.currentTime)
    oscillator.stop(this.audioContext.currentTime + 1)
    
    return new Promise(resolve => {
      setTimeout(resolve, 1000)
    })
  }

  /**
   * 生成标准音效
   */
  private async generateStandardSound(): Promise<void> {
    if (!this.audioContext) return

    const frequencies = [523, 659, 784] // C5, E5, G5
    
    for (let i = 0; i < frequencies.length; i++) {
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      oscillator.frequency.value = frequencies[i]
      oscillator.type = 'triangle'
      
      const startTime = this.audioContext.currentTime + i * 0.2
      const duration = 0.3
      
      gainNode.gain.setValueAtTime(0, startTime)
      gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.5, startTime + 0.05)
      gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration)
      
      oscillator.start(startTime)
      oscillator.stop(startTime + duration)
    }
    
    return new Promise(resolve => {
      setTimeout(resolve, 800)
    })
  }

  /**
   * 生成紧急音效
   */
  private async generateUrgentSound(): Promise<void> {
    if (!this.audioContext) return

    for (let i = 0; i < 3; i++) {
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + i * 0.4)
      oscillator.frequency.setValueAtTime(1200, this.audioContext.currentTime + i * 0.4 + 0.1)
      oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + i * 0.4 + 0.2)
      
      oscillator.type = 'square'
      
      const startTime = this.audioContext.currentTime + i * 0.4
      const duration = 0.3
      
      gainNode.gain.setValueAtTime(0, startTime)
      gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.8, startTime + 0.02)
      gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration)
      
      oscillator.start(startTime)
      oscillator.stop(startTime + duration)
    }
    
    return new Promise(resolve => {
      setTimeout(resolve, 1500)
    })
  }

  /**
   * 生成钟声音效
   */
  private async generateChimeSound(): Promise<void> {
    if (!this.audioContext) return

    const frequencies = [523, 659, 784, 1047] // C5, E5, G5, C6
    
    frequencies.forEach((freq, index) => {
      const oscillator = this.audioContext!.createOscillator()
      const gainNode = this.audioContext!.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext!.destination)
      
      oscillator.frequency.value = freq
      oscillator.type = 'sine'
      
      const startTime = this.audioContext!.currentTime + index * 0.3
      const duration = 2
      
      gainNode.gain.setValueAtTime(0, startTime)
      gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.4, startTime + 0.1)
      gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration)
      
      oscillator.start(startTime)
      oscillator.stop(startTime + duration)
    })
    
    return new Promise(resolve => {
      setTimeout(resolve, 2500)
    })
  }

  /**
   * 生成蜂鸣音效
   */
  private async generateBeepSound(): Promise<void> {
    if (!this.audioContext) return

    for (let i = 0; i < 2; i++) {
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      oscillator.frequency.value = 1000
      oscillator.type = 'square'
      
      const startTime = this.audioContext.currentTime + i * 0.3
      const duration = 0.2
      
      gainNode.gain.setValueAtTime(0, startTime)
      gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.6, startTime + 0.01)
      gainNode.gain.linearRampToValueAtTime(0, startTime + duration)
      
      oscillator.start(startTime)
      oscillator.stop(startTime + duration)
    }
    
    return new Promise(resolve => {
      setTimeout(resolve, 700)
    })
  }

  /**
   * 触发震动
   */
  private triggerVibration(level: number): void {
    if (!('vibrate' in navigator)) return

    const patterns = {
      1: [200],
      2: [200, 100, 200],
      3: [200, 100, 200, 100, 200]
    }

    navigator.vibrate(patterns[level as keyof typeof patterns] || patterns[1])
  }

  /**
   * 触发闪烁效果
   */
  private triggerFlashEffect(level: number): void {
    if (typeof document === 'undefined') return

    const flashCount = level * 2
    let count = 0

    const flash = () => {
      if (count >= flashCount) return

      document.body.style.backgroundColor = count % 2 === 0 ? '#ff6b6b' : ''
      count++

      setTimeout(flash, 200)
    }

    flash()

    // 恢复原始背景色
    setTimeout(() => {
      document.body.style.backgroundColor = ''
    }, flashCount * 200 + 100)
  }

  /**
   * 停止当前音频
   */
  private stopCurrentAudio(): void {
    if (this.currentAudio) {
      this.currentAudio.pause()
      this.currentAudio.currentTime = 0
      this.currentAudio = null
    }
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<AudioSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
  }

  /**
   * 获取当前设置
   */
  getSettings(): AudioSettings {
    return { ...this.settings }
  }

  /**
   * 测试音效
   */
  async testSound(soundType: string): Promise<void> {
    await this.playSound(soundType)
  }
}

export const audioManager = new AudioManager()
