@echo off
chcp 65001 >nul
title 照护宝 - 创建桌面快捷方式

echo.
echo ========================================
echo           照护宝桌面快捷方式创建工具
echo ========================================
echo.

echo 正在创建桌面快捷方式...
echo.

REM 获取桌面路径
for /f "tokens=3*" %%i in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP=%%i %%j

REM 创建照护宝主页快捷方式
echo [InternetShortcut] > "%DESKTOP%\照护宝 - 主页.url"
echo URL=http://localhost:3000 >> "%DESKTOP%\照护宝 - 主页.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\照护宝 - 主页.url"
echo IconIndex=13 >> "%DESKTOP%\照护宝 - 主页.url"

REM 创建用药提醒快捷方式
echo [InternetShortcut] > "%DESKTOP%\照护宝 - 用药提醒.url"
echo URL=http://localhost:3000/medication >> "%DESKTOP%\照护宝 - 用药提醒.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\照护宝 - 用药提醒.url"
echo IconIndex=23 >> "%DESKTOP%\照护宝 - 用药提醒.url"

REM 创建登录注册快捷方式
echo [InternetShortcut] > "%DESKTOP%\照护宝 - 登录注册.url"
echo URL=http://localhost:3000/auth-demo >> "%DESKTOP%\照护宝 - 登录注册.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\照护宝 - 登录注册.url"
echo IconIndex=48 >> "%DESKTOP%\照护宝 - 登录注册.url"

echo ✅ 桌面快捷方式创建成功！
echo.
echo 已创建以下快捷方式：
echo 📱 照护宝 - 主页.url
echo 💊 照护宝 - 用药提醒.url  
echo 🔐 照护宝 - 登录注册.url
echo.
echo ⚠️  注意：请确保开发服务器正在运行 (npm run dev)
echo 💡 双击桌面快捷方式即可快速访问照护宝系统！
echo.

REM 询问是否启动开发服务器
set /p choice="是否现在启动开发服务器？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 正在启动开发服务器...
    start "照护宝开发服务器" cmd /k "cd /d %~dp0 && npm run dev"
    echo 开发服务器已在新窗口中启动！
)

echo.
echo 脚本执行完成！
pause
