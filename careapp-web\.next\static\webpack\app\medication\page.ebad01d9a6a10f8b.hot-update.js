"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/store/medication.ts":
/*!*********************************!*\
  !*** ./src/store/medication.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMedication: () => (/* binding */ useMedication),\n/* harmony export */   useMedicationStore: () => (/* binding */ useMedicationStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/medication-service */ \"(app-pages-browser)/./src/lib/medication-service.ts\");\n/* harmony import */ var _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/medication-scheduler */ \"(app-pages-browser)/./src/lib/medication-scheduler.ts\");\n/* harmony import */ var _lib_notification_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/notification-service */ \"(app-pages-browser)/./src/lib/notification-service.ts\");\n\n\n\n\nconst useMedicationStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        // 初始状态\n        reminders: [],\n        dailySchedule: null,\n        reminderSettings: null,\n        guardianContacts: [],\n        medicationRecords: [],\n        loading: false,\n        error: null,\n        // 加载用药提醒列表\n        loadReminders: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const reminders = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getMedicineReminders(userId);\n                set({\n                    reminders,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 添加用药提醒\n        addReminder: async (reminderData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                // 如果有作息时间，自动计算用药时间\n                const { dailySchedule } = get();\n                if (dailySchedule && reminderData.scheduledTimes.length === 0) {\n                    const calculatedTimes = _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.calculateMedicationTimes(reminderData.usage, reminderData.frequency, dailySchedule);\n                    reminderData.scheduledTimes = _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.validateAndAdjustTimes(calculatedTimes);\n                }\n                const reminder = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.createMedicineReminder(reminderData);\n                const { reminders } = get();\n                set({\n                    reminders: [\n                        reminder,\n                        ...reminders\n                    ],\n                    loading: false\n                });\n                // 如果启用了提醒，重新启动提醒系统\n                if (reminder.isEnabled && \"object\" !== 'undefined') {\n                    const { reminderSettings } = get();\n                    if (reminderSettings) {\n                        const notificationService1 = (0,_lib_notification_service__WEBPACK_IMPORTED_MODULE_2__.getNotificationService)();\n                        notificationService1.scheduleReminder(reminder, reminderSettings, (reminderId, confirmed)=>{\n                            get().recordMedication({\n                                reminderId,\n                                scheduledTime: new Date().toISOString(),\n                                status: confirmed ? 'taken' : 'missed',\n                                confirmationMethod: 'manual',\n                                userId: reminderData.userId\n                            });\n                        });\n                    }\n                }\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '添加提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 更新用药提醒\n        updateReminder: async (id, updates)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const updatedReminder = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.updateMedicineReminder(id, updates);\n                const { reminders } = get();\n                const newReminders = reminders.map((r)=>r.id === id ? updatedReminder : r);\n                set({\n                    reminders: newReminders,\n                    loading: false\n                });\n                // 更新提醒系统\n                if (true) {\n                    const notificationService1 = (0,_lib_notification_service__WEBPACK_IMPORTED_MODULE_2__.getNotificationService)();\n                    notificationService1.cancelReminder(id);\n                    if (updatedReminder.isEnabled) {\n                        const { reminderSettings } = get();\n                        if (reminderSettings) {\n                            notificationService1.scheduleReminder(updatedReminder, reminderSettings, (reminderId, confirmed)=>{\n                                get().recordMedication({\n                                    reminderId,\n                                    scheduledTime: new Date().toISOString(),\n                                    status: confirmed ? 'taken' : 'missed',\n                                    confirmationMethod: 'manual',\n                                    userId: updatedReminder.userId\n                                });\n                            });\n                        }\n                    }\n                }\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '更新提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 删除用药提醒\n        deleteReminder: async (id)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.deleteMedicineReminder(id);\n                const { reminders } = get();\n                set({\n                    reminders: reminders.filter((r)=>r.id !== id),\n                    loading: false\n                });\n                // 取消相关提醒\n                if (true) {\n                    const notificationService1 = (0,_lib_notification_service__WEBPACK_IMPORTED_MODULE_2__.getNotificationService)();\n                    notificationService1.cancelReminder(id);\n                }\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '删除提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 加载作息时间\n        loadDailySchedule: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const schedule = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getDailySchedule(userId);\n                set({\n                    dailySchedule: schedule,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载作息时间失败',\n                    loading: false\n                });\n            }\n        },\n        // 保存作息时间\n        saveDailySchedule: async (scheduleData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const schedule = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.saveDailySchedule(scheduleData);\n                set({\n                    dailySchedule: schedule,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '保存作息时间失败',\n                    loading: false\n                });\n            }\n        },\n        // 加载提醒设置\n        loadReminderSettings: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const settings = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getReminderSettings(userId);\n                set({\n                    reminderSettings: settings,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载提醒设置失败',\n                    loading: false\n                });\n            }\n        },\n        // 保存提醒设置\n        saveReminderSettings: async (settingsData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const settings = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.saveReminderSettings(settingsData);\n                set({\n                    reminderSettings: settings,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '保存提醒设置失败',\n                    loading: false\n                });\n            }\n        },\n        // 加载监护人联系人\n        loadGuardianContacts: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const contacts = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getGuardianContacts(userId);\n                set({\n                    guardianContacts: contacts,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载监护人联系人失败',\n                    loading: false\n                });\n            }\n        },\n        // 添加监护人联系人\n        addGuardianContact: async (contactData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const contact = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.addGuardianContact(contactData);\n                const { guardianContacts } = get();\n                set({\n                    guardianContacts: [\n                        ...guardianContacts,\n                        contact\n                    ],\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '添加监护人联系人失败',\n                    loading: false\n                });\n            }\n        },\n        // 记录用药\n        recordMedication: async (recordData)=>{\n            try {\n                const record = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.recordMedication(recordData);\n                const { medicationRecords } = get();\n                set({\n                    medicationRecords: [\n                        record,\n                        ...medicationRecords\n                    ]\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '记录用药失败'\n                });\n            }\n        },\n        // 加载用药记录\n        loadMedicationRecords: async (userId, startDate, endDate)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const records = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getMedicationRecords(userId, startDate, endDate);\n                set({\n                    medicationRecords: records,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载用药记录失败',\n                    loading: false\n                });\n            }\n        },\n        // 计算用药时间\n        calculateMedicationTimes: (usage, frequency)=>{\n            const { dailySchedule } = get();\n            if (!dailySchedule) {\n                return [\n                    '08:00'\n                ] // 默认时间\n                ;\n            }\n            const times = _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.calculateMedicationTimes(usage, frequency, dailySchedule);\n            return _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.validateAndAdjustTimes(times);\n        },\n        // 启动提醒系统\n        startReminderSystem: async (userId)=>{\n            try {\n                const { reminders, reminderSettings } = get();\n                if (!reminderSettings) {\n                    // 使用默认设置\n                    const defaultSettings = {\n                        userId,\n                        firstReminderMinutes: 15,\n                        reminderInterval: 5,\n                        maxReminders: 6,\n                        soundEnabled: true,\n                        voiceEnabled: true,\n                        notificationEnabled: true,\n                        guardianNotificationDelay: 30\n                    };\n                    await get().saveReminderSettings(defaultSettings);\n                }\n                const settings = get().reminderSettings;\n                // 为所有启用的提醒设置通知\n                reminders.filter((r)=>r.isEnabled).forEach((reminder)=>{\n                    notificationService.scheduleReminder(reminder, settings, (reminderId, confirmed)=>{\n                        get().recordMedication({\n                            reminderId,\n                            scheduledTime: new Date().toISOString(),\n                            status: confirmed ? 'taken' : 'missed',\n                            confirmationMethod: 'manual',\n                            userId\n                        });\n                    });\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '启动提醒系统失败'\n                });\n            }\n        },\n        // 停止提醒系统\n        stopReminderSystem: ()=>{\n            notificationService.cancelAllReminders();\n        },\n        // 清除错误\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        }\n    }));\n// 便捷的hooks\nconst useMedication = ()=>{\n    const store = useMedicationStore();\n    return {\n        // 数据\n        reminders: store.reminders,\n        dailySchedule: store.dailySchedule,\n        reminderSettings: store.reminderSettings,\n        guardianContacts: store.guardianContacts,\n        medicationRecords: store.medicationRecords,\n        // 状态\n        loading: store.loading,\n        error: store.error,\n        // 方法\n        loadReminders: store.loadReminders,\n        addReminder: store.addReminder,\n        updateReminder: store.updateReminder,\n        deleteReminder: store.deleteReminder,\n        loadDailySchedule: store.loadDailySchedule,\n        saveDailySchedule: store.saveDailySchedule,\n        loadReminderSettings: store.loadReminderSettings,\n        saveReminderSettings: store.saveReminderSettings,\n        loadGuardianContacts: store.loadGuardianContacts,\n        addGuardianContact: store.addGuardianContact,\n        recordMedication: store.recordMedication,\n        loadMedicationRecords: store.loadMedicationRecords,\n        calculateMedicationTimes: store.calculateMedicationTimes,\n        startReminderSystem: store.startReminderSystem,\n        stopReminderSystem: store.stopReminderSystem,\n        clearError: store.clearError\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/medication.ts\n"));

/***/ })

});