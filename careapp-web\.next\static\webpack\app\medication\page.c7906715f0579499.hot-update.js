"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/speech-service.ts":
/*!***********************************!*\
  !*** ./src/lib/speech-service.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeechService: () => (/* binding */ SpeechService),\n/* harmony export */   speechService: () => (/* binding */ speechService)\n/* harmony export */ });\nclass SpeechService {\n    /**\n   * 初始化语音识别\n   */ initializeSpeechRecognition() {\n        if (true) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (SpeechRecognition) {\n                this.recognition = new SpeechRecognition();\n                this.recognition.continuous = false;\n                this.recognition.interimResults = true;\n                this.recognition.lang = 'zh-CN';\n                this.recognition.maxAlternatives = 1;\n            }\n        }\n    }\n    /**\n   * 初始化语音合成\n   */ initializeSpeechSynthesis() {\n        if ( true && 'speechSynthesis' in window) {\n            this.synthesis = window.speechSynthesis;\n        }\n    }\n    /**\n   * 初始化药品数据库\n   */ initializeMedicineDatabase() {\n        // 常用药品及其别名\n        const medicines = [\n            // 心血管药物\n            {\n                name: '阿司匹林',\n                aliases: [\n                    '阿斯匹林',\n                    '阿司匹林肠溶片',\n                    '拜阿司匹灵'\n                ]\n            },\n            {\n                name: '硝苯地平',\n                aliases: [\n                    '硝苯地平片',\n                    '心痛定',\n                    '拜新同'\n                ]\n            },\n            {\n                name: '卡托普利',\n                aliases: [\n                    '卡托普利片',\n                    '开博通'\n                ]\n            },\n            {\n                name: '美托洛尔',\n                aliases: [\n                    '美托洛尔片',\n                    '倍他乐克',\n                    '酒石酸美托洛尔'\n                ]\n            },\n            // 降糖药物\n            {\n                name: '二甲双胍',\n                aliases: [\n                    '二甲双胍片',\n                    '格华止',\n                    '美迪康'\n                ]\n            },\n            {\n                name: '格列齐特',\n                aliases: [\n                    '格列齐特片',\n                    '达美康',\n                    '迪沙片'\n                ]\n            },\n            {\n                name: '胰岛素',\n                aliases: [\n                    '胰岛素注射液',\n                    '诺和灵',\n                    '优泌林'\n                ]\n            },\n            // 抗生素\n            {\n                name: '阿莫西林',\n                aliases: [\n                    '阿莫西林胶囊',\n                    '阿莫西林片',\n                    '再林'\n                ]\n            },\n            {\n                name: '头孢拉定',\n                aliases: [\n                    '头孢拉定胶囊',\n                    '头孢拉定片'\n                ]\n            },\n            {\n                name: '左氧氟沙星',\n                aliases: [\n                    '左氧氟沙星片',\n                    '可乐必妥',\n                    '来立信'\n                ]\n            },\n            // 消化系统药物\n            {\n                name: '奥美拉唑',\n                aliases: [\n                    '奥美拉唑肠溶胶囊',\n                    '洛赛克',\n                    '奥克'\n                ]\n            },\n            {\n                name: '多潘立酮',\n                aliases: [\n                    '多潘立酮片',\n                    '吗丁啉',\n                    '胃复安'\n                ]\n            },\n            {\n                name: '蒙脱石散',\n                aliases: [\n                    '思密达',\n                    '必奇'\n                ]\n            },\n            // 感冒药物\n            {\n                name: '对乙酰氨基酚',\n                aliases: [\n                    '对乙酰氨基酚片',\n                    '泰诺林',\n                    '百服宁',\n                    '扑热息痛'\n                ]\n            },\n            {\n                name: '布洛芬',\n                aliases: [\n                    '布洛芬片',\n                    '芬必得',\n                    '美林'\n                ]\n            },\n            {\n                name: '复方氨酚烷胺',\n                aliases: [\n                    '快克',\n                    '感康'\n                ]\n            },\n            // 维生素类\n            {\n                name: '维生素C',\n                aliases: [\n                    '维生素C片',\n                    'VC片',\n                    '维C'\n                ]\n            },\n            {\n                name: '维生素D',\n                aliases: [\n                    '维生素D3',\n                    'VD3',\n                    '钙尔奇D'\n                ]\n            },\n            {\n                name: '复合维生素B',\n                aliases: [\n                    '维生素B族',\n                    'VB片',\n                    '复合VB'\n                ]\n            }\n        ];\n        // 构建药品数据库\n        medicines.forEach((medicine)=>{\n            const allNames = [\n                medicine.name,\n                ...medicine.aliases\n            ];\n            this.medicineDatabase.set(medicine.name, allNames);\n            this.commonMedicines.push(medicine.name);\n            // 为每个别名也建立映射\n            medicine.aliases.forEach((alias)=>{\n                this.medicineDatabase.set(alias, allNames);\n            });\n        });\n    }\n    /**\n   * 检查浏览器是否支持语音识别\n   */ isSpeechRecognitionSupported() {\n        return this.recognition !== null;\n    }\n    /**\n   * 检查浏览器是否支持语音合成\n   */ isSpeechSynthesisSupported() {\n        return this.synthesis !== null;\n    }\n    /**\n   * 开始语音识别\n   */ async startListening(onResult, onError) {\n        if (!this.recognition) {\n            throw new Error('语音识别不支持');\n        }\n        if (this.isListening) {\n            this.stopListening();\n        }\n        return new Promise((resolve, reject)=>{\n            this.recognition.onstart = ()=>{\n                this.isListening = true;\n                resolve();\n            };\n            this.recognition.onresult = (event)=>{\n                const result = event.results[event.results.length - 1];\n                const transcript = result[0].transcript;\n                const confidence = result[0].confidence;\n                const isFinal = result.isFinal;\n                onResult({\n                    transcript: transcript.trim(),\n                    confidence,\n                    isFinal\n                });\n            };\n            this.recognition.onerror = (event)=>{\n                this.isListening = false;\n                const errorMessage = this.getErrorMessage(event.error);\n                onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n                reject(new Error(errorMessage));\n            };\n            this.recognition.onend = ()=>{\n                this.isListening = false;\n            };\n            try {\n                this.recognition.start();\n            } catch (error) {\n                this.isListening = false;\n                reject(error);\n            }\n        });\n    }\n    /**\n   * 停止语音识别\n   */ stopListening() {\n        if (this.recognition && this.isListening) {\n            this.recognition.stop();\n            this.isListening = false;\n        }\n    }\n    /**\n   * 语音播报\n   */ async speak(options) {\n        if (!this.synthesis) {\n            throw new Error('语音合成不支持');\n        }\n        // 停止当前播报\n        this.synthesis.cancel();\n        return new Promise((resolve, reject)=>{\n            const utterance = new SpeechSynthesisUtterance(options.text);\n            utterance.lang = options.lang || 'zh-CN';\n            utterance.rate = options.rate || 1;\n            utterance.pitch = options.pitch || 1;\n            utterance.volume = options.volume || 1;\n            utterance.onend = ()=>resolve();\n            utterance.onerror = (event)=>reject(new Error(\"语音播报失败: \".concat(event.error)));\n            // 获取中文语音\n            const voices = this.synthesis.getVoices();\n            const chineseVoice = voices.find((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n            if (chineseVoice) {\n                utterance.voice = chineseVoice;\n            }\n            this.synthesis.speak(utterance);\n        });\n    }\n    /**\n   * 停止语音播报\n   */ stopSpeaking() {\n        if (this.synthesis) {\n            this.synthesis.cancel();\n        }\n    }\n    /**\n   * 解析药品信息的语音输入（增强版）\n   */ parseMedicineInput(transcript) {\n        const result = {};\n        const cleanTranscript = this.cleanTranscript(transcript);\n        // 智能提取药品名称\n        const medicineInfo = this.extractMedicineName(cleanTranscript);\n        if (medicineInfo) {\n            result.medicineName = medicineInfo.name;\n            result.confidence = medicineInfo.confidence;\n            result.suggestions = medicineInfo.suggestions;\n        }\n        // 提取剂量信息\n        const dosagePatterns = [\n            /(\\d+)\\s*片/,\n            /(\\d+)\\s*粒/,\n            /(\\d+)\\s*毫升/,\n            /(\\d+)\\s*ml/,\n            /(\\d+)\\s*毫克/,\n            /(\\d+)\\s*mg/,\n            /(\\d+)\\s*滴/\n        ];\n        for (const pattern of dosagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                const unit = match[0].replace(match[1], '').trim();\n                result.dosage = \"\".concat(match[1]).concat(unit);\n                break;\n            }\n        }\n        // 提取用法信息\n        const usagePatterns = [\n            /餐前\\s*(\\d+)?\\s*分钟?/,\n            /饭前\\s*(\\d+)?\\s*分钟?/,\n            /餐后\\s*(\\d+)?\\s*分钟?/,\n            /饭后\\s*(\\d+)?\\s*分钟?/,\n            /睡前\\s*(\\d+)?\\s*分钟?/,\n            /晨起/,\n            /起床后/\n        ];\n        for (const pattern of usagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.usage = match[0];\n                break;\n            }\n        }\n        // 提取频次信息\n        const frequencyPatterns = [\n            /每日\\s*(\\d+)\\s*次/,\n            /一日\\s*(\\d+)\\s*次/,\n            /每天\\s*(\\d+)\\s*次/,\n            /(\\d+)\\s*次\\s*每日/,\n            /(\\d+)\\s*次\\s*一日/\n        ];\n        for (const pattern of frequencyPatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.frequency = match[0];\n                break;\n            }\n        }\n        return result;\n    }\n    /**\n   * 生成用药提醒的语音内容\n   */ generateReminderSpeech(medicineName, dosage, usage) {\n        let speech = \"请注意，现在是服用\".concat(medicineName, \"的时间。\");\n        if (dosage) {\n            speech += \"请服用\".concat(dosage, \"。\");\n        }\n        if (usage) {\n            speech += \"用法：\".concat(usage, \"。\");\n        }\n        speech += '请确认是否已经服药。';\n        return speech;\n    }\n    /**\n   * 获取错误信息\n   */ getErrorMessage(error) {\n        switch(error){\n            case 'no-speech':\n                return '没有检测到语音输入';\n            case 'audio-capture':\n                return '无法访问麦克风';\n            case 'not-allowed':\n                return '麦克风权限被拒绝';\n            case 'network':\n                return '网络错误';\n            case 'service-not-allowed':\n                return '语音识别服务不可用';\n            default:\n                return \"语音识别错误: \".concat(error);\n        }\n    }\n    /**\n   * 请求麦克风权限\n   */ async requestMicrophonePermission() {\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            stream.getTracks().forEach((track)=>track.stop());\n            return true;\n        } catch (error) {\n            console.error('麦克风权限请求失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 获取可用的语音列表\n   */ getAvailableVoices() {\n        if (!this.synthesis) return [];\n        return this.synthesis.getVoices().filter((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n    }\n    /**\n   * 测试语音功能\n   */ async testSpeech() {\n        const result = {\n            recognition: this.isSpeechRecognitionSupported(),\n            synthesis: this.isSpeechSynthesisSupported()\n        };\n        if (result.synthesis) {\n            try {\n                await this.speak({\n                    text: '语音功能测试正常'\n                });\n            } catch (error) {\n                result.synthesis = false;\n            }\n        }\n        return result;\n    }\n    constructor(){\n        this.recognition = null;\n        this.synthesis = null;\n        this.isListening = false;\n        this.medicineDatabase = new Map();\n        this.commonMedicines = [];\n        this.initializeSpeechRecognition();\n        this.initializeSpeechSynthesis();\n        this.initializeMedicineDatabase();\n    }\n}\nconst speechService = new SpeechService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/speech-service.ts\n"));

/***/ })

});