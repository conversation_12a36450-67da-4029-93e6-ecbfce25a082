'use client'

import { useState, useEffect } from 'react'
import { Volume2, VolumeX, Smartphone, Zap, Play, Settings } from 'lucide-react'
import { audioManager } from '@/lib/audio-manager'
import type { AudioSettings, SoundEffect } from '@/lib/audio-manager'

export function AudioSettings() {
  const [settings, setSettings] = useState<AudioSettings>(audioManager.getSettings())
  const [availableSounds, setAvailableSounds] = useState<SoundEffect[]>([])
  const [isPlaying, setIsPlaying] = useState<string | null>(null)

  useEffect(() => {
    setAvailableSounds(audioManager.getAvailableSounds())
  }, [])

  const handleSettingChange = (key: keyof AudioSettings, value: any) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    audioManager.updateSettings(newSettings)
  }

  const testSound = async (soundId: string) => {
    if (isPlaying) return

    setIsPlaying(soundId)
    try {
      await audioManager.testSound(soundId)
    } catch (error) {
      console.error('测试音效失败:', error)
    } finally {
      setIsPlaying(null)
    }
  }

  const testReminderLevel = async (level: 1 | 2 | 3) => {
    if (isPlaying) return

    setIsPlaying(`level-${level}`)
    try {
      await audioManager.playReminderSound(level)
    } catch (error) {
      console.error('测试提醒失败:', error)
    } finally {
      setIsPlaying(null)
    }
  }

  return (
    <div className="space-y-6">
      {/* 音量设置 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Volume2 className="w-5 h-5 mr-2" />
          音量设置
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              提醒音量: {Math.round(settings.volume * 100)}%
            </label>
            <div className="flex items-center space-x-3">
              <VolumeX className="w-4 h-4 text-gray-400" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={settings.volume}
                onChange={(e) => handleSettingChange('volume', parseFloat(e.target.value))}
                className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <Volume2 className="w-4 h-4 text-gray-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 音效选择 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          提醒音效
        </h3>
        
        <div className="space-y-3">
          {availableSounds.map((sound) => (
            <div key={sound.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex-1">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id={sound.id}
                    name="soundType"
                    value={sound.id}
                    checked={settings.soundType === sound.id}
                    onChange={(e) => handleSettingChange('soundType', e.target.value)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                  />
                  <label htmlFor={sound.id} className="ml-3 cursor-pointer">
                    <div className="font-medium text-gray-800">{sound.name}</div>
                    <div className="text-sm text-gray-600">{sound.description}</div>
                  </label>
                </div>
              </div>
              <button
                type="button"
                onClick={() => testSound(sound.id)}
                disabled={isPlaying === sound.id}
                className="ml-3 p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                title="试听音效"
              >
                <Play className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* 视觉和触觉效果 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Zap className="w-5 h-5 mr-2" />
          视觉和触觉效果
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Smartphone className="w-5 h-5 text-gray-500 mr-3" />
              <div>
                <div className="font-medium text-gray-800">震动提醒</div>
                <div className="text-sm text-gray-600">在支持的设备上启用震动</div>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableVibration}
                onChange={(e) => handleSettingChange('enableVibration', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Zap className="w-5 h-5 text-gray-500 mr-3" />
              <div>
                <div className="font-medium text-gray-800">屏幕闪烁</div>
                <div className="text-sm text-gray-600">重要提醒时屏幕闪烁</div>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableFlash}
                onChange={(e) => handleSettingChange('enableFlash', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* 提醒级别测试 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">提醒级别测试</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button
            type="button"
            onClick={() => testReminderLevel(1)}
            disabled={isPlaying === 'level-1'}
            className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            第一级提醒
          </button>
          
          <button
            type="button"
            onClick={() => testReminderLevel(2)}
            disabled={isPlaying === 'level-2'}
            className="flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            第二级提醒
          </button>
          
          <button
            type="button"
            onClick={() => testReminderLevel(3)}
            disabled={isPlaying === 'level-3'}
            className="flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
          >
            <Play className="w-4 h-4 mr-2" />
            第三级提醒
          </button>
        </div>
        
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600">
            💡 <strong>说明：</strong>
            第一级为普通提醒，第二级增加震动和闪烁，第三级为最高级别的紧急提醒。
          </p>
        </div>
      </div>
    </div>
  )
}
