"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n/* harmony import */ var _audio_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./audio-manager */ \"(app-pages-browser)/./src/lib/audio-manager.ts\");\n\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 初始化音频系统\n   */ initializeAudio() {\n        if (false) {}\n        try {\n            // 预加载提醒音效\n            this.reminderAudio = new Audio();\n            this.reminderAudio.preload = 'auto';\n            this.reminderAudio.volume = 0.7;\n            // 尝试加载多种音效格式\n            const audioSources = [\n                '/sounds/reminder.mp3',\n                '/sounds/reminder.wav',\n                '/sounds/reminder.ogg'\n            ];\n            // 使用第一个可用的音频格式\n            for (const src of audioSources){\n                this.reminderAudio.src = src;\n                break;\n            }\n            // 如果没有音频文件，创建合成音效\n            if (!this.reminderAudio.src) {\n                this.createSyntheticReminderSound();\n            }\n            this.isAudioInitialized = true;\n        } catch (error) {\n            console.warn('音频初始化失败，将使用合成音效:', error);\n            this.createSyntheticReminderSound();\n        }\n    }\n    /**\n   * 创建合成提醒音效\n   */ createSyntheticReminderSound() {\n        if (false) {}\n        try {\n            // 使用Web Audio API创建合成音效\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const createBeep = function(frequency, duration) {\n                let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const oscillator = audioContext.createOscillator();\n                        const gainNode = audioContext.createGain();\n                        oscillator.connect(gainNode);\n                        gainNode.connect(audioContext.destination);\n                        oscillator.frequency.value = frequency;\n                        oscillator.type = 'sine';\n                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n                        oscillator.start(audioContext.currentTime);\n                        oscillator.stop(audioContext.currentTime + duration);\n                        setTimeout(resolve, duration * 1000);\n                    }, delay);\n                });\n            };\n            // 创建自定义提醒音效播放函数\n            this.playCustomReminderSound = async ()=>{\n                try {\n                    await createBeep(800, 0.2, 0) // 第一声\n                    ;\n                    await createBeep(1000, 0.2, 100) // 第二声\n                    ;\n                    await createBeep(800, 0.3, 200) // 第三声\n                    ;\n                } catch (error) {\n                    console.error('播放合成音效失败:', error);\n                }\n            };\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒（使用音效管理器）\n        if (settings.soundEnabled) {\n            await _audio_manager__WEBPACK_IMPORTED_MODULE_1__.audioManager.playReminderSound(1);\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报和强化音效\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 浏览器通知（更紧急）\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"⚠️ 重要提醒：\".concat(notification.medicineName), {\n                body: \"请立即服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-urgent.png',\n                badge: '/icons/badge-urgent.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 强化音效提醒（使用音效管理器）\n        if (settings.soundEnabled) {\n            await _audio_manager__WEBPACK_IMPORTED_MODULE_1__.audioManager.playReminderSound(2);\n        }\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 页面弹窗（更显眼的样式）\n        this.notifyUI('reminder-popup-urgent', notification);\n    }\n    /**\n   * 第三级提醒：确认是否已服药（最高级别）\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 最高级别浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"\\uD83D\\uDEA8 紧急提醒：\".concat(notification.medicineName), {\n                body: \"您可能忘记服药了！请确认是否已服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-emergency.png',\n                badge: '/icons/badge-emergency.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                vibrate: [\n                    200,\n                    100,\n                    200\n                ],\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'missed',\n                        title: '忘记了'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后确认'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 连续强化音效（使用音效管理器）\n        if (settings.soundEnabled) {\n            await _audio_manager__WEBPACK_IMPORTED_MODULE_1__.audioManager.playReminderSound(3);\n            // 间隔后再次播放\n            setTimeout(async ()=>{\n                await _audio_manager__WEBPACK_IMPORTED_MODULE_1__.audioManager.playReminderSound(3);\n            }, 2000);\n        }\n        // 询问是否已服药\n        const confirmationText = \"重要提醒！您是否已经服用了\".concat(notification.medicineName, \"？如果忘记了，请立即服用。\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText,\n                    rate: 0.9,\n                    pitch: 1.1,\n                    volume: 1.0 // 最大音量\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示紧急确认对话框\n        this.notifyUI('confirmation-dialog-urgent', notification);\n        // 如果支持，尝试使页面闪烁提醒\n        this.triggerPageFlash();\n    }\n    /**\n   * 触发页面闪烁效果\n   */ triggerPageFlash() {\n        if (typeof document === 'undefined') return;\n        try {\n            const originalTitle = document.title;\n            let flashCount = 0;\n            const maxFlashes = 6;\n            const flashInterval = setInterval(()=>{\n                document.title = flashCount % 2 === 0 ? '🚨 用药提醒！' : originalTitle;\n                flashCount++;\n                if (flashCount >= maxFlashes) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                }\n            }, 500);\n            // 页面可见性变化时停止闪烁\n            const handleVisibilityChange = ()=>{\n                if (!document.hidden) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                }\n            };\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n        } catch (error) {\n            console.error('页面闪烁效果失败:', error);\n        }\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ async playReminderSound() {\n        try {\n            // 优先使用预加载的音频\n            if (this.reminderAudio && this.isAudioInitialized) {\n                this.reminderAudio.currentTime = 0 // 重置播放位置\n                ;\n                await this.reminderAudio.play();\n                return;\n            }\n            // 如果预加载音频不可用，使用合成音效\n            if (this.playCustomReminderSound) {\n                await this.playCustomReminderSound();\n                return;\n            }\n            // 最后的备选方案：简单的beep音效\n            this.playFallbackSound();\n        } catch (error) {\n            console.error('播放提醒声音失败:', error);\n            // 如果所有音效都失败，尝试备选方案\n            this.playFallbackSound();\n        }\n    }\n    /**\n   * 备选音效（系统beep）\n   */ playFallbackSound() {\n        try {\n            // 使用系统默认音效\n            if ('speechSynthesis' in window) {\n                const utterance = new SpeechSynthesisUtterance('');\n                utterance.volume = 0.1;\n                speechSynthesis.speak(utterance);\n            }\n        } catch (error) {\n            console.warn('备选音效也无法播放:', error);\n        }\n    }\n    /**\n   * 播放连续提醒音效（用于重要提醒）\n   */ async playIntensiveReminderSound() {\n        for(let i = 0; i < 3; i++){\n            await this.playReminderSound();\n            await new Promise((resolve)=>setTimeout(resolve, 500)) // 间隔500ms\n            ;\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        this.reminderAudio = null;\n        this.isAudioInitialized = false;\n        this.playCustomReminderSound = null;\n        if (true) {\n            this.initializeNotifications();\n            this.initializeAudio();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});