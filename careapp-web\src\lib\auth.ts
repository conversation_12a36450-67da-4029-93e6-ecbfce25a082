import { createClient } from './supabase'
import type { User } from '@supabase/supabase-js'

export interface AuthUser extends User {
  user_metadata: {
    name?: string
    phone?: string
    role?: 'patient' | 'family' | 'caregiver'
  }
}

export interface SignUpData {
  email: string
  password: string
  name: string
  phone?: string
  role: 'patient' | 'family' | 'caregiver'
}

export interface SignInData {
  email: string
  password: string
}

export class AuthService {
  private supabase = createClient()

  // 用户注册
  async signUp(data: SignUpData) {
    const { email, password, name, phone, role } = data
    
    const { data: authData, error } = await this.supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          phone,
          role,
        },
      },
    })

    if (error) {
      throw new Error(error.message)
    }

    // 如果注册成功，创建用户档案
    if (authData.user) {
      const { error: profileError } = await this.supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: authData.user.email!,
          name,
          phone,
          role,
        })

      if (profileError) {
        console.error('创建用户档案失败:', profileError)
      }
    }

    return authData
  }

  // 用户登录
  async signIn(data: SignInData) {
    const { email, password } = data
    
    const { data: authData, error } = await this.supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      throw new Error(error.message)
    }

    return authData
  }

  // 用户登出
  async signOut() {
    const { error } = await this.supabase.auth.signOut()
    
    if (error) {
      throw new Error(error.message)
    }
  }

  // 获取当前用户
  async getCurrentUser(): Promise<AuthUser | null> {
    const { data: { user }, error } = await this.supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }

    return user as AuthUser
  }

  // 获取用户会话
  async getSession() {
    const { data: { session }, error } = await this.supabase.auth.getSession()
    
    if (error) {
      throw new Error(error.message)
    }

    return session
  }

  // 重置密码
  async resetPassword(email: string) {
    const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  // 更新密码
  async updatePassword(password: string) {
    const { error } = await this.supabase.auth.updateUser({
      password,
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  // 监听认证状态变化
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return this.supabase.auth.onAuthStateChange((event, session) => {
      callback(session?.user as AuthUser | null)
    })
  }
}

// 导出单例实例
export const authService = new AuthService()
