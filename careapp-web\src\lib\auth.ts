import { supabase } from './supabase'
import type { User } from '@supabase/supabase-js'

// 检查是否配置了Supabase
const isSupabaseConfigured = () => {
  return process.env.NEXT_PUBLIC_SUPABASE_URL &&
         process.env.NEXT_PUBLIC_SUPABASE_URL !== 'https://your-project.supabase.co' &&
         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY !== 'your-anon-key'
}

export interface AuthUser extends User {
  user_metadata: {
    name?: string
    phone?: string
    role?: 'patient' | 'family' | 'caregiver'
  }
}

export interface SignUpData {
  email: string
  password: string
  name: string
  phone?: string
  role: 'patient' | 'family' | 'caregiver'
}

export interface SignInData {
  email: string
  password: string
}

export class AuthService {
  private client = supabase

  // 用户注册
  async signUp(data: SignUpData) {
    // 检查Supabase配置
    if (!isSupabaseConfigured()) {
      // 演示模式：模拟注册成功
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟

      const mockUser: AuthUser = {
        id: `demo-${Date.now()}`,
        email: data.email,
        user_metadata: {
          name: data.name,
          phone: data.phone,
          role: data.role
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        aud: 'authenticated',
        app_metadata: {},
        role: 'authenticated'
      } as AuthUser

      // 存储到localStorage作为演示
      localStorage.setItem('demo_user', JSON.stringify(mockUser))

      return { user: mockUser, session: null }
    }

    const { email, password, name, phone, role } = data

    const { data: authData, error } = await this.client.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          phone,
          role,
        },
      },
    })

    if (error) {
      throw new Error(error.message)
    }

    // 如果注册成功，创建用户档案
    if (authData.user) {
      const { error: profileError } = await this.client
        .from('users')
        .insert({
          id: authData.user.id,
          email: authData.user.email!,
          name,
          phone,
          role,
        })

      if (profileError) {
        console.error('创建用户档案失败:', profileError)
      }
    }

    return authData
  }

  // 用户登录
  async signIn(data: SignInData) {
    // 检查Supabase配置
    if (!isSupabaseConfigured()) {
      // 演示模式：检查是否有演示用户
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟

      const demoUser = localStorage.getItem('demo_user')
      if (demoUser) {
        const user = JSON.parse(demoUser) as AuthUser
        if (user.email === data.email) {
          return { user, session: null }
        }
      }

      // 创建默认演示用户
      const mockUser: AuthUser = {
        id: `demo-${Date.now()}`,
        email: data.email,
        user_metadata: {
          name: '演示用户',
          role: 'patient'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        aud: 'authenticated',
        app_metadata: {},
        role: 'authenticated'
      } as AuthUser

      localStorage.setItem('demo_user', JSON.stringify(mockUser))
      return { user: mockUser, session: null }
    }

    const { email, password } = data

    const { data: authData, error } = await this.client.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      throw new Error(error.message)
    }

    return authData
  }

  // 用户登出
  async signOut() {
    // 检查Supabase配置
    if (!isSupabaseConfigured()) {
      // 演示模式：清除本地存储
      localStorage.removeItem('demo_user')
      return
    }

    const { error } = await this.client.auth.signOut()

    if (error) {
      throw new Error(error.message)
    }
  }

  // 获取当前用户
  async getCurrentUser(): Promise<AuthUser | null> {
    // 检查Supabase配置
    if (!isSupabaseConfigured()) {
      // 演示模式：从本地存储获取用户
      const demoUser = localStorage.getItem('demo_user')
      return demoUser ? JSON.parse(demoUser) as AuthUser : null
    }

    const { data: { user }, error } = await this.client.auth.getUser()

    if (error || !user) {
      return null
    }

    return user as AuthUser
  }

  // 获取用户会话
  async getSession() {
    const { data: { session }, error } = await this.client.auth.getSession()

    if (error) {
      throw new Error(error.message)
    }

    return session
  }

  // 重置密码
  async resetPassword(email: string) {
    const { error } = await this.client.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  // 更新密码
  async updatePassword(password: string) {
    const { error } = await this.client.auth.updateUser({
      password,
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  // 监听认证状态变化
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    // 检查Supabase配置
    if (!isSupabaseConfigured()) {
      // 演示模式：返回一个空的取消函数
      return {
        data: { subscription: null },
        unsubscribe: () => {}
      }
    }

    return this.client.auth.onAuthStateChange((event, session) => {
      callback(session?.user as AuthUser | null)
    })
  }
}

// 导出单例实例
export const authService = new AuthService()
