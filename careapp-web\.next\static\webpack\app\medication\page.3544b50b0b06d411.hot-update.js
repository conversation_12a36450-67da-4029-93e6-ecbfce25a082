"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx":
/*!*********************************************************!*\
  !*** ./src/components/medication/MedicineInputForm.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MedicineInputForm: () => (/* binding */ MedicineInputForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileText,Mic,MicOff,Pill,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.mjs\");\n/* harmony import */ var _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n/* harmony import */ var _store_medication__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/medication */ \"(app-pages-browser)/./src/store/medication.ts\");\n/* __next_internal_client_entry_do_not_use__ MedicineInputForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MedicineInputForm(param) {\n    let { userId, onSuccess, onCancel } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        medicineName: '',\n        dosage: '',\n        usage: '',\n        frequency: '',\n        duration: ''\n    });\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [speechSupported, setSpeechSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceTranscript, setVoiceTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [confidence, setConfidence] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addReminder, calculateMedicationTimes, loading, error, clearError } = (0,_store_medication__WEBPACK_IMPORTED_MODULE_3__.useMedication)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicineInputForm.useEffect\": ()=>{\n            setSpeechSupported(_lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.isSpeechRecognitionSupported());\n        }\n    }[\"MedicineInputForm.useEffect\"], []);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const handleVoiceInput = async ()=>{\n        if (!speechSupported) {\n            alert('您的浏览器不支持语音识别功能');\n            return;\n        }\n        if (isListening) {\n            _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.stopListening();\n            setIsListening(false);\n            return;\n        }\n        try {\n            // 请求麦克风权限\n            const hasPermission = await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.requestMicrophonePermission();\n            if (!hasPermission) {\n                alert('需要麦克风权限才能使用语音输入功能');\n                return;\n            }\n            setIsListening(true);\n            setVoiceTranscript('');\n            await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.startListening((result)=>{\n                setVoiceTranscript(result.transcript);\n                if (result.isFinal) {\n                    // 解析语音输入\n                    const parsed = _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.parseMedicineInput(result.transcript);\n                    setFormData((prev)=>({\n                            ...prev,\n                            medicineName: parsed.medicineName || prev.medicineName,\n                            dosage: parsed.dosage || prev.dosage,\n                            usage: parsed.usage || prev.usage,\n                            frequency: parsed.frequency || prev.frequency\n                        }));\n                    setIsListening(false);\n                }\n            }, (error)=>{\n                console.error('语音识别错误:', error);\n                setIsListening(false);\n                alert(\"语音识别失败: \".concat(error));\n            });\n        } catch (error) {\n            console.error('启动语音识别失败:', error);\n            setIsListening(false);\n            alert('启动语音识别失败，请检查麦克风权限');\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.medicineName.trim()) {\n            newErrors.medicineName = '请输入药品名称';\n        }\n        if (!formData.dosage.trim()) {\n            newErrors.dosage = '请输入用药剂量';\n        }\n        if (!formData.usage.trim()) {\n            newErrors.usage = '请输入用药规则';\n        }\n        if (!formData.frequency.trim()) {\n            newErrors.frequency = '请输入用药频次';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        try {\n            clearError();\n            // 计算用药时间\n            const scheduledTimes = calculateMedicationTimes(formData.usage, formData.frequency);\n            await addReminder({\n                medicineName: formData.medicineName.trim(),\n                dosage: formData.dosage.trim(),\n                usage: formData.usage.trim(),\n                frequency: formData.frequency.trim(),\n                duration: formData.duration ? parseInt(formData.duration) : undefined,\n                scheduledTimes,\n                isEnabled: true,\n                userId\n            });\n            // 重置表单\n            setFormData({\n                medicineName: '',\n                dosage: '',\n                usage: '',\n                frequency: '',\n                duration: ''\n            });\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            console.error('添加用药提醒失败:', error);\n        }\n    };\n    const usageOptions = [\n        '餐前30分钟',\n        '餐后1小时',\n        '随餐服用',\n        '睡前30分钟',\n        '晨起服用',\n        '空腹服用'\n    ];\n    const frequencyOptions = [\n        '每日1次',\n        '每日2次',\n        '每日3次',\n        '每日4次',\n        '每周2次',\n        '每周3次'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800\",\n                        children: \"添加用药提醒\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    speechSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleVoiceInput,\n                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors \".concat(isListening ? 'bg-red-500 text-white' : 'bg-blue-500 text-white hover:bg-blue-600'),\n                        children: [\n                            isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 66\n                            }, this),\n                            isListening ? '停止录音' : '语音输入'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            voiceTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"语音识别结果：\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        voiceTranscript\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"药品名称 *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.medicineName,\n                                onChange: (e)=>handleInputChange('medicineName', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.medicineName ? 'border-red-300' : 'border-gray-300'),\n                                placeholder: \"例如：阿司匹林肠溶片\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            errors.medicineName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.medicineName\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"用药剂量 *\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.dosage,\n                                onChange: (e)=>handleInputChange('dosage', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.dosage ? 'border-red-300' : 'border-gray-300'),\n                                placeholder: \"例如：1片、2ml、100mg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            errors.dosage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.dosage\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"用药规则 *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.usage,\n                                onChange: (e)=>handleInputChange('usage', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.usage ? 'border-red-300' : 'border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"请选择用药规则\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    usageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.usage,\n                                onChange: (e)=>handleInputChange('usage', e.target.value),\n                                className: \"w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"或自定义用药规则\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            errors.usage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.usage\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"用药频次 *\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.frequency,\n                                onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent \".concat(errors.frequency ? 'border-red-300' : 'border-gray-300'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"请选择用药频次\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    frequencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option,\n                                            children: option\n                                        }, option, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.frequency,\n                                onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                className: \"w-full px-4 py-2 mt-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"或自定义用药频次\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            errors.frequency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.frequency\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"疗程时长（可选）\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                value: formData.duration,\n                                onChange: (e)=>handleInputChange('duration', e.target.value),\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                placeholder: \"请输入疗程天数\",\n                                min: \"1\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileText_Mic_MicOff_Pill_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"添加提醒\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            speechSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-800 mb-2\",\n                        children: \"语音输入提示：\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: '您可以说：\"阿司匹林肠溶片，每次1片，餐后1小时服用，每日3次\"'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\MedicineInputForm.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicineInputForm, \"QEmjyBvFEGpQ1/QEMGqYO17MO+Y=\", false, function() {\n    return [\n        _store_medication__WEBPACK_IMPORTED_MODULE_3__.useMedication\n    ];\n});\n_c = MedicineInputForm;\nvar _c;\n$RefreshReg$(_c, \"MedicineInputForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/medication/MedicineInputForm.tsx\n"));

/***/ })

});