"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth-demo/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/database.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Database)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Database = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Database\", [\n    [\n        \"ellipse\",\n        {\n            cx: \"12\",\n            cy: \"5\",\n            rx: \"9\",\n            ry: \"3\",\n            key: \"msslwz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5V19A9 3 0 0 0 21 19V5\",\n            key: \"1wlel7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 12A9 3 0 0 0 21 12\",\n            key: \"mv7ke4\"\n        }\n    ]\n]);\n //# sourceMappingURL=database.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/info.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Info)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Info = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Info\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 16v-4\",\n            key: \"1dtifu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 8h.01\",\n            key: \"e9boi3\"\n        }\n    ]\n]);\n //# sourceMappingURL=info.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wifi.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Wifi)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Wifi = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Wifi\", [\n    [\n        \"path\",\n        {\n            d: \"M5 13a10 10 0 0 1 14 0\",\n            key: \"6v8j51\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8.5 16.5a5 5 0 0 1 7 0\",\n            key: \"sej527\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 8.82a15 15 0 0 1 20 0\",\n            key: \"dnpr2z\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"20\",\n            y2: \"20\",\n            key: \"of4bc4\"\n        }\n    ]\n]);\n //# sourceMappingURL=wifi.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs":
/*!********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst X = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n]);\n //# sourceMappingURL=x.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxVQUFJLGlFQUFnQixDQUFDLEdBQUs7SUFDOUI7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWM7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzNDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUM1QyIsInNvdXJjZXMiOlsiRTpcXHNyY1xcaWNvbnNcXHgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBYXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVGdnTmlBMklERTRJaUF2UGdvZ0lEeHdZWFJvSUdROUltMDJJRFlnTVRJZ01USWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy94XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgWCA9IGNyZWF0ZUx1Y2lkZUljb24oJ1gnLCBbXG4gIFsncGF0aCcsIHsgZDogJ00xOCA2IDYgMTgnLCBrZXk6ICcxYmw1ZjgnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdtNiA2IDEyIDEyJywga2V5OiAnZDhiazZ2JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBYO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/auth-demo/page.tsx":
/*!************************************!*\
  !*** ./src/app/auth-demo/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthDemoPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,Lock,Mail,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.mjs\");\n/* harmony import */ var _components_DemoNotice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DemoNotice */ \"(app-pages-browser)/./src/components/DemoNotice.tsx\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 强制动态渲染\nconst dynamic = 'force-dynamic';\nfunction AuthDemoPage() {\n    _s();\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        name: '',\n        phone: '',\n        role: 'patient'\n    });\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        alert(\"\".concat(mode === 'signin' ? '登录' : '注册', \"演示 - 功能开发中\"));\n    };\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DemoNotice__WEBPACK_IMPORTED_MODULE_2__.DemoNotice, {}, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-12 h-12 text-red-500 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold text-gray-800\",\n                                        children: \"照护宝\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600\",\n                                children: \"认证系统演示\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                            children: mode === 'signin' ? '用户登录' : '用户注册'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: mode === 'signin' ? '登录您的账户' : '创建新账户'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        mode === 'signup' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"姓名 *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 66,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    name: \"name\",\n                                                                    type: \"text\",\n                                                                    value: formData.name,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"请输入您的姓名\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 67,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"手机号码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 84,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    name: \"phone\",\n                                                                    type: \"tel\",\n                                                                    value: formData.phone,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"请输入手机号码（可选）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"用户角色 *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"role\",\n                                                            value: formData.role,\n                                                            onChange: handleChange,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            required: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"patient\",\n                                                                    children: \"患者/长辈\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"family\",\n                                                                    children: \"家属\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 108,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"caregiver\",\n                                                                    children: \"护理员\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"邮箱地址 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            name: \"email\",\n                                                            type: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleChange,\n                                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"请输入邮箱地址\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"密码 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            name: \"password\",\n                                                            type: showPassword ? 'text' : 'password',\n                                                            value: formData.password,\n                                                            onChange: handleChange,\n                                                            className: \"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"请输入密码\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 37\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_Lock_Mail_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 70\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"w-full text-white py-3 px-4 rounded-lg transition-colors \".concat(mode === 'signin' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-green-600 hover:bg-green-700'),\n                                            children: mode === 'signin' ? '登录' : '注册'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            mode === 'signin' ? '还没有账户？' : '已有账户？',\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setMode(mode === 'signin' ? 'signup' : 'signin'),\n                                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                children: mode === 'signin' ? '立即注册' : '立即登录'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-800 text-sm text-center\",\n                                        children: \"\\uD83D\\uDCA1 这是认证系统的演示版本，实际功能需要配置Supabase数据库\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                            children: \"← 返回主页\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth-demo\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthDemoPage, \"2m00bt6BdFhuIvilp44JkelEY2g=\");\n_c = AuthDemoPage;\nvar _c;\n$RefreshReg$(_c, \"AuthDemoPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth-demo/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DemoNotice.tsx":
/*!***************************************!*\
  !*** ./src/components/DemoNotice.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DemoNotice: () => (/* binding */ DemoNotice)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Info,Wifi,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.mjs\");\n/* harmony import */ var _barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Info,Wifi,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.mjs\");\n/* harmony import */ var _barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Info,Wifi,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.mjs\");\n/* harmony import */ var _barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Info,Wifi,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* __next_internal_client_entry_do_not_use__ DemoNotice auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DemoNotice() {\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    if (!isVisible) return null;\n    const isSupabaseConfigured = ()=>{\n        return  false && 0;\n    };\n    if (isSupabaseConfigured()) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-6 h-6 flex-shrink-0 mt-0.5\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2\",\n                                children: \"\\uD83D\\uDE80 演示模式\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-3\",\n                                children: \"当前运行在演示模式下，数据将保存在浏览器本地存储中。要启用完整功能，请配置Supabase数据库。\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"数据存储：本地浏览器\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"同步功能：已禁用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 text-xs opacity-90\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"配置步骤：\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"1. 创建Supabase项目 → 2. 更新.env.local文件 → 3. 执行数据库迁移\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsVisible(false),\n                        className: \"text-white hover:text-gray-200 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Info_Wifi_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\DemoNotice.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(DemoNotice, \"C45KFF5iQHXNkju7O/pllv86QL4=\");\n_c = DemoNotice;\nvar _c;\n$RefreshReg$(_c, \"DemoNotice\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DemoNotice.tsx\n"));

/***/ })

});