import { speechService } from './speech-service'
import type { MedicineR<PERSON>inder, ReminderSettings, GuardianContact } from '@/types'

export interface ReminderNotification {
  id: string
  reminderId: string
  medicineName: string
  dosage: string
  usage?: string
  scheduledTime: string
  level: 1 | 2 | 3 // 提醒级别
  isActive: boolean
  createdAt: Date
}

export class NotificationService {
  private activeReminders = new Map<string, ReminderNotification>()
  private reminderTimeouts = new Map<string, NodeJS.Timeout>()
  private notificationPermission: NotificationPermission = 'default'
  private reminderAudio: HTMLAudioElement | null = null
  private isAudioInitialized = false

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeNotifications()
      this.initializeAudio()
    }
  }

  /**
   * 初始化通知权限
   */
  private async initializeNotifications() {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      this.notificationPermission = Notification.permission

      if (this.notificationPermission === 'default') {
        this.notificationPermission = await Notification.requestPermission()
      }
    }
  }

  /**
   * 初始化音频系统
   */
  private initializeAudio() {
    if (typeof window === 'undefined') return

    try {
      // 预加载提醒音效
      this.reminderAudio = new Audio()
      this.reminderAudio.preload = 'auto'
      this.reminderAudio.volume = 0.7

      // 尝试加载多种音效格式
      const audioSources = [
        '/sounds/reminder.mp3',
        '/sounds/reminder.wav',
        '/sounds/reminder.ogg'
      ]

      // 使用第一个可用的音频格式
      for (const src of audioSources) {
        this.reminderAudio.src = src
        break
      }

      // 如果没有音频文件，创建合成音效
      if (!this.reminderAudio.src) {
        this.createSyntheticReminderSound()
      }

      this.isAudioInitialized = true
    } catch (error) {
      console.warn('音频初始化失败，将使用合成音效:', error)
      this.createSyntheticReminderSound()
    }
  }

  /**
   * 创建合成提醒音效
   */
  private createSyntheticReminderSound() {
    if (typeof window === 'undefined') return

    try {
      // 使用Web Audio API创建合成音效
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      const createBeep = (frequency: number, duration: number, delay: number = 0) => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            const oscillator = audioContext.createOscillator()
            const gainNode = audioContext.createGain()

            oscillator.connect(gainNode)
            gainNode.connect(audioContext.destination)

            oscillator.frequency.value = frequency
            oscillator.type = 'sine'

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration)

            oscillator.start(audioContext.currentTime)
            oscillator.stop(audioContext.currentTime + duration)

            setTimeout(resolve, duration * 1000)
          }, delay)
        })
      }

      // 创建自定义提醒音效播放函数
      this.playCustomReminderSound = async () => {
        try {
          await createBeep(800, 0.2, 0)    // 第一声
          await createBeep(1000, 0.2, 100) // 第二声
          await createBeep(800, 0.3, 200)  // 第三声
        } catch (error) {
          console.error('播放合成音效失败:', error)
        }
      }
    } catch (error) {
      console.warn('Web Audio API不可用:', error)
    }
  }

  private playCustomReminderSound: (() => Promise<void>) | null = null

  /**
   * 请求通知权限
   */
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    const permission = await Notification.requestPermission()
    this.notificationPermission = permission
    return permission === 'granted'
  }

  /**
   * 创建用药提醒
   */
  scheduleReminder(
    reminder: MedicineReminder,
    settings: ReminderSettings,
    onConfirm: (reminderId: string, confirmed: boolean) => void
  ): void {
    reminder.scheduledTimes.forEach(time => {
      const scheduledDateTime = this.getNextScheduledDateTime(time)
      const reminderId = `${reminder.id}-${time}`
      
      // 计算第一级提醒时间
      const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000)
      
      const timeout = setTimeout(() => {
        this.triggerReminder(reminder, time, settings, onConfirm)
      }, firstReminderTime.getTime() - Date.now())

      this.reminderTimeouts.set(reminderId, timeout)
    })
  }

  /**
   * 触发提醒
   */
  private async triggerReminder(
    reminder: MedicineReminder,
    scheduledTime: string,
    settings: ReminderSettings,
    onConfirm: (reminderId: string, confirmed: boolean) => void
  ): Promise<void> {
    const notificationId = `${reminder.id}-${scheduledTime}-${Date.now()}`
    
    const notification: ReminderNotification = {
      id: notificationId,
      reminderId: reminder.id,
      medicineName: reminder.medicineName,
      dosage: reminder.dosage,
      usage: reminder.usage,
      scheduledTime,
      level: 1,
      isActive: true,
      createdAt: new Date()
    }

    this.activeReminders.set(notificationId, notification)

    // 第一级提醒
    await this.showFirstLevelReminder(notification, settings)

    // 设置重复提醒
    this.scheduleRepeatedReminders(notification, settings, onConfirm)
  }

  /**
   * 第一级提醒：弹窗 + 声音
   */
  private async showFirstLevelReminder(
    notification: ReminderNotification,
    settings: ReminderSettings
  ): Promise<void> {
    // 浏览器通知
    if (settings.notificationEnabled && this.notificationPermission === 'granted') {
      const browserNotification = new Notification(`用药提醒：${notification.medicineName}`, {
        body: `请服用 ${notification.dosage}`,
        icon: '/icons/medicine.png',
        badge: '/icons/badge.png',
        tag: notification.id,
        requireInteraction: true,
        actions: [
          { action: 'confirm', title: '已服药' },
          { action: 'snooze', title: '稍后提醒' }
        ]
      })

      browserNotification.onclick = () => {
        this.handleReminderConfirmation(notification.id, true)
        browserNotification.close()
      }
    }

    // 声音提醒
    if (settings.soundEnabled) {
      await this.playReminderSound()
    }

    // 页面弹窗（通过事件通知UI组件）
    this.notifyUI('reminder-popup', notification)
  }

  /**
   * 第二级提醒：增加语音播报和强化音效
   */
  private async showSecondLevelReminder(
    notification: ReminderNotification,
    settings: ReminderSettings
  ): Promise<void> {
    notification.level = 2

    // 浏览器通知（更紧急）
    if (settings.notificationEnabled && this.notificationPermission === 'granted') {
      const browserNotification = new Notification(`⚠️ 重要提醒：${notification.medicineName}`, {
        body: `请立即服用 ${notification.dosage}`,
        icon: '/icons/medicine-urgent.png',
        badge: '/icons/badge-urgent.png',
        tag: notification.id,
        requireInteraction: true,
        silent: false,
        actions: [
          { action: 'confirm', title: '已服药' },
          { action: 'snooze', title: '稍后提醒' }
        ]
      })

      browserNotification.onclick = () => {
        this.handleReminderConfirmation(notification.id, true)
        browserNotification.close()
      }
    }

    // 强化音效提醒
    if (settings.soundEnabled) {
      await this.playIntensiveReminderSound()
    }

    // 语音播报
    if (settings.voiceEnabled && speechService.isSpeechSynthesisSupported()) {
      const speechText = speechService.generateReminderSpeech(
        notification.medicineName,
        notification.dosage,
        notification.usage
      )

      try {
        await speechService.speak({ text: speechText })
      } catch (error) {
        console.error('语音播报失败:', error)
      }
    }

    // 页面弹窗（更显眼的样式）
    this.notifyUI('reminder-popup-urgent', notification)
  }

  /**
   * 第三级提醒：确认是否已服药（最高级别）
   */
  private async showThirdLevelReminder(
    notification: ReminderNotification,
    settings: ReminderSettings
  ): Promise<void> {
    notification.level = 3

    // 最高级别浏览器通知
    if (settings.notificationEnabled && this.notificationPermission === 'granted') {
      const browserNotification = new Notification(`🚨 紧急提醒：${notification.medicineName}`, {
        body: `您可能忘记服药了！请确认是否已服用 ${notification.dosage}`,
        icon: '/icons/medicine-emergency.png',
        badge: '/icons/badge-emergency.png',
        tag: notification.id,
        requireInteraction: true,
        silent: false,
        vibrate: [200, 100, 200], // 震动模式
        actions: [
          { action: 'confirm', title: '已服药' },
          { action: 'missed', title: '忘记了' },
          { action: 'snooze', title: '稍后确认' }
        ]
      })

      browserNotification.onclick = () => {
        this.handleReminderConfirmation(notification.id, true)
        browserNotification.close()
      }
    }

    // 连续强化音效
    if (settings.soundEnabled) {
      await this.playIntensiveReminderSound()
      // 间隔后再次播放
      setTimeout(async () => {
        await this.playIntensiveReminderSound()
      }, 2000)
    }

    // 询问是否已服药
    const confirmationText = `重要提醒！您是否已经服用了${notification.medicineName}？如果忘记了，请立即服用。`

    if (settings.voiceEnabled && speechService.isSpeechSynthesisSupported()) {
      try {
        await speechService.speak({
          text: confirmationText,
          rate: 0.9, // 稍慢的语速
          pitch: 1.1, // 稍高的音调
          volume: 1.0 // 最大音量
        })
      } catch (error) {
        console.error('语音播报失败:', error)
      }
    }

    // 显示紧急确认对话框
    this.notifyUI('confirmation-dialog-urgent', notification)

    // 如果支持，尝试使页面闪烁提醒
    this.triggerPageFlash()
  }

  /**
   * 触发页面闪烁效果
   */
  private triggerPageFlash(): void {
    if (typeof document === 'undefined') return

    try {
      const originalTitle = document.title
      let flashCount = 0
      const maxFlashes = 6

      const flashInterval = setInterval(() => {
        document.title = flashCount % 2 === 0 ? '🚨 用药提醒！' : originalTitle
        flashCount++

        if (flashCount >= maxFlashes) {
          clearInterval(flashInterval)
          document.title = originalTitle
        }
      }, 500)

      // 页面可见性变化时停止闪烁
      const handleVisibilityChange = () => {
        if (!document.hidden) {
          clearInterval(flashInterval)
          document.title = originalTitle
          document.removeEventListener('visibilitychange', handleVisibilityChange)
        }
      }

      document.addEventListener('visibilitychange', handleVisibilityChange)
    } catch (error) {
      console.error('页面闪烁效果失败:', error)
    }
  }

  /**
   * 设置重复提醒
   */
  private scheduleRepeatedReminders(
    notification: ReminderNotification,
    settings: ReminderSettings,
    onConfirm: (reminderId: string, confirmed: boolean) => void
  ): void {
    let reminderCount = 1
    const maxReminders = settings.maxReminders

    const scheduleNext = () => {
      if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {
        // 达到最大提醒次数，通知监护人
        if (reminderCount >= maxReminders) {
          this.notifyGuardians(notification, settings)
        }
        return
      }

      const timeout = setTimeout(async () => {
        if (!this.activeReminders.has(notification.id)) return

        reminderCount++

        if (reminderCount === 2) {
          await this.showSecondLevelReminder(notification, settings)
        } else if (reminderCount >= 3) {
          await this.showThirdLevelReminder(notification, settings)
        }

        scheduleNext()
      }, settings.reminderInterval * 60000)

      this.reminderTimeouts.set(`${notification.id}-repeat-${reminderCount}`, timeout)
    }

    scheduleNext()
  }

  /**
   * 处理提醒确认
   */
  handleReminderConfirmation(notificationId: string, confirmed: boolean): void {
    const notification = this.activeReminders.get(notificationId)
    if (!notification) return

    notification.isActive = false
    this.activeReminders.delete(notificationId)

    // 清除相关的定时器
    this.clearReminderTimeouts(notificationId)

    // 通知UI更新
    this.notifyUI('reminder-confirmed', { notificationId, confirmed })
  }

  /**
   * 通知监护人
   */
  private async notifyGuardians(
    notification: ReminderNotification,
    settings: ReminderSettings
  ): Promise<void> {
    // 延迟通知监护人
    setTimeout(() => {
      this.notifyUI('guardian-notification', {
        notification,
        message: `患者可能忘记服用${notification.medicineName}，请及时关注。`
      })
    }, settings.guardianNotificationDelay * 60000)
  }

  /**
   * 播放提醒声音
   */
  private async playReminderSound(): Promise<void> {
    try {
      // 优先使用预加载的音频
      if (this.reminderAudio && this.isAudioInitialized) {
        this.reminderAudio.currentTime = 0 // 重置播放位置
        await this.reminderAudio.play()
        return
      }

      // 如果预加载音频不可用，使用合成音效
      if (this.playCustomReminderSound) {
        await this.playCustomReminderSound()
        return
      }

      // 最后的备选方案：简单的beep音效
      this.playFallbackSound()
    } catch (error) {
      console.error('播放提醒声音失败:', error)
      // 如果所有音效都失败，尝试备选方案
      this.playFallbackSound()
    }
  }

  /**
   * 备选音效（系统beep）
   */
  private playFallbackSound(): void {
    try {
      // 使用系统默认音效
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance('')
        utterance.volume = 0.1
        speechSynthesis.speak(utterance)
      }
    } catch (error) {
      console.warn('备选音效也无法播放:', error)
    }
  }

  /**
   * 播放连续提醒音效（用于重要提醒）
   */
  private async playIntensiveReminderSound(): Promise<void> {
    for (let i = 0; i < 3; i++) {
      await this.playReminderSound()
      await new Promise(resolve => setTimeout(resolve, 500)) // 间隔500ms
    }
  }

  /**
   * 通知UI组件
   */
  private notifyUI(event: string, data: any): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`medication-${event}`, { detail: data }))
    }
  }

  /**
   * 清除提醒定时器
   */
  private clearReminderTimeouts(notificationId: string): void {
    // 清除主定时器
    const mainTimeout = this.reminderTimeouts.get(notificationId)
    if (mainTimeout) {
      clearTimeout(mainTimeout)
      this.reminderTimeouts.delete(notificationId)
    }

    // 清除重复提醒定时器
    for (const [key, timeout] of this.reminderTimeouts.entries()) {
      if (key.startsWith(`${notificationId}-repeat-`)) {
        clearTimeout(timeout)
        this.reminderTimeouts.delete(key)
      }
    }
  }

  /**
   * 获取下次计划时间
   */
  private getNextScheduledDateTime(time: string): Date {
    const [hours, minutes] = time.split(':').map(Number)
    const now = new Date()
    const scheduled = new Date()
    
    scheduled.setHours(hours, minutes, 0, 0)
    
    // 如果时间已过，设置为明天
    if (scheduled <= now) {
      scheduled.setDate(scheduled.getDate() + 1)
    }
    
    return scheduled
  }

  /**
   * 取消所有活动提醒
   */
  cancelAllReminders(): void {
    this.activeReminders.clear()
    
    for (const timeout of this.reminderTimeouts.values()) {
      clearTimeout(timeout)
    }
    this.reminderTimeouts.clear()
  }

  /**
   * 取消特定提醒
   */
  cancelReminder(reminderId: string): void {
    // 找到并删除相关的活动提醒
    for (const [id, notification] of this.activeReminders.entries()) {
      if (notification.reminderId === reminderId) {
        this.activeReminders.delete(id)
        this.clearReminderTimeouts(id)
      }
    }
  }

  /**
   * 获取活动提醒列表
   */
  getActiveReminders(): ReminderNotification[] {
    return Array.from(this.activeReminders.values())
  }

  /**
   * 检查通知权限状态
   */
  getNotificationPermission(): NotificationPermission {
    return this.notificationPermission
  }
}

// 延迟初始化，避免服务器端渲染问题
let notificationServiceInstance: NotificationService | null = null

export const getNotificationService = (): NotificationService => {
  if (typeof window === 'undefined') {
    // 服务器端返回一个空的实现
    return {
      requestNotificationPermission: async () => false,
      scheduleReminder: () => {},
      cancelAllReminders: () => {},
      cancelReminder: () => {},
      getActiveReminders: () => [],
      getNotificationPermission: () => 'default' as NotificationPermission,
      handleReminderConfirmation: () => {}
    } as any
  }

  if (!notificationServiceInstance) {
    notificationServiceInstance = new NotificationService()
  }

  return notificationServiceInstance
}

// 只在客户端导出实例
export const notificationService = typeof window !== 'undefined' ? getNotificationService() : null as any
