import { createBrowserClient } from '@supabase/ssr'

// 浏览器端客户端 - 用于客户端组件
export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// 默认导出浏览器客户端实例
export const supabase = createClient()

// 数据库表名常量
export const TABLES = {
  USERS: 'users',
  MEDICINE_REMINDERS: 'medicine_reminders',
  HEALTH_DATA: 'health_data',
  EMERGENCY_CONTACTS: 'emergency_contacts',
  EMERGENCY_CALLS: 'emergency_calls',
  FAMILY_MESSAGES: 'family_messages',
  DEVICES: 'devices',
} as const

// 数据库类型定义
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          phone: string | null
          avatar: string | null
          role: 'patient' | 'family' | 'caregiver'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          phone?: string | null
          avatar?: string | null
          role?: 'patient' | 'family' | 'caregiver'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          phone?: string | null
          avatar?: string | null
          role?: 'patient' | 'family' | 'caregiver'
          updated_at?: string
        }
      }
      medicine_reminders: {
        Row: {
          id: string
          medicine_name: string
          dosage: string
          time: string
          repeat_days: string
          is_enabled: boolean
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          medicine_name: string
          dosage: string
          time: string
          repeat_days: string
          is_enabled?: boolean
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          medicine_name?: string
          dosage?: string
          time?: string
          repeat_days?: string
          is_enabled?: boolean
          updated_at?: string
          user_id?: string
        }
      }
      health_data: {
        Row: {
          id: string
          type: 'blood_pressure' | 'blood_sugar' | 'heart_rate' | 'temperature'
          value: number
          unit: string
          measured_at: string
          notes: string | null
          user_id: string
          device_id: string | null
          systolic: number | null
          diastolic: number | null
          pulse: number | null
          measurement_type: string | null
        }
        Insert: {
          id?: string
          type: 'blood_pressure' | 'blood_sugar' | 'heart_rate' | 'temperature'
          value: number
          unit: string
          measured_at: string
          notes?: string | null
          user_id: string
          device_id?: string | null
          systolic?: number | null
          diastolic?: number | null
          pulse?: number | null
          measurement_type?: string | null
        }
        Update: {
          id?: string
          type?: 'blood_pressure' | 'blood_sugar' | 'heart_rate' | 'temperature'
          value?: number
          unit?: string
          measured_at?: string
          notes?: string | null
          user_id?: string
          device_id?: string | null
          systolic?: number | null
          diastolic?: number | null
          pulse?: number | null
          measurement_type?: string | null
        }
      }
    }
  }
}
