"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 初始化音频系统\n   */ initializeAudio() {\n        if (false) {}\n        try {\n            // 预加载提醒音效\n            this.reminderAudio = new Audio();\n            this.reminderAudio.preload = 'auto';\n            this.reminderAudio.volume = 0.7;\n            // 尝试加载多种音效格式\n            const audioSources = [\n                '/sounds/reminder.mp3',\n                '/sounds/reminder.wav',\n                '/sounds/reminder.ogg'\n            ];\n            // 使用第一个可用的音频格式\n            for (const src of audioSources){\n                this.reminderAudio.src = src;\n                break;\n            }\n            // 如果没有音频文件，创建合成音效\n            if (!this.reminderAudio.src) {\n                this.createSyntheticReminderSound();\n            }\n            this.isAudioInitialized = true;\n        } catch (error) {\n            console.warn('音频初始化失败，将使用合成音效:', error);\n            this.createSyntheticReminderSound();\n        }\n    }\n    /**\n   * 创建合成提醒音效\n   */ createSyntheticReminderSound() {\n        if (false) {}\n        try {\n            // 使用Web Audio API创建合成音效\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const createBeep = function(frequency, duration) {\n                let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const oscillator = audioContext.createOscillator();\n                        const gainNode = audioContext.createGain();\n                        oscillator.connect(gainNode);\n                        gainNode.connect(audioContext.destination);\n                        oscillator.frequency.value = frequency;\n                        oscillator.type = 'sine';\n                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n                        oscillator.start(audioContext.currentTime);\n                        oscillator.stop(audioContext.currentTime + duration);\n                        setTimeout(resolve, duration * 1000);\n                    }, delay);\n                });\n            };\n            // 创建自定义提醒音效播放函数\n            this.playCustomReminderSound = async ()=>{\n                try {\n                    await createBeep(800, 0.2, 0) // 第一声\n                    ;\n                    await createBeep(1000, 0.2, 100) // 第二声\n                    ;\n                    await createBeep(800, 0.3, 200) // 第三声\n                    ;\n                } catch (error) {\n                    console.error('播放合成音效失败:', error);\n                }\n            };\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            await this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报和强化音效\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 浏览器通知（更紧急）\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"⚠️ 重要提醒：\".concat(notification.medicineName), {\n                body: \"请立即服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-urgent.png',\n                badge: '/icons/badge-urgent.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 强化音效提醒\n        if (settings.soundEnabled) {\n            await this.playIntensiveReminderSound();\n        }\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 页面弹窗（更显眼的样式）\n        this.notifyUI('reminder-popup-urgent', notification);\n    }\n    /**\n   * 第三级提醒：确认是否已服药（最高级别）\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 最高级别浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"\\uD83D\\uDEA8 紧急提醒：\".concat(notification.medicineName), {\n                body: \"您可能忘记服药了！请确认是否已服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-emergency.png',\n                badge: '/icons/badge-emergency.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                vibrate: [\n                    200,\n                    100,\n                    200\n                ],\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'missed',\n                        title: '忘记了'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后确认'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 连续强化音效\n        if (settings.soundEnabled) {\n            await this.playIntensiveReminderSound();\n            // 间隔后再次播放\n            setTimeout(async ()=>{\n                await this.playIntensiveReminderSound();\n            }, 2000);\n        }\n        // 询问是否已服药\n        const confirmationText = \"重要提醒！您是否已经服用了\".concat(notification.medicineName, \"？如果忘记了，请立即服用。\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText,\n                    rate: 0.9,\n                    pitch: 1.1,\n                    volume: 1.0 // 最大音量\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示紧急确认对话框\n        this.notifyUI('confirmation-dialog-urgent', notification);\n        // 如果支持，尝试使页面闪烁提醒\n        this.triggerPageFlash();\n    }\n    /**\n   * 触发页面闪烁效果\n   */ triggerPageFlash() {\n        if (typeof document === 'undefined') return;\n        try {\n            const originalTitle = document.title;\n            let flashCount = 0;\n            const maxFlashes = 6;\n            const flashInterval = setInterval(()=>{\n                document.title = flashCount % 2 === 0 ? '🚨 用药提醒！' : originalTitle;\n                flashCount++;\n                if (flashCount >= maxFlashes) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                }\n            }, 500);\n            // 页面可见性变化时停止闪烁\n            const handleVisibilityChange = ()=>{\n                if (!document.hidden) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                }\n            };\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n        } catch (error) {\n            console.error('页面闪烁效果失败:', error);\n        }\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ async playReminderSound() {\n        try {\n            // 优先使用预加载的音频\n            if (this.reminderAudio && this.isAudioInitialized) {\n                this.reminderAudio.currentTime = 0 // 重置播放位置\n                ;\n                await this.reminderAudio.play();\n                return;\n            }\n            // 如果预加载音频不可用，使用合成音效\n            if (this.playCustomReminderSound) {\n                await this.playCustomReminderSound();\n                return;\n            }\n            // 最后的备选方案：简单的beep音效\n            this.playFallbackSound();\n        } catch (error) {\n            console.error('播放提醒声音失败:', error);\n            // 如果所有音效都失败，尝试备选方案\n            this.playFallbackSound();\n        }\n    }\n    /**\n   * 备选音效（系统beep）\n   */ playFallbackSound() {\n        try {\n            // 使用系统默认音效\n            if ('speechSynthesis' in window) {\n                const utterance = new SpeechSynthesisUtterance('');\n                utterance.volume = 0.1;\n                speechSynthesis.speak(utterance);\n            }\n        } catch (error) {\n            console.warn('备选音效也无法播放:', error);\n        }\n    }\n    /**\n   * 播放连续提醒音效（用于重要提醒）\n   */ async playIntensiveReminderSound() {\n        for(let i = 0; i < 3; i++){\n            await this.playReminderSound();\n            await new Promise((resolve)=>setTimeout(resolve, 500)) // 间隔500ms\n            ;\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        this.reminderAudio = null;\n        this.isAudioInitialized = false;\n        this.playCustomReminderSound = null;\n        if (true) {\n            this.initializeNotifications();\n            this.initializeAudio();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});