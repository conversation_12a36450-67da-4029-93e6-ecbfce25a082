"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 重复第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n    }\n    /**\n   * 第三级提醒：确认是否已服药\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 询问是否已服药\n        const confirmationText = \"您是否已经服用了\".concat(notification.medicineName, \"？\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示确认对话框\n        this.notifyUI('confirmation-dialog', notification);\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ playReminderSound() {\n        try {\n            const audio = new Audio('/sounds/reminder.mp3');\n            audio.volume = 0.7;\n            audio.play().catch((error)=>{\n                console.error('播放提醒声音失败:', error);\n            });\n        } catch (error) {\n            console.error('创建音频对象失败:', error);\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        if (true) {\n            this.initializeNotifications();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});