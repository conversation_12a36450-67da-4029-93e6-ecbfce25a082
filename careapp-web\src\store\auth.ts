import { create } from 'zustand'
import { authService, type AuthUser } from '@/lib/auth'

interface AuthState {
  user: AuthUser | null
  loading: boolean
  initialized: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (data: {
    email: string
    password: string
    name: string
    phone?: string
    role: 'patient' | 'family' | 'caregiver'
  }) => Promise<void>
  signOut: () => Promise<void>
  initialize: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: false,
  initialized: false,

  initialize: async () => {
    try {
      set({ loading: true })
      
      // 获取当前用户
      const user = await authService.getCurrentUser()
      set({ user, initialized: true })

      // 监听认证状态变化
      authService.onAuthStateChange((user) => {
        set({ user })
      })
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      set({ user: null, initialized: true })
    } finally {
      set({ loading: false })
    }
  },

  signIn: async (email: string, password: string) => {
    try {
      set({ loading: true })
      await authService.signIn({ email, password })
      
      // 获取更新后的用户信息
      const user = await authService.getCurrentUser()
      set({ user })
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      set({ loading: false })
    }
  },

  signUp: async (data) => {
    try {
      set({ loading: true })
      await authService.signUp(data)
      
      // 注册后自动获取用户信息
      const user = await authService.getCurrentUser()
      set({ user })
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      set({ loading: false })
    }
  },

  signOut: async () => {
    try {
      set({ loading: true })
      await authService.signOut()
      set({ user: null })
    } catch (error) {
      console.error('登出失败:', error)
      throw error
    } finally {
      set({ loading: false })
    }
  },

  resetPassword: async (email: string) => {
    try {
      set({ loading: true })
      await authService.resetPassword(email)
    } catch (error) {
      console.error('重置密码失败:', error)
      throw error
    } finally {
      set({ loading: false })
    }
  },
}))

// 便捷的hooks
export const useAuth = () => {
  const store = useAuthStore()
  return {
    user: store.user,
    loading: store.loading,
    initialized: store.initialized,
    isAuthenticated: !!store.user,
    signIn: store.signIn,
    signUp: store.signUp,
    signOut: store.signOut,
    initialize: store.initialize,
    resetPassword: store.resetPassword,
  }
}
