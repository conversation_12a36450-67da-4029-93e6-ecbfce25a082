# 照护宝桌面快捷方式使用说明

## 📋 概述

为了方便您随时访问照护宝系统，我们提供了桌面快捷方式创建工具。

## 🚀 快速开始

### 方法一：使用批处理文件（推荐）

1. **双击运行** `创建桌面快捷方式.bat`
2. 按照提示选择是否启动开发服务器
3. 完成后桌面将出现快捷方式

### 方法二：使用PowerShell脚本

1. 右键点击 `create-desktop-shortcut.ps1`
2. 选择"使用PowerShell运行"
3. 按照提示操作

## 📱 创建的快捷方式

运行脚本后，桌面将创建以下快捷方式：

| 快捷方式 | 功能 | 访问地址 |
|---------|------|----------|
| 📱 照护宝 - 主页 | 系统主页，查看所有功能 | http://localhost:3000 |
| 💊 照护宝 - 用药提醒 | 直接进入用药提醒系统 | http://localhost:3000/medication |
| 🔐 照护宝 - 登录注册 | 用户认证页面 | http://localhost:3000/auth-demo |

## 🔧 服务器管理

### 启动服务器

**方法一：使用快捷启动**
- 双击 `启动照护宝服务器.bat`

**方法二：手动启动**
```bash
npm run dev
```

### 停止服务器
- 在服务器窗口按 `Ctrl + C`
- 或直接关闭命令行窗口

## 💡 使用提示

### ✅ 正常使用流程

1. **启动服务器**
   - 双击 `启动照护宝服务器.bat`
   - 等待显示 "Ready in XXXms"

2. **访问系统**
   - 双击桌面快捷方式
   - 或在浏览器输入 http://localhost:3000

3. **开始使用**
   - 注册/登录账户
   - 体验用药提醒功能

### ⚠️ 注意事项

- **服务器必须运行**：快捷方式需要开发服务器运行才能访问
- **端口占用**：确保3000端口未被其他程序占用
- **网络连接**：本地开发无需网络，但某些功能可能需要
- **浏览器兼容**：推荐使用Chrome、Edge或Firefox

### 🔍 故障排除

**问题：点击快捷方式无法访问**
- 检查开发服务器是否正在运行
- 确认地址栏显示 http://localhost:3000

**问题：服务器启动失败**
- 检查Node.js是否已安装
- 运行 `npm install` 安装依赖
- 检查3000端口是否被占用

**问题：页面显示错误**
- 刷新浏览器页面
- 重启开发服务器
- 清除浏览器缓存

## 📞 技术支持

如果遇到问题，请检查：

1. **环境要求**
   - Node.js 18+ 已安装
   - npm 包管理器可用
   - 3000端口可用

2. **常用命令**
   ```bash
   # 安装依赖
   npm install
   
   # 启动开发服务器
   npm run dev
   
   # 检查端口占用
   netstat -ano | findstr :3000
   ```

3. **重新创建快捷方式**
   - 删除桌面现有快捷方式
   - 重新运行创建脚本

## 🎯 功能特色

- **一键访问**：桌面快捷方式直达功能页面
- **自动启动**：可选择自动启动开发服务器
- **多入口**：提供主页、用药提醒、认证等多个入口
- **便捷管理**：独立的服务器启动脚本

---

**享受便捷的照护宝体验！** 🎉
