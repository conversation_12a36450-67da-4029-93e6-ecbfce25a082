import { create } from 'zustand'
import { medicationService } from '@/lib/medication-service'
import { medicationScheduler } from '@/lib/medication-scheduler'
import { notificationService } from '@/lib/notification-service'
import type { 
  MedicineReminder, 
  DailySchedule, 
  MedicationRecord, 
  ReminderSettings,
  GuardianContact 
} from '@/types'

interface MedicationState {
  // 数据状态
  reminders: MedicineReminder[]
  dailySchedule: DailySchedule | null
  reminderSettings: ReminderSettings | null
  guardianContacts: GuardianContact[]
  medicationRecords: MedicationRecord[]
  
  // UI状态
  loading: boolean
  error: string | null
  
  // 操作方法
  loadReminders: (userId: string) => Promise<void>
  addReminder: (reminder: Omit<MedicineReminder, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateReminder: (id: string, updates: Partial<MedicineReminder>) => Promise<void>
  deleteReminder: (id: string) => Promise<void>
  
  loadDailySchedule: (userId: string) => Promise<void>
  saveDailySchedule: (schedule: Omit<DailySchedule, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  
  loadReminderSettings: (userId: string) => Promise<void>
  saveReminderSettings: (settings: Omit<ReminderSettings, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  
  loadGuardianContacts: (userId: string) => Promise<void>
  addGuardianContact: (contact: Omit<GuardianContact, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  
  recordMedication: (record: Omit<MedicationRecord, 'id' | 'createdAt'>) => Promise<void>
  loadMedicationRecords: (userId: string, startDate?: string, endDate?: string) => Promise<void>
  
  calculateMedicationTimes: (usage: string, frequency: string) => string[]
  startReminderSystem: (userId: string) => Promise<void>
  stopReminderSystem: () => void
  
  clearError: () => void
}

export const useMedicationStore = create<MedicationState>((set, get) => ({
  // 初始状态
  reminders: [],
  dailySchedule: null,
  reminderSettings: null,
  guardianContacts: [],
  medicationRecords: [],
  loading: false,
  error: null,

  // 加载用药提醒列表
  loadReminders: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const reminders = await medicationService.getMedicineReminders(userId)
      set({ reminders, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '加载提醒失败', loading: false })
    }
  },

  // 添加用药提醒
  addReminder: async (reminderData) => {
    try {
      set({ loading: true, error: null })
      
      // 如果有作息时间，自动计算用药时间
      const { dailySchedule } = get()
      if (dailySchedule && reminderData.scheduledTimes.length === 0) {
        const calculatedTimes = medicationScheduler.calculateMedicationTimes(
          reminderData.usage,
          reminderData.frequency,
          dailySchedule
        )
        reminderData.scheduledTimes = medicationScheduler.validateAndAdjustTimes(calculatedTimes)
      }

      const reminder = await medicationService.createMedicineReminder(reminderData)
      const { reminders } = get()
      set({ reminders: [reminder, ...reminders], loading: false })
      
      // 如果启用了提醒，重新启动提醒系统
      if (reminder.isEnabled) {
        const { reminderSettings } = get()
        if (reminderSettings) {
          notificationService.scheduleReminder(reminder, reminderSettings, (reminderId, confirmed) => {
            get().recordMedication({
              reminderId,
              scheduledTime: new Date().toISOString(),
              status: confirmed ? 'taken' : 'missed',
              confirmationMethod: 'manual',
              userId: reminderData.userId
            })
          })
        }
      }
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '添加提醒失败', loading: false })
    }
  },

  // 更新用药提醒
  updateReminder: async (id: string, updates) => {
    try {
      set({ loading: true, error: null })
      const updatedReminder = await medicationService.updateMedicineReminder(id, updates)
      const { reminders } = get()
      const newReminders = reminders.map(r => r.id === id ? updatedReminder : r)
      set({ reminders: newReminders, loading: false })
      
      // 更新提醒系统
      notificationService.cancelReminder(id)
      if (updatedReminder.isEnabled) {
        const { reminderSettings } = get()
        if (reminderSettings) {
          notificationService.scheduleReminder(updatedReminder, reminderSettings, (reminderId, confirmed) => {
            get().recordMedication({
              reminderId,
              scheduledTime: new Date().toISOString(),
              status: confirmed ? 'taken' : 'missed',
              confirmationMethod: 'manual',
              userId: updatedReminder.userId
            })
          })
        }
      }
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '更新提醒失败', loading: false })
    }
  },

  // 删除用药提醒
  deleteReminder: async (id: string) => {
    try {
      set({ loading: true, error: null })
      await medicationService.deleteMedicineReminder(id)
      const { reminders } = get()
      set({ reminders: reminders.filter(r => r.id !== id), loading: false })
      
      // 取消相关提醒
      notificationService.cancelReminder(id)
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '删除提醒失败', loading: false })
    }
  },

  // 加载作息时间
  loadDailySchedule: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const schedule = await medicationService.getDailySchedule(userId)
      set({ dailySchedule: schedule, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '加载作息时间失败', loading: false })
    }
  },

  // 保存作息时间
  saveDailySchedule: async (scheduleData) => {
    try {
      set({ loading: true, error: null })
      const schedule = await medicationService.saveDailySchedule(scheduleData)
      set({ dailySchedule: schedule, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '保存作息时间失败', loading: false })
    }
  },

  // 加载提醒设置
  loadReminderSettings: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const settings = await medicationService.getReminderSettings(userId)
      set({ reminderSettings: settings, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '加载提醒设置失败', loading: false })
    }
  },

  // 保存提醒设置
  saveReminderSettings: async (settingsData) => {
    try {
      set({ loading: true, error: null })
      const settings = await medicationService.saveReminderSettings(settingsData)
      set({ reminderSettings: settings, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '保存提醒设置失败', loading: false })
    }
  },

  // 加载监护人联系人
  loadGuardianContacts: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const contacts = await medicationService.getGuardianContacts(userId)
      set({ guardianContacts: contacts, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '加载监护人联系人失败', loading: false })
    }
  },

  // 添加监护人联系人
  addGuardianContact: async (contactData) => {
    try {
      set({ loading: true, error: null })
      const contact = await medicationService.addGuardianContact(contactData)
      const { guardianContacts } = get()
      set({ guardianContacts: [...guardianContacts, contact], loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '添加监护人联系人失败', loading: false })
    }
  },

  // 记录用药
  recordMedication: async (recordData) => {
    try {
      const record = await medicationService.recordMedication(recordData)
      const { medicationRecords } = get()
      set({ medicationRecords: [record, ...medicationRecords] })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '记录用药失败' })
    }
  },

  // 加载用药记录
  loadMedicationRecords: async (userId: string, startDate?: string, endDate?: string) => {
    try {
      set({ loading: true, error: null })
      const records = await medicationService.getMedicationRecords(userId, startDate, endDate)
      set({ medicationRecords: records, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '加载用药记录失败', loading: false })
    }
  },

  // 计算用药时间
  calculateMedicationTimes: (usage: string, frequency: string) => {
    const { dailySchedule } = get()
    if (!dailySchedule) {
      return ['08:00'] // 默认时间
    }
    
    const times = medicationScheduler.calculateMedicationTimes(usage, frequency, dailySchedule)
    return medicationScheduler.validateAndAdjustTimes(times)
  },

  // 启动提醒系统
  startReminderSystem: async (userId: string) => {
    try {
      const { reminders, reminderSettings } = get()
      
      if (!reminderSettings) {
        // 使用默认设置
        const defaultSettings: Omit<ReminderSettings, 'id' | 'createdAt' | 'updatedAt'> = {
          userId,
          firstReminderMinutes: 15,
          reminderInterval: 5,
          maxReminders: 6,
          soundEnabled: true,
          voiceEnabled: true,
          notificationEnabled: true,
          guardianNotificationDelay: 30
        }
        await get().saveReminderSettings(defaultSettings)
      }

      const settings = get().reminderSettings!
      
      // 为所有启用的提醒设置通知
      reminders.filter(r => r.isEnabled).forEach(reminder => {
        notificationService.scheduleReminder(reminder, settings, (reminderId, confirmed) => {
          get().recordMedication({
            reminderId,
            scheduledTime: new Date().toISOString(),
            status: confirmed ? 'taken' : 'missed',
            confirmationMethod: 'manual',
            userId
          })
        })
      })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '启动提醒系统失败' })
    }
  },

  // 停止提醒系统
  stopReminderSystem: () => {
    notificationService.cancelAllReminders()
  },

  // 清除错误
  clearError: () => {
    set({ error: null })
  }
}))

// 便捷的hooks
export const useMedication = () => {
  const store = useMedicationStore()
  return {
    // 数据
    reminders: store.reminders,
    dailySchedule: store.dailySchedule,
    reminderSettings: store.reminderSettings,
    guardianContacts: store.guardianContacts,
    medicationRecords: store.medicationRecords,
    
    // 状态
    loading: store.loading,
    error: store.error,
    
    // 方法
    loadReminders: store.loadReminders,
    addReminder: store.addReminder,
    updateReminder: store.updateReminder,
    deleteReminder: store.deleteReminder,
    
    loadDailySchedule: store.loadDailySchedule,
    saveDailySchedule: store.saveDailySchedule,
    
    loadReminderSettings: store.loadReminderSettings,
    saveReminderSettings: store.saveReminderSettings,
    
    loadGuardianContacts: store.loadGuardianContacts,
    addGuardianContact: store.addGuardianContact,
    
    recordMedication: store.recordMedication,
    loadMedicationRecords: store.loadMedicationRecords,
    
    calculateMedicationTimes: store.calculateMedicationTimes,
    startReminderSystem: store.startReminderSystem,
    stopReminderSystem: store.stopReminderSystem,
    
    clearError: store.clearError
  }
}
