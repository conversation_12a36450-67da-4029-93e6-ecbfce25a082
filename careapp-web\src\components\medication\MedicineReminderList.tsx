'use client'

import { useState, useEffect } from 'react'
import { Pill, Clock, Edit, Trash2, ToggleLeft, ToggleRight, Calendar } from 'lucide-react'
import { useMedication } from '@/store/medication'
import type { MedicineReminder } from '@/types'

interface MedicineReminderListProps {
  userId: string
  onEdit?: (reminder: MedicineReminder) => void
}

export function MedicineReminderList({ userId, onEdit }: MedicineReminderListProps) {
  const [selectedReminder, setSelectedReminder] = useState<string | null>(null)
  
  const { 
    reminders, 
    loadReminders, 
    updateReminder, 
    deleteReminder, 
    loading, 
    error 
  } = useMedication()

  useEffect(() => {
    loadReminders(userId)
  }, [userId, loadReminders])

  const handleToggleEnabled = async (reminder: MedicineReminder) => {
    try {
      await updateReminder(reminder.id, { isEnabled: !reminder.isEnabled })
    } catch (error) {
      console.error('更新提醒状态失败:', error)
    }
  }

  const handleDelete = async (reminderId: string) => {
    if (!confirm('确定要删除这个用药提醒吗？')) return
    
    try {
      await deleteReminder(reminderId)
    } catch (error) {
      console.error('删除提醒失败:', error)
    }
  }

  const formatScheduledTimes = (times: string[]): string => {
    if (times.length === 0) return '未设置'
    return times.join(', ')
  }

  const getNextReminderTime = (times: string[]): string => {
    if (times.length === 0) return ''
    
    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()
    
    for (const time of times) {
      const [hours, minutes] = time.split(':').map(Number)
      const timeInMinutes = hours * 60 + minutes
      
      if (timeInMinutes > currentTime) {
        return time
      }
    }
    
    // 如果今天没有更多提醒，返回明天第一个时间
    return `明天 ${times[0]}`
  }

  if (loading && reminders.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-center py-8">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => loadReminders(userId)}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  if (reminders.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <Pill className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">暂无用药提醒</h3>
          <p className="text-gray-600">点击"添加提醒"按钮创建您的第一个用药提醒</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-800">用药提醒列表</h2>
        <p className="text-sm text-gray-600 mt-1">共 {reminders.length} 个提醒</p>
      </div>

      <div className="divide-y divide-gray-200">
        {reminders.map(reminder => (
          <div
            key={reminder.id}
            className={`p-6 hover:bg-gray-50 transition-colors ${
              selectedReminder === reminder.id ? 'bg-blue-50' : ''
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <Pill className="w-5 h-5 text-blue-500 mr-2" />
                  <h3 className="text-lg font-medium text-gray-800">
                    {reminder.medicineName}
                  </h3>
                  <span className={`ml-3 px-2 py-1 text-xs rounded-full ${
                    reminder.isEnabled 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {reminder.isEnabled ? '已启用' : '已禁用'}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">
                      <strong>剂量：</strong>{reminder.dosage}
                    </p>
                    <p className="text-sm text-gray-600">
                      <strong>用法：</strong>{reminder.usage}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">
                      <strong>频次：</strong>{reminder.frequency}
                    </p>
                    {reminder.duration && (
                      <p className="text-sm text-gray-600">
                        <strong>疗程：</strong>{reminder.duration}天
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center mb-3">
                  <Clock className="w-4 h-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-600">
                    <strong>提醒时间：</strong>
                    {formatScheduledTimes(reminder.scheduledTimes)}
                  </span>
                </div>

                {reminder.isEnabled && reminder.scheduledTimes.length > 0 && (
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 text-green-500 mr-2" />
                    <span className="text-sm text-green-600">
                      <strong>下次提醒：</strong>
                      {getNextReminderTime(reminder.scheduledTimes)}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => handleToggleEnabled(reminder)}
                  className={`p-2 rounded-lg transition-colors ${
                    reminder.isEnabled
                      ? 'text-green-600 hover:bg-green-100'
                      : 'text-gray-400 hover:bg-gray-100'
                  }`}
                  title={reminder.isEnabled ? '禁用提醒' : '启用提醒'}
                >
                  {reminder.isEnabled ? (
                    <ToggleRight className="w-6 h-6" />
                  ) : (
                    <ToggleLeft className="w-6 h-6" />
                  )}
                </button>

                <button
                  onClick={() => onEdit?.(reminder)}
                  className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                  title="编辑提醒"
                >
                  <Edit className="w-5 h-5" />
                </button>

                <button
                  onClick={() => handleDelete(reminder.id)}
                  className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                  title="删除提醒"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* 展开详情 */}
            {selectedReminder === reminder.id && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                  <div>
                    <p><strong>创建时间：</strong>{new Date(reminder.createdAt).toLocaleString('zh-CN')}</p>
                    <p><strong>更新时间：</strong>{new Date(reminder.updatedAt).toLocaleString('zh-CN')}</p>
                  </div>
                  <div>
                    <p><strong>提醒ID：</strong>{reminder.id}</p>
                  </div>
                </div>
              </div>
            )}

            <button
              onClick={() => setSelectedReminder(
                selectedReminder === reminder.id ? null : reminder.id
              )}
              className="mt-3 text-sm text-blue-600 hover:text-blue-700"
            >
              {selectedReminder === reminder.id ? '收起详情' : '查看详情'}
            </button>
          </div>
        ))}
      </div>

      {loading && (
        <div className="p-4 text-center border-t border-gray-200">
          <div className="inline-flex items-center">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2" />
            <span className="text-sm text-gray-600">更新中...</span>
          </div>
        </div>
      )}
    </div>
  )
}
