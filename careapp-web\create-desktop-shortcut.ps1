# 照护宝桌面快捷方式创建脚本
# PowerShell脚本，用于创建桌面快捷方式

Write-Host "正在创建照护宝桌面快捷方式..." -ForegroundColor Green

# 获取桌面路径
$DesktopPath = [Environment]::GetFolderPath("Desktop")

# 创建快捷方式对象
$WshShell = New-Object -comObject WScript.Shell

# 照护宝主页快捷方式
$Shortcut1 = $WshShell.CreateShortcut("$DesktopPath\照护宝 - 主页.url")
$Shortcut1.TargetPath = "http://localhost:3000"
$Shortcut1.Save()

# 照护宝用药提醒快捷方式
$Shortcut2 = $WshShell.CreateShortcut("$DesktopPath\照护宝 - 用药提醒.url")
$Shortcut2.TargetPath = "http://localhost:3000/medication"
$Shortcut2.Save()

# 照护宝认证页面快捷方式
$Shortcut3 = $WshShell.CreateShortcut("$DesktopPath\照护宝 - 登录注册.url")
$Shortcut3.TargetPath = "http://localhost:3000/auth-demo"
$Shortcut3.Save()

Write-Host "✅ 桌面快捷方式创建成功！" -ForegroundColor Green
Write-Host ""
Write-Host "已创建以下快捷方式：" -ForegroundColor Yellow
Write-Host "📱 照护宝 - 主页.url" -ForegroundColor Cyan
Write-Host "💊 照护宝 - 用药提醒.url" -ForegroundColor Cyan
Write-Host "🔐 照护宝 - 登录注册.url" -ForegroundColor Cyan
Write-Host ""
Write-Host "注意：请确保开发服务器正在运行 (npm run dev)" -ForegroundColor Yellow
Write-Host "双击桌面快捷方式即可快速访问照护宝系统！" -ForegroundColor Green

# 询问是否启动开发服务器
Write-Host ""
$response = Read-Host "是否现在启动开发服务器？(y/n)"
if ($response -eq "y" -or $response -eq "Y") {
    Write-Host "正在启动开发服务器..." -ForegroundColor Green
    Set-Location $PSScriptRoot
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev"
    Write-Host "开发服务器已在新窗口中启动！" -ForegroundColor Green
}

Write-Host ""
Write-Host "脚本执行完成！按任意键退出..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
