"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 初始化音频系统\n   */ initializeAudio() {\n        if (false) {}\n        try {\n            // 预加载提醒音效\n            this.reminderAudio = new Audio();\n            this.reminderAudio.preload = 'auto';\n            this.reminderAudio.volume = 0.7;\n            // 尝试加载多种音效格式\n            const audioSources = [\n                '/sounds/reminder.mp3',\n                '/sounds/reminder.wav',\n                '/sounds/reminder.ogg'\n            ];\n            // 使用第一个可用的音频格式\n            for (const src of audioSources){\n                this.reminderAudio.src = src;\n                break;\n            }\n            // 如果没有音频文件，创建合成音效\n            if (!this.reminderAudio.src) {\n                this.createSyntheticReminderSound();\n            }\n            this.isAudioInitialized = true;\n        } catch (error) {\n            console.warn('音频初始化失败，将使用合成音效:', error);\n            this.createSyntheticReminderSound();\n        }\n    }\n    /**\n   * 创建合成提醒音效\n   */ createSyntheticReminderSound() {\n        if (false) {}\n        try {\n            // 使用Web Audio API创建合成音效\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const createBeep = function(frequency, duration) {\n                let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const oscillator = audioContext.createOscillator();\n                        const gainNode = audioContext.createGain();\n                        oscillator.connect(gainNode);\n                        gainNode.connect(audioContext.destination);\n                        oscillator.frequency.value = frequency;\n                        oscillator.type = 'sine';\n                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n                        oscillator.start(audioContext.currentTime);\n                        oscillator.stop(audioContext.currentTime + duration);\n                        setTimeout(resolve, duration * 1000);\n                    }, delay);\n                });\n            };\n            // 创建自定义提醒音效播放函数\n            this.playCustomReminderSound = async ()=>{\n                try {\n                    await createBeep(800, 0.2, 0) // 第一声\n                    ;\n                    await createBeep(1000, 0.2, 100) // 第二声\n                    ;\n                    await createBeep(800, 0.3, 200) // 第三声\n                    ;\n                } catch (error) {\n                    console.error('播放合成音效失败:', error);\n                }\n            };\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 重复第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n    }\n    /**\n   * 第三级提醒：确认是否已服药\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 询问是否已服药\n        const confirmationText = \"您是否已经服用了\".concat(notification.medicineName, \"？\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示确认对话框\n        this.notifyUI('confirmation-dialog', notification);\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ playReminderSound() {\n        try {\n            const audio = new Audio('/sounds/reminder.mp3');\n            audio.volume = 0.7;\n            audio.play().catch((error)=>{\n                console.error('播放提醒声音失败:', error);\n            });\n        } catch (error) {\n            console.error('创建音频对象失败:', error);\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        this.reminderAudio = null;\n        this.isAudioInitialized = false;\n        this.playCustomReminderSound = null;\n        if (true) {\n            this.initializeNotifications();\n            this.initializeAudio();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});