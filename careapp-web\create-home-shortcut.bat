@echo off
title CareApp Home Shortcut Creator

echo.
echo ========================================
echo     CareApp Home Shortcut Creator
echo ========================================
echo.

echo Creating CareApp home page desktop shortcut...
echo.

REM Get desktop path
for /f "tokens=3*" %%i in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP=%%i %%j

REM Create CareApp Home shortcut
echo [InternetShortcut] > "%DESKTOP%\CareApp.url"
echo URL=http://localhost:3000 >> "%DESKTOP%\CareApp.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\CareApp.url"
echo IconIndex=13 >> "%DESKTOP%\CareApp.url"

echo Desktop shortcut created successfully!
echo.
echo Created shortcut:
echo - CareApp.url (Home page)
echo.
echo Usage Instructions:
echo 1. Double-click 'start-server.bat' to start development server
echo 2. Double-click 'CareApp.url' on desktop to open system
echo 3. Click buttons on home page to access different modules
echo.
echo Available Features:
echo - Medication Reminder: Smart medication management
echo - Authentication: User registration and login
echo - Dashboard: System overview
echo - Test Auth: Simplified authentication test
echo.

REM Ask if user wants to start dev server
set /p choice="Start development server now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting development server...
    start "CareApp Dev Server" cmd /k "cd /d %~dp0 && npm run dev"
    echo Development server started in new window!
    echo Wait for server to start, then double-click desktop shortcut!
)

echo.
echo Script completed!
pause
