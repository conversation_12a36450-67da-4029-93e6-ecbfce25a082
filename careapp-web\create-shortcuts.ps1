# CareApp Desktop Shortcuts Creator
# PowerShell script to create desktop shortcuts

Write-Host "Creating CareApp desktop shortcuts..." -ForegroundColor Green

# Get desktop path
$DesktopPath = [Environment]::GetFolderPath("Desktop")

# Create shortcuts
$shortcuts = @(
    @{
        Name = "CareApp - Home"
        URL = "http://localhost:3000"
    },
    @{
        Name = "CareApp - Medication"
        URL = "http://localhost:3000/medication"
    },
    @{
        Name = "CareApp - Login"
        URL = "http://localhost:3000/auth-demo"
    }
)

foreach ($shortcut in $shortcuts) {
    $shortcutPath = Join-Path $DesktopPath "$($shortcut.Name).url"
    
    # Create URL shortcut content
    $content = @"
[InternetShortcut]
URL=$($shortcut.URL)
"@
    
    # Write to file
    $content | Out-File -FilePath $shortcutPath -Encoding ASCII
    Write-Host "Created: $($shortcut.Name).url" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Desktop shortcuts created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Created shortcuts:" -ForegroundColor Yellow
Write-Host "- CareApp - Home.url (Main page)" -ForegroundColor Cyan
Write-Host "- CareApp - Medication.url (Medication reminders)" -ForegroundColor Cyan
Write-Host "- CareApp - Login.url (Authentication)" -ForegroundColor Cyan
Write-Host ""
Write-Host "Note: Make sure the development server is running (npm run dev)" -ForegroundColor Yellow
Write-Host "Double-click desktop shortcuts to access CareApp system!" -ForegroundColor Green

# Ask if user wants to start dev server
Write-Host ""
$response = Read-Host "Start development server now? (y/n)"
if ($response -eq "y" -or $response -eq "Y") {
    Write-Host "Starting development server..." -ForegroundColor Green
    Set-Location $PSScriptRoot
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev"
    Write-Host "Development server started in new window!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Script completed! Press any key to exit..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
