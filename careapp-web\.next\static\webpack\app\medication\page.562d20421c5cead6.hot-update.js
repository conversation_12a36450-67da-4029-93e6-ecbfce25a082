"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 重复第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n    }\n    /**\n   * 第三级提醒：确认是否已服药\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 询问是否已服药\n        const confirmationText = \"您是否已经服用了\".concat(notification.medicineName, \"？\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示确认对话框\n        this.notifyUI('confirmation-dialog', notification);\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ playReminderSound() {\n        try {\n            const audio = new Audio('/sounds/reminder.mp3');\n            audio.volume = 0.7;\n            audio.play().catch((error)=>{\n                console.error('播放提醒声音失败:', error);\n            });\n        } catch (error) {\n            console.error('创建音频对象失败:', error);\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n            detail: data\n        }));\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        if (true) {\n            this.initializeNotifications();\n        }\n    }\n}\nconst notificationService = new NotificationService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});