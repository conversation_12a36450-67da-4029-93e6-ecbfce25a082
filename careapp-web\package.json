{"name": "careapp-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "lucide-react": "^0.263.1", "next": "15.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "15.3.3", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}