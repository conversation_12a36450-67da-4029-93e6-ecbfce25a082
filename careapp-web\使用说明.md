# 照护宝系统使用说明

## 🎯 桌面快捷方式已创建成功！

您的桌面现在有一个 **"照护宝.url"** 快捷方式，可以直接访问系统主页。

## 🚀 使用步骤

### 第一步：启动开发服务器

**方法一：使用启动脚本（推荐）**
- 双击项目文件夹中的 `start-server.bat`

**方法二：手动启动**
- 打开命令行，进入项目目录
- 运行 `npm run dev`

### 第二步：访问系统

- 双击桌面上的 **"照护宝.url"** 快捷方式
- 系统将在浏览器中打开主页

### 第三步：功能导航

在主页上，您可以点击以下按钮进入各个功能模块：

| 按钮 | 功能 | 说明 |
|------|------|------|
| 💊 **用药提醒** | 智能用药管理系统 | 设置用药时间、语音输入、智能提醒 |
| 🔐 **认证演示** | 用户注册登录 | 完整的用户认证功能演示 |
| 📊 **仪表板** | 系统概览 | 用户个人中心和系统状态 |
| 🧪 **测试认证** | 简化版认证测试 | 快速测试认证功能 |
| 📝 **测试页面** | 系统测试 | 开发测试功能 |

## 💊 用药提醒系统使用指南

### 功能特色
- ✅ **语音输入**：支持语音识别药品信息
- ✅ **智能计算**：根据作息时间自动计算用药时间
- ✅ **多级提醒**：15分钟前提醒、5分钟间隔重复、用药后确认
- ✅ **个性化设置**：自定义作息时间和提醒参数

### 使用流程
1. **设置作息时间**：配置起床、三餐、就寝时间
2. **添加用药提醒**：
   - 点击"语音输入"按钮
   - 说出药品信息，如："阿司匹林，每次1片，餐后1小时，每日3次"
   - 或手动填写表单
3. **查看计算结果**：系统自动计算最佳用药时间
4. **启用提醒系统**：点击"启用提醒系统"开始智能提醒

## 🔐 认证系统使用指南

### 注册新用户
1. 点击"认证演示"按钮
2. 选择"注册"标签
3. 填写用户信息：
   - 邮箱地址
   - 密码（至少8位）
   - 姓名
   - 手机号（可选）
   - 用户角色（患者/家属/护理员）
4. 点击"注册"按钮

### 用户登录
1. 选择"登录"标签
2. 输入邮箱和密码
3. 点击"登录"按钮

## ⚠️ 注意事项

### 系统要求
- ✅ Node.js 18+ 已安装
- ✅ 现代浏览器（Chrome、Edge、Firefox）
- ✅ 3000端口未被占用

### 演示模式说明
- 📱 **数据存储**：当前为演示模式，数据保存在浏览器本地存储
- 🔄 **同步功能**：演示模式下同步功能已禁用
- 💾 **数据持久化**：关闭浏览器后数据仍会保留

### 常见问题解决

**Q: 双击快捷方式无法打开？**
A: 请确保开发服务器正在运行，双击 `start-server.bat` 启动

**Q: 页面显示404错误？**
A: 刷新浏览器页面，或重启开发服务器

**Q: 语音输入不工作？**
A: 确保浏览器已允许麦克风权限，推荐使用Chrome浏览器

**Q: 提醒通知不显示？**
A: 确保浏览器已允许通知权限

## 🛠️ 开发者信息

### 项目结构
```
careapp-web/
├── src/app/                 # 页面组件
├── src/components/          # 可复用组件
├── src/lib/                # 工具库和服务
├── src/store/              # 状态管理
├── public/                 # 静态资源
└── supabase/              # 数据库配置
```

### 启动脚本
- `start-server.bat` - 启动开发服务器
- `create-home-shortcut.ps1` - 创建桌面快捷方式
- `create-home-shortcut.bat` - 批处理版本

### 技术栈
- **前端框架**：Next.js 15 + React 18
- **样式**：Tailwind CSS
- **状态管理**：Zustand
- **数据库**：Supabase（演示模式使用本地存储）
- **语音功能**：Web Speech API

## 🎉 开始体验

现在您可以：

1. **体验用药提醒**：设置个人用药计划，体验智能提醒功能
2. **测试用户认证**：注册账户，体验登录功能
3. **探索系统功能**：通过主页导航访问各个模块

---

**享受便捷的照护宝体验！** 🎉

如有问题，请查看项目文档或重新运行快捷方式创建脚本。
