"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/speech-service.ts":
/*!***********************************!*\
  !*** ./src/lib/speech-service.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeechService: () => (/* binding */ SpeechService),\n/* harmony export */   speechService: () => (/* binding */ speechService)\n/* harmony export */ });\nclass SpeechService {\n    /**\n   * 初始化语音识别\n   */ initializeSpeechRecognition() {\n        if (true) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (SpeechRecognition) {\n                this.recognition = new SpeechRecognition();\n                this.recognition.continuous = false;\n                this.recognition.interimResults = true;\n                this.recognition.lang = 'zh-CN';\n                this.recognition.maxAlternatives = 1;\n            }\n        }\n    }\n    /**\n   * 初始化语音合成\n   */ initializeSpeechSynthesis() {\n        if ( true && 'speechSynthesis' in window) {\n            this.synthesis = window.speechSynthesis;\n        }\n    }\n    /**\n   * 初始化药品数据库\n   */ initializeMedicineDatabase() {\n        // 常用药品及其别名\n        const medicines = [\n            // 心血管药物\n            {\n                name: '阿司匹林',\n                aliases: [\n                    '阿斯匹林',\n                    '阿司匹林肠溶片',\n                    '拜阿司匹灵'\n                ]\n            },\n            {\n                name: '硝苯地平',\n                aliases: [\n                    '硝苯地平片',\n                    '心痛定',\n                    '拜新同'\n                ]\n            },\n            {\n                name: '卡托普利',\n                aliases: [\n                    '卡托普利片',\n                    '开博通'\n                ]\n            },\n            {\n                name: '美托洛尔',\n                aliases: [\n                    '美托洛尔片',\n                    '倍他乐克',\n                    '酒石酸美托洛尔'\n                ]\n            },\n            // 降糖药物\n            {\n                name: '二甲双胍',\n                aliases: [\n                    '二甲双胍片',\n                    '格华止',\n                    '美迪康'\n                ]\n            },\n            {\n                name: '格列齐特',\n                aliases: [\n                    '格列齐特片',\n                    '达美康',\n                    '迪沙片'\n                ]\n            },\n            {\n                name: '胰岛素',\n                aliases: [\n                    '胰岛素注射液',\n                    '诺和灵',\n                    '优泌林'\n                ]\n            },\n            // 抗生素\n            {\n                name: '阿莫西林',\n                aliases: [\n                    '阿莫西林胶囊',\n                    '阿莫西林片',\n                    '再林'\n                ]\n            },\n            {\n                name: '头孢拉定',\n                aliases: [\n                    '头孢拉定胶囊',\n                    '头孢拉定片'\n                ]\n            },\n            {\n                name: '左氧氟沙星',\n                aliases: [\n                    '左氧氟沙星片',\n                    '可乐必妥',\n                    '来立信'\n                ]\n            },\n            // 消化系统药物\n            {\n                name: '奥美拉唑',\n                aliases: [\n                    '奥美拉唑肠溶胶囊',\n                    '洛赛克',\n                    '奥克'\n                ]\n            },\n            {\n                name: '多潘立酮',\n                aliases: [\n                    '多潘立酮片',\n                    '吗丁啉',\n                    '胃复安'\n                ]\n            },\n            {\n                name: '蒙脱石散',\n                aliases: [\n                    '思密达',\n                    '必奇'\n                ]\n            },\n            // 感冒药物\n            {\n                name: '对乙酰氨基酚',\n                aliases: [\n                    '对乙酰氨基酚片',\n                    '泰诺林',\n                    '百服宁',\n                    '扑热息痛'\n                ]\n            },\n            {\n                name: '布洛芬',\n                aliases: [\n                    '布洛芬片',\n                    '芬必得',\n                    '美林'\n                ]\n            },\n            {\n                name: '复方氨酚烷胺',\n                aliases: [\n                    '快克',\n                    '感康'\n                ]\n            },\n            // 维生素类\n            {\n                name: '维生素C',\n                aliases: [\n                    '维生素C片',\n                    'VC片',\n                    '维C'\n                ]\n            },\n            {\n                name: '维生素D',\n                aliases: [\n                    '维生素D3',\n                    'VD3',\n                    '钙尔奇D'\n                ]\n            },\n            {\n                name: '复合维生素B',\n                aliases: [\n                    '维生素B族',\n                    'VB片',\n                    '复合VB'\n                ]\n            }\n        ];\n        // 构建药品数据库\n        medicines.forEach((medicine)=>{\n            const allNames = [\n                medicine.name,\n                ...medicine.aliases\n            ];\n            this.medicineDatabase.set(medicine.name, allNames);\n            this.commonMedicines.push(medicine.name);\n            // 为每个别名也建立映射\n            medicine.aliases.forEach((alias)=>{\n                this.medicineDatabase.set(alias, allNames);\n            });\n        });\n    }\n    /**\n   * 检查浏览器是否支持语音识别\n   */ isSpeechRecognitionSupported() {\n        return this.recognition !== null;\n    }\n    /**\n   * 检查浏览器是否支持语音合成\n   */ isSpeechSynthesisSupported() {\n        return this.synthesis !== null;\n    }\n    /**\n   * 开始语音识别\n   */ async startListening(onResult, onError) {\n        if (!this.recognition) {\n            throw new Error('语音识别不支持');\n        }\n        if (this.isListening) {\n            this.stopListening();\n        }\n        return new Promise((resolve, reject)=>{\n            this.recognition.onstart = ()=>{\n                this.isListening = true;\n                resolve();\n            };\n            this.recognition.onresult = (event)=>{\n                const result = event.results[event.results.length - 1];\n                const transcript = result[0].transcript;\n                const confidence = result[0].confidence;\n                const isFinal = result.isFinal;\n                onResult({\n                    transcript: transcript.trim(),\n                    confidence,\n                    isFinal\n                });\n            };\n            this.recognition.onerror = (event)=>{\n                this.isListening = false;\n                const errorMessage = this.getErrorMessage(event.error);\n                onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n                reject(new Error(errorMessage));\n            };\n            this.recognition.onend = ()=>{\n                this.isListening = false;\n            };\n            try {\n                this.recognition.start();\n            } catch (error) {\n                this.isListening = false;\n                reject(error);\n            }\n        });\n    }\n    /**\n   * 停止语音识别\n   */ stopListening() {\n        if (this.recognition && this.isListening) {\n            this.recognition.stop();\n            this.isListening = false;\n        }\n    }\n    /**\n   * 语音播报\n   */ async speak(options) {\n        if (!this.synthesis) {\n            throw new Error('语音合成不支持');\n        }\n        // 停止当前播报\n        this.synthesis.cancel();\n        return new Promise((resolve, reject)=>{\n            const utterance = new SpeechSynthesisUtterance(options.text);\n            utterance.lang = options.lang || 'zh-CN';\n            utterance.rate = options.rate || 1;\n            utterance.pitch = options.pitch || 1;\n            utterance.volume = options.volume || 1;\n            utterance.onend = ()=>resolve();\n            utterance.onerror = (event)=>reject(new Error(\"语音播报失败: \".concat(event.error)));\n            // 获取中文语音\n            const voices = this.synthesis.getVoices();\n            const chineseVoice = voices.find((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n            if (chineseVoice) {\n                utterance.voice = chineseVoice;\n            }\n            this.synthesis.speak(utterance);\n        });\n    }\n    /**\n   * 停止语音播报\n   */ stopSpeaking() {\n        if (this.synthesis) {\n            this.synthesis.cancel();\n        }\n    }\n    /**\n   * 解析药品信息的语音输入\n   */ parseMedicineInput(transcript) {\n        const result = {};\n        // 提取药品名称（通常在开头）\n        const medicineNameMatch = transcript.match(/^(.+?)(?:\\s|，|,)/);\n        if (medicineNameMatch) {\n            result.medicineName = medicineNameMatch[1].trim();\n        }\n        // 提取剂量信息\n        const dosagePatterns = [\n            /(\\d+)\\s*片/,\n            /(\\d+)\\s*粒/,\n            /(\\d+)\\s*毫升/,\n            /(\\d+)\\s*ml/,\n            /(\\d+)\\s*毫克/,\n            /(\\d+)\\s*mg/,\n            /(\\d+)\\s*滴/\n        ];\n        for (const pattern of dosagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                const unit = match[0].replace(match[1], '').trim();\n                result.dosage = \"\".concat(match[1]).concat(unit);\n                break;\n            }\n        }\n        // 提取用法信息\n        const usagePatterns = [\n            /餐前\\s*(\\d+)?\\s*分钟?/,\n            /饭前\\s*(\\d+)?\\s*分钟?/,\n            /餐后\\s*(\\d+)?\\s*分钟?/,\n            /饭后\\s*(\\d+)?\\s*分钟?/,\n            /睡前\\s*(\\d+)?\\s*分钟?/,\n            /晨起/,\n            /起床后/\n        ];\n        for (const pattern of usagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.usage = match[0];\n                break;\n            }\n        }\n        // 提取频次信息\n        const frequencyPatterns = [\n            /每日\\s*(\\d+)\\s*次/,\n            /一日\\s*(\\d+)\\s*次/,\n            /每天\\s*(\\d+)\\s*次/,\n            /(\\d+)\\s*次\\s*每日/,\n            /(\\d+)\\s*次\\s*一日/\n        ];\n        for (const pattern of frequencyPatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.frequency = match[0];\n                break;\n            }\n        }\n        return result;\n    }\n    /**\n   * 生成用药提醒的语音内容\n   */ generateReminderSpeech(medicineName, dosage, usage) {\n        let speech = \"请注意，现在是服用\".concat(medicineName, \"的时间。\");\n        if (dosage) {\n            speech += \"请服用\".concat(dosage, \"。\");\n        }\n        if (usage) {\n            speech += \"用法：\".concat(usage, \"。\");\n        }\n        speech += '请确认是否已经服药。';\n        return speech;\n    }\n    /**\n   * 获取错误信息\n   */ getErrorMessage(error) {\n        switch(error){\n            case 'no-speech':\n                return '没有检测到语音输入';\n            case 'audio-capture':\n                return '无法访问麦克风';\n            case 'not-allowed':\n                return '麦克风权限被拒绝';\n            case 'network':\n                return '网络错误';\n            case 'service-not-allowed':\n                return '语音识别服务不可用';\n            default:\n                return \"语音识别错误: \".concat(error);\n        }\n    }\n    /**\n   * 请求麦克风权限\n   */ async requestMicrophonePermission() {\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            stream.getTracks().forEach((track)=>track.stop());\n            return true;\n        } catch (error) {\n            console.error('麦克风权限请求失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 获取可用的语音列表\n   */ getAvailableVoices() {\n        if (!this.synthesis) return [];\n        return this.synthesis.getVoices().filter((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n    }\n    /**\n   * 测试语音功能\n   */ async testSpeech() {\n        const result = {\n            recognition: this.isSpeechRecognitionSupported(),\n            synthesis: this.isSpeechSynthesisSupported()\n        };\n        if (result.synthesis) {\n            try {\n                await this.speak({\n                    text: '语音功能测试正常'\n                });\n            } catch (error) {\n                result.synthesis = false;\n            }\n        }\n        return result;\n    }\n    constructor(){\n        this.recognition = null;\n        this.synthesis = null;\n        this.isListening = false;\n        this.medicineDatabase = new Map();\n        this.commonMedicines = [];\n        this.initializeSpeechRecognition();\n        this.initializeSpeechSynthesis();\n        this.initializeMedicineDatabase();\n    }\n}\nconst speechService = new SpeechService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/speech-service.ts\n"));

/***/ })

});