"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 初始化音频系统\n   */ initializeAudio() {\n        if (false) {}\n        try {\n            // 预加载提醒音效\n            this.reminderAudio = new Audio();\n            this.reminderAudio.preload = 'auto';\n            this.reminderAudio.volume = 0.7;\n            // 尝试加载多种音效格式\n            const audioSources = [\n                '/sounds/reminder.mp3',\n                '/sounds/reminder.wav',\n                '/sounds/reminder.ogg'\n            ];\n            // 使用第一个可用的音频格式\n            for (const src of audioSources){\n                this.reminderAudio.src = src;\n                break;\n            }\n            // 如果没有音频文件，创建合成音效\n            if (!this.reminderAudio.src) {\n                this.createSyntheticReminderSound();\n            }\n            this.isAudioInitialized = true;\n        } catch (error) {\n            console.warn('音频初始化失败，将使用合成音效:', error);\n            this.createSyntheticReminderSound();\n        }\n    }\n    /**\n   * 创建合成提醒音效\n   */ createSyntheticReminderSound() {\n        if (false) {}\n        try {\n            // 使用Web Audio API创建合成音效\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const createBeep = function(frequency, duration) {\n                let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const oscillator = audioContext.createOscillator();\n                        const gainNode = audioContext.createGain();\n                        oscillator.connect(gainNode);\n                        gainNode.connect(audioContext.destination);\n                        oscillator.frequency.value = frequency;\n                        oscillator.type = 'sine';\n                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n                        oscillator.start(audioContext.currentTime);\n                        oscillator.stop(audioContext.currentTime + duration);\n                        setTimeout(resolve, duration * 1000);\n                    }, delay);\n                });\n            };\n            // 创建自定义提醒音效播放函数\n            this.playCustomReminderSound = async ()=>{\n                try {\n                    await createBeep(800, 0.2, 0) // 第一声\n                    ;\n                    await createBeep(1000, 0.2, 100) // 第二声\n                    ;\n                    await createBeep(800, 0.3, 200) // 第三声\n                    ;\n                } catch (error) {\n                    console.error('播放合成音效失败:', error);\n                }\n            };\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒\n        if (settings.soundEnabled) {\n            this.playReminderSound();\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 重复第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n    }\n    /**\n   * 第三级提醒：确认是否已服药\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 询问是否已服药\n        const confirmationText = \"您是否已经服用了\".concat(notification.medicineName, \"？\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示确认对话框\n        this.notifyUI('confirmation-dialog', notification);\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ async playReminderSound() {\n        try {\n            // 优先使用预加载的音频\n            if (this.reminderAudio && this.isAudioInitialized) {\n                this.reminderAudio.currentTime = 0 // 重置播放位置\n                ;\n                await this.reminderAudio.play();\n                return;\n            }\n            // 如果预加载音频不可用，使用合成音效\n            if (this.playCustomReminderSound) {\n                await this.playCustomReminderSound();\n                return;\n            }\n            // 最后的备选方案：简单的beep音效\n            this.playFallbackSound();\n        } catch (error) {\n            console.error('播放提醒声音失败:', error);\n            // 如果所有音效都失败，尝试备选方案\n            this.playFallbackSound();\n        }\n    }\n    /**\n   * 备选音效（系统beep）\n   */ playFallbackSound() {\n        try {\n            // 使用系统默认音效\n            if ('speechSynthesis' in window) {\n                const utterance = new SpeechSynthesisUtterance('');\n                utterance.volume = 0.1;\n                speechSynthesis.speak(utterance);\n            }\n        } catch (error) {\n            console.warn('备选音效也无法播放:', error);\n        }\n    }\n    /**\n   * 播放连续提醒音效（用于重要提醒）\n   */ async playIntensiveReminderSound() {\n        for(let i = 0; i < 3; i++){\n            await this.playReminderSound();\n            await new Promise((resolve)=>setTimeout(resolve, 500)) // 间隔500ms\n            ;\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        this.reminderAudio = null;\n        this.isAudioInitialized = false;\n        this.playCustomReminderSound = null;\n        if (true) {\n            this.initializeNotifications();\n            this.initializeAudio();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvbm90aWZpY2F0aW9uLXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnRDtBQWV6QyxNQUFNQztJQWNYOztHQUVDLEdBQ0QsTUFBY0MsMEJBQTBCO1FBQ3RDLElBQUksS0FBNkIsSUFBSSxrQkFBa0JDLFFBQVE7WUFDN0QsSUFBSSxDQUFDQyxzQkFBc0IsR0FBR0MsYUFBYUMsVUFBVTtZQUVyRCxJQUFJLElBQUksQ0FBQ0Ysc0JBQXNCLEtBQUssV0FBVztnQkFDN0MsSUFBSSxDQUFDQSxzQkFBc0IsR0FBRyxNQUFNQyxhQUFhRSxpQkFBaUI7WUFDcEU7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEI7UUFDeEIsSUFBSSxLQUE2QixFQUFFO1FBRW5DLElBQUk7WUFDRixVQUFVO1lBQ1YsSUFBSSxDQUFDRSxhQUFhLEdBQUcsSUFBSUM7WUFDekIsSUFBSSxDQUFDRCxhQUFhLENBQUNFLE9BQU8sR0FBRztZQUM3QixJQUFJLENBQUNGLGFBQWEsQ0FBQ0csTUFBTSxHQUFHO1lBRTVCLGFBQWE7WUFDYixNQUFNQyxlQUFlO2dCQUNuQjtnQkFDQTtnQkFDQTthQUNEO1lBRUQsZUFBZTtZQUNmLEtBQUssTUFBTUMsT0FBT0QsYUFBYztnQkFDOUIsSUFBSSxDQUFDSixhQUFhLENBQUNLLEdBQUcsR0FBR0E7Z0JBQ3pCO1lBQ0Y7WUFFQSxrQkFBa0I7WUFDbEIsSUFBSSxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDSyxHQUFHLEVBQUU7Z0JBQzNCLElBQUksQ0FBQ0MsNEJBQTRCO1lBQ25DO1lBRUEsSUFBSSxDQUFDQyxrQkFBa0IsR0FBRztRQUM1QixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLG9CQUFvQkY7WUFDakMsSUFBSSxDQUFDRiw0QkFBNEI7UUFDbkM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsK0JBQXVDO1FBQ3JDLElBQUksS0FBNkIsRUFBRTtRQUVuQyxJQUFJO1lBQ0Ysd0JBQXdCO1lBQ3hCLE1BQU1LLGVBQWUsSUFBS2pCLENBQUFBLE9BQU9rQixZQUFZLElBQUksT0FBZ0JDLGtCQUFrQjtZQUVuRixNQUFNQyxhQUFhLFNBQUNDLFdBQW1CQztvQkFBa0JDLHlFQUFnQjtnQkFDdkUsT0FBTyxJQUFJQyxRQUFjLENBQUNDO29CQUN4QkMsV0FBVzt3QkFDVCxNQUFNQyxhQUFhVixhQUFhVyxnQkFBZ0I7d0JBQ2hELE1BQU1DLFdBQVdaLGFBQWFhLFVBQVU7d0JBRXhDSCxXQUFXSSxPQUFPLENBQUNGO3dCQUNuQkEsU0FBU0UsT0FBTyxDQUFDZCxhQUFhZSxXQUFXO3dCQUV6Q0wsV0FBV04sU0FBUyxDQUFDWSxLQUFLLEdBQUdaO3dCQUM3Qk0sV0FBV08sSUFBSSxHQUFHO3dCQUVsQkwsU0FBU00sSUFBSSxDQUFDQyxjQUFjLENBQUMsS0FBS25CLGFBQWFvQixXQUFXO3dCQUMxRFIsU0FBU00sSUFBSSxDQUFDRyw0QkFBNEIsQ0FBQyxNQUFNckIsYUFBYW9CLFdBQVcsR0FBR2Y7d0JBRTVFSyxXQUFXWSxLQUFLLENBQUN0QixhQUFhb0IsV0FBVzt3QkFDekNWLFdBQVdhLElBQUksQ0FBQ3ZCLGFBQWFvQixXQUFXLEdBQUdmO3dCQUUzQ0ksV0FBV0QsU0FBU0gsV0FBVztvQkFDakMsR0FBR0M7Z0JBQ0w7WUFDRjtZQUVBLGdCQUFnQjtZQUNoQixJQUFJLENBQUNrQix1QkFBdUIsR0FBRztnQkFDN0IsSUFBSTtvQkFDRixNQUFNckIsV0FBVyxLQUFLLEtBQUssR0FBTSxNQUFNOztvQkFDdkMsTUFBTUEsV0FBVyxNQUFNLEtBQUssS0FBSyxNQUFNOztvQkFDdkMsTUFBTUEsV0FBVyxLQUFLLEtBQUssS0FBTSxNQUFNOztnQkFDekMsRUFBRSxPQUFPTixPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsYUFBYUE7Z0JBQzdCO1lBQ0Y7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLHFCQUFxQkY7UUFDcEM7SUFDRjtJQUlBOztHQUVDLEdBQ0QsTUFBTTRCLGdDQUFrRDtRQUN0RCxJQUFJLENBQUUsbUJBQWtCMUMsTUFBSyxHQUFJO1lBQy9CLE9BQU87UUFDVDtRQUVBLElBQUlFLGFBQWFDLFVBQVUsS0FBSyxXQUFXO1lBQ3pDLE9BQU87UUFDVDtRQUVBLE1BQU1BLGFBQWEsTUFBTUQsYUFBYUUsaUJBQWlCO1FBQ3ZELElBQUksQ0FBQ0gsc0JBQXNCLEdBQUdFO1FBQzlCLE9BQU9BLGVBQWU7SUFDeEI7SUFFQTs7R0FFQyxHQUNEd0MsaUJBQ0VDLFFBQTBCLEVBQzFCQyxRQUEwQixFQUMxQkMsU0FBMkQsRUFDckQ7UUFDTkYsU0FBU0csY0FBYyxDQUFDQyxPQUFPLENBQUNDLENBQUFBO1lBQzlCLE1BQU1DLG9CQUFvQixJQUFJLENBQUNDLHdCQUF3QixDQUFDRjtZQUN4RCxNQUFNRyxhQUFhLEdBQWtCSCxPQUFmTCxTQUFTUyxFQUFFLEVBQUMsS0FBUSxPQUFMSjtZQUVyQyxZQUFZO1lBQ1osTUFBTUssb0JBQW9CLElBQUlDLEtBQUtMLGtCQUFrQk0sT0FBTyxLQUFLWCxTQUFTWSxvQkFBb0IsR0FBRztZQUVqRyxNQUFNQyxVQUFVaEMsV0FBVztnQkFDekIsSUFBSSxDQUFDaUMsZUFBZSxDQUFDZixVQUFVSyxNQUFNSixVQUFVQztZQUNqRCxHQUFHUSxrQkFBa0JFLE9BQU8sS0FBS0QsS0FBS0ssR0FBRztZQUV6QyxJQUFJLENBQUNDLGdCQUFnQixDQUFDQyxHQUFHLENBQUNWLFlBQVlNO1FBQ3hDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNDLGdCQUNaZixRQUEwQixFQUMxQm1CLGFBQXFCLEVBQ3JCbEIsUUFBMEIsRUFDMUJDLFNBQTJELEVBQzVDO1FBQ2YsTUFBTWtCLGlCQUFpQixHQUFrQkQsT0FBZm5CLFNBQVNTLEVBQUUsRUFBQyxLQUFvQkUsT0FBakJRLGVBQWMsS0FBYyxPQUFYUixLQUFLSyxHQUFHO1FBRWxFLE1BQU1LLGVBQXFDO1lBQ3pDWixJQUFJVztZQUNKWixZQUFZUixTQUFTUyxFQUFFO1lBQ3ZCYSxjQUFjdEIsU0FBU3NCLFlBQVk7WUFDbkNDLFFBQVF2QixTQUFTdUIsTUFBTTtZQUN2QkMsT0FBT3hCLFNBQVN3QixLQUFLO1lBQ3JCTDtZQUNBTSxPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsV0FBVyxJQUFJaEI7UUFDakI7UUFFQSxJQUFJLENBQUNpQixlQUFlLENBQUNWLEdBQUcsQ0FBQ0UsZ0JBQWdCQztRQUV6QyxRQUFRO1FBQ1IsTUFBTSxJQUFJLENBQUNRLHNCQUFzQixDQUFDUixjQUFjcEI7UUFFaEQsU0FBUztRQUNULElBQUksQ0FBQzZCLHlCQUF5QixDQUFDVCxjQUFjcEIsVUFBVUM7SUFDekQ7SUFFQTs7R0FFQyxHQUNELE1BQWMyQix1QkFDWlIsWUFBa0MsRUFDbENwQixRQUEwQixFQUNYO1FBQ2YsUUFBUTtRQUNSLElBQUlBLFNBQVM4QixtQkFBbUIsSUFBSSxJQUFJLENBQUMxRSxzQkFBc0IsS0FBSyxXQUFXO1lBQzdFLE1BQU0yRSxzQkFBc0IsSUFBSTFFLGFBQWEsUUFBa0MsT0FBMUIrRCxhQUFhQyxZQUFZLEdBQUk7Z0JBQ2hGVyxNQUFNLE9BQTJCLE9BQXBCWixhQUFhRSxNQUFNO2dCQUNoQ1csTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsS0FBS2YsYUFBYVosRUFBRTtnQkFDcEI0QixvQkFBb0I7Z0JBQ3BCQyxTQUFTO29CQUNQO3dCQUFFQyxRQUFRO3dCQUFXQyxPQUFPO29CQUFNO29CQUNsQzt3QkFBRUQsUUFBUTt3QkFBVUMsT0FBTztvQkFBTztpQkFDbkM7WUFDSDtZQUVBUixvQkFBb0JTLE9BQU8sR0FBRztnQkFDNUIsSUFBSSxDQUFDQywwQkFBMEIsQ0FBQ3JCLGFBQWFaLEVBQUUsRUFBRTtnQkFDakR1QixvQkFBb0JXLEtBQUs7WUFDM0I7UUFDRjtRQUVBLE9BQU87UUFDUCxJQUFJMUMsU0FBUzJDLFlBQVksRUFBRTtZQUN6QixJQUFJLENBQUNDLGlCQUFpQjtRQUN4QjtRQUVBLG1CQUFtQjtRQUNuQixJQUFJLENBQUNDLFFBQVEsQ0FBQyxrQkFBa0J6QjtJQUNsQztJQUVBOztHQUVDLEdBQ0QsTUFBYzBCLHdCQUNaMUIsWUFBa0MsRUFDbENwQixRQUEwQixFQUNYO1FBQ2ZvQixhQUFhSSxLQUFLLEdBQUc7UUFFckIsVUFBVTtRQUNWLE1BQU0sSUFBSSxDQUFDSSxzQkFBc0IsQ0FBQ1IsY0FBY3BCO1FBRWhELE9BQU87UUFDUCxJQUFJQSxTQUFTK0MsWUFBWSxJQUFJL0YsMERBQWFBLENBQUNnRywwQkFBMEIsSUFBSTtZQUN2RSxNQUFNQyxhQUFhakcsMERBQWFBLENBQUNrRyxzQkFBc0IsQ0FDckQ5QixhQUFhQyxZQUFZLEVBQ3pCRCxhQUFhRSxNQUFNLEVBQ25CRixhQUFhRyxLQUFLO1lBR3BCLElBQUk7Z0JBQ0YsTUFBTXZFLDBEQUFhQSxDQUFDbUcsS0FBSyxDQUFDO29CQUFFQyxNQUFNSDtnQkFBVztZQUMvQyxFQUFFLE9BQU9oRixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsV0FBV0E7WUFDM0I7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjb0YsdUJBQ1pqQyxZQUFrQyxFQUNsQ3BCLFFBQTBCLEVBQ1g7UUFDZm9CLGFBQWFJLEtBQUssR0FBRztRQUVyQixVQUFVO1FBQ1YsTUFBTThCLG1CQUFtQixXQUFxQyxPQUExQmxDLGFBQWFDLFlBQVksRUFBQztRQUU5RCxJQUFJckIsU0FBUytDLFlBQVksSUFBSS9GLDBEQUFhQSxDQUFDZ0csMEJBQTBCLElBQUk7WUFDdkUsSUFBSTtnQkFDRixNQUFNaEcsMERBQWFBLENBQUNtRyxLQUFLLENBQUM7b0JBQUVDLE1BQU1FO2dCQUFpQjtZQUNyRCxFQUFFLE9BQU9yRixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsV0FBV0E7WUFDM0I7UUFDRjtRQUVBLFVBQVU7UUFDVixJQUFJLENBQUM0RSxRQUFRLENBQUMsdUJBQXVCekI7SUFDdkM7SUFFQTs7R0FFQyxHQUNELDBCQUNFQSxZQUFrQyxFQUNsQ3BCLFFBQTBCLEVBQzFCQyxTQUEyRCxFQUNyRDtRQUNOLElBQUlzRCxnQkFBZ0I7UUFDcEIsTUFBTUMsZUFBZXhELFNBQVN3RCxZQUFZO1FBRTFDLE1BQU1DLGVBQWU7WUFDbkIsSUFBSSxDQUFDLElBQUksQ0FBQzlCLGVBQWUsQ0FBQytCLEdBQUcsQ0FBQ3RDLGFBQWFaLEVBQUUsS0FBSytDLGlCQUFpQkMsY0FBYztnQkFDL0UsaUJBQWlCO2dCQUNqQixJQUFJRCxpQkFBaUJDLGNBQWM7b0JBQ2pDLElBQUksQ0FBQ0csZUFBZSxDQUFDdkMsY0FBY3BCO2dCQUNyQztnQkFDQTtZQUNGO1lBRUEsTUFBTWEsVUFBVWhDLFdBQVc7Z0JBQ3pCLElBQUksQ0FBQyxJQUFJLENBQUM4QyxlQUFlLENBQUMrQixHQUFHLENBQUN0QyxhQUFhWixFQUFFLEdBQUc7Z0JBRWhEK0M7Z0JBRUEsSUFBSUEsa0JBQWtCLEdBQUc7b0JBQ3ZCLE1BQU0sSUFBSSxDQUFDVCx1QkFBdUIsQ0FBQzFCLGNBQWNwQjtnQkFDbkQsT0FBTyxJQUFJdUQsaUJBQWlCLEdBQUc7b0JBQzdCLE1BQU0sSUFBSSxDQUFDRixzQkFBc0IsQ0FBQ2pDLGNBQWNwQjtnQkFDbEQ7Z0JBRUF5RDtZQUNGLEdBQUd6RCxTQUFTNEQsZ0JBQWdCLEdBQUc7WUFFL0IsSUFBSSxDQUFDNUMsZ0JBQWdCLENBQUNDLEdBQUcsQ0FBQyxHQUE2QnNDLE9BQTFCbkMsYUFBYVosRUFBRSxFQUFDLFlBQXdCLE9BQWQrQyxnQkFBaUIxQztRQUMxRTtRQUVBNEM7SUFDRjtJQUVBOztHQUVDLEdBQ0RoQiwyQkFBMkJ0QixjQUFzQixFQUFFMEMsU0FBa0IsRUFBUTtRQUMzRSxNQUFNekMsZUFBZSxJQUFJLENBQUNPLGVBQWUsQ0FBQ21DLEdBQUcsQ0FBQzNDO1FBQzlDLElBQUksQ0FBQ0MsY0FBYztRQUVuQkEsYUFBYUssUUFBUSxHQUFHO1FBQ3hCLElBQUksQ0FBQ0UsZUFBZSxDQUFDb0MsTUFBTSxDQUFDNUM7UUFFNUIsV0FBVztRQUNYLElBQUksQ0FBQzZDLHFCQUFxQixDQUFDN0M7UUFFM0IsU0FBUztRQUNULElBQUksQ0FBQzBCLFFBQVEsQ0FBQyxzQkFBc0I7WUFBRTFCO1lBQWdCMEM7UUFBVTtJQUNsRTtJQUVBOztHQUVDLEdBQ0QsTUFBY0YsZ0JBQ1p2QyxZQUFrQyxFQUNsQ3BCLFFBQTBCLEVBQ1g7UUFDZixVQUFVO1FBQ1ZuQixXQUFXO1lBQ1QsSUFBSSxDQUFDZ0UsUUFBUSxDQUFDLHlCQUF5QjtnQkFDckN6QjtnQkFDQTZDLFNBQVMsV0FBcUMsT0FBMUI3QyxhQUFhQyxZQUFZLEVBQUM7WUFDaEQ7UUFDRixHQUFHckIsU0FBU2tFLHlCQUF5QixHQUFHO0lBQzFDO0lBRUE7O0dBRUMsR0FDRCxNQUFjdEIsb0JBQW1DO1FBQy9DLElBQUk7WUFDRixhQUFhO1lBQ2IsSUFBSSxJQUFJLENBQUNuRixhQUFhLElBQUksSUFBSSxDQUFDTyxrQkFBa0IsRUFBRTtnQkFDakQsSUFBSSxDQUFDUCxhQUFhLENBQUMrQixXQUFXLEdBQUcsRUFBRSxTQUFTOztnQkFDNUMsTUFBTSxJQUFJLENBQUMvQixhQUFhLENBQUMwRyxJQUFJO2dCQUM3QjtZQUNGO1lBRUEsb0JBQW9CO1lBQ3BCLElBQUksSUFBSSxDQUFDdkUsdUJBQXVCLEVBQUU7Z0JBQ2hDLE1BQU0sSUFBSSxDQUFDQSx1QkFBdUI7Z0JBQ2xDO1lBQ0Y7WUFFQSxvQkFBb0I7WUFDcEIsSUFBSSxDQUFDd0UsaUJBQWlCO1FBQ3hCLEVBQUUsT0FBT25HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLG1CQUFtQjtZQUNuQixJQUFJLENBQUNtRyxpQkFBaUI7UUFDeEI7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsb0JBQWtDO1FBQ2hDLElBQUk7WUFDRixXQUFXO1lBQ1gsSUFBSSxxQkFBcUJqSCxRQUFRO2dCQUMvQixNQUFNa0gsWUFBWSxJQUFJQyx5QkFBeUI7Z0JBQy9DRCxVQUFVekcsTUFBTSxHQUFHO2dCQUNuQjJHLGdCQUFnQnBCLEtBQUssQ0FBQ2tCO1lBQ3hCO1FBQ0YsRUFBRSxPQUFPcEcsT0FBTztZQUNkQyxRQUFRQyxJQUFJLENBQUMsY0FBY0Y7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBY3VHLDZCQUE0QztRQUN4RCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLO1lBQzFCLE1BQU0sSUFBSSxDQUFDN0IsaUJBQWlCO1lBQzVCLE1BQU0sSUFBSWpFLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVMsTUFBTSxVQUFVOztRQUNuRTtJQUNGO0lBRUE7O0dBRUMsR0FDRCxTQUFpQjhGLEtBQWEsRUFBRUMsSUFBUyxFQUFRO1FBQy9DLElBQUksSUFBNkIsRUFBRTtZQUNqQ3hILE9BQU95SCxhQUFhLENBQUMsSUFBSUMsWUFBWSxjQUFvQixPQUFOSCxRQUFTO2dCQUFFSSxRQUFRSDtZQUFLO1FBQzdFO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHNCQUE4QnhELGNBQXNCLEVBQVE7UUFDMUQsU0FBUztRQUNULE1BQU00RCxjQUFjLElBQUksQ0FBQy9ELGdCQUFnQixDQUFDOEMsR0FBRyxDQUFDM0M7UUFDOUMsSUFBSTRELGFBQWE7WUFDZkMsYUFBYUQ7WUFDYixJQUFJLENBQUMvRCxnQkFBZ0IsQ0FBQytDLE1BQU0sQ0FBQzVDO1FBQy9CO1FBRUEsWUFBWTtRQUNaLEtBQUssTUFBTSxDQUFDOEQsS0FBS3BFLFFBQVEsSUFBSSxJQUFJLENBQUNHLGdCQUFnQixDQUFDa0UsT0FBTyxHQUFJO1lBQzVELElBQUlELElBQUlFLFVBQVUsQ0FBQyxHQUFrQixPQUFmaEUsZ0JBQWUsY0FBWTtnQkFDL0M2RCxhQUFhbkU7Z0JBQ2IsSUFBSSxDQUFDRyxnQkFBZ0IsQ0FBQytDLE1BQU0sQ0FBQ2tCO1lBQy9CO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QseUJBQWlDN0UsSUFBWSxFQUFRO1FBQ25ELE1BQU0sQ0FBQ2dGLE9BQU9DLFFBQVEsR0FBR2pGLEtBQUtrRixLQUFLLENBQUMsS0FBS0MsR0FBRyxDQUFDQztRQUM3QyxNQUFNekUsTUFBTSxJQUFJTDtRQUNoQixNQUFNK0UsWUFBWSxJQUFJL0U7UUFFdEIrRSxVQUFVQyxRQUFRLENBQUNOLE9BQU9DLFNBQVMsR0FBRztRQUV0QyxlQUFlO1FBQ2YsSUFBSUksYUFBYTFFLEtBQUs7WUFDcEIwRSxVQUFVRSxPQUFPLENBQUNGLFVBQVVHLE9BQU8sS0FBSztRQUMxQztRQUVBLE9BQU9IO0lBQ1Q7SUFFQTs7R0FFQyxHQUNESSxxQkFBMkI7UUFDekIsSUFBSSxDQUFDbEUsZUFBZSxDQUFDbUUsS0FBSztRQUUxQixLQUFLLE1BQU1qRixXQUFXLElBQUksQ0FBQ0csZ0JBQWdCLENBQUMrRSxNQUFNLEdBQUk7WUFDcERmLGFBQWFuRTtRQUNmO1FBQ0EsSUFBSSxDQUFDRyxnQkFBZ0IsQ0FBQzhFLEtBQUs7SUFDN0I7SUFFQTs7R0FFQyxHQUNERSxlQUFlekYsVUFBa0IsRUFBUTtRQUN2QyxlQUFlO1FBQ2YsS0FBSyxNQUFNLENBQUNDLElBQUlZLGFBQWEsSUFBSSxJQUFJLENBQUNPLGVBQWUsQ0FBQ3VELE9BQU8sR0FBSTtZQUMvRCxJQUFJOUQsYUFBYWIsVUFBVSxLQUFLQSxZQUFZO2dCQUMxQyxJQUFJLENBQUNvQixlQUFlLENBQUNvQyxNQUFNLENBQUN2RDtnQkFDNUIsSUFBSSxDQUFDd0QscUJBQXFCLENBQUN4RDtZQUM3QjtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEeUYscUJBQTZDO1FBQzNDLE9BQU9DLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUN4RSxlQUFlLENBQUNvRSxNQUFNO0lBQy9DO0lBRUE7O0dBRUMsR0FDREssNEJBQW9EO1FBQ2xELE9BQU8sSUFBSSxDQUFDaEosc0JBQXNCO0lBQ3BDO0lBN2RBaUosYUFBYzthQU5OMUUsa0JBQWtCLElBQUkyRTthQUN0QnRGLG1CQUFtQixJQUFJc0Y7YUFDdkJsSix5QkFBaUQ7YUFDakRLLGdCQUF5QzthQUN6Q08scUJBQXFCO2FBMkdyQjRCLDBCQUF3RDtRQXhHOUQsSUFBSSxJQUE2QixFQUFFO1lBQ2pDLElBQUksQ0FBQzFDLHVCQUF1QjtZQUM1QixJQUFJLENBQUNNLGVBQWU7UUFDdEI7SUFDRjtBQXlkRjtBQUVBLG1CQUFtQjtBQUNuQixJQUFJK0ksOEJBQTBEO0FBRXZELE1BQU1DLHlCQUF5QjtJQUNwQyxJQUFJLEtBQTZCLEVBQUUsRUFXbEM7SUFFRCxJQUFJLENBQUNELDZCQUE2QjtRQUNoQ0EsOEJBQThCLElBQUl0SjtJQUNwQztJQUVBLE9BQU9zSjtBQUNULEVBQUM7QUFFRCxZQUFZO0FBQ0wsTUFBTUUsc0JBQXNCLEtBQTZCLEdBQUdELDJCQUEyQixDQUFJLENBQU8iLCJzb3VyY2VzIjpbIkU6XFxjYXJlYmFvXFxjYXJlYXBwLXdlYlxcc3JjXFxsaWJcXG5vdGlmaWNhdGlvbi1zZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNwZWVjaFNlcnZpY2UgfSBmcm9tICcuL3NwZWVjaC1zZXJ2aWNlJ1xuaW1wb3J0IHR5cGUgeyBNZWRpY2luZVJlbWluZGVyLCBSZW1pbmRlclNldHRpbmdzLCBHdWFyZGlhbkNvbnRhY3QgfSBmcm9tICdAL3R5cGVzJ1xuXG5leHBvcnQgaW50ZXJmYWNlIFJlbWluZGVyTm90aWZpY2F0aW9uIHtcbiAgaWQ6IHN0cmluZ1xuICByZW1pbmRlcklkOiBzdHJpbmdcbiAgbWVkaWNpbmVOYW1lOiBzdHJpbmdcbiAgZG9zYWdlOiBzdHJpbmdcbiAgdXNhZ2U/OiBzdHJpbmdcbiAgc2NoZWR1bGVkVGltZTogc3RyaW5nXG4gIGxldmVsOiAxIHwgMiB8IDMgLy8g5o+Q6YaS57qn5YirXG4gIGlzQWN0aXZlOiBib29sZWFuXG4gIGNyZWF0ZWRBdDogRGF0ZVxufVxuXG5leHBvcnQgY2xhc3MgTm90aWZpY2F0aW9uU2VydmljZSB7XG4gIHByaXZhdGUgYWN0aXZlUmVtaW5kZXJzID0gbmV3IE1hcDxzdHJpbmcsIFJlbWluZGVyTm90aWZpY2F0aW9uPigpXG4gIHByaXZhdGUgcmVtaW5kZXJUaW1lb3V0cyA9IG5ldyBNYXA8c3RyaW5nLCBOb2RlSlMuVGltZW91dD4oKVxuICBwcml2YXRlIG5vdGlmaWNhdGlvblBlcm1pc3Npb246IE5vdGlmaWNhdGlvblBlcm1pc3Npb24gPSAnZGVmYXVsdCdcbiAgcHJpdmF0ZSByZW1pbmRlckF1ZGlvOiBIVE1MQXVkaW9FbGVtZW50IHwgbnVsbCA9IG51bGxcbiAgcHJpdmF0ZSBpc0F1ZGlvSW5pdGlhbGl6ZWQgPSBmYWxzZVxuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgdGhpcy5pbml0aWFsaXplTm90aWZpY2F0aW9ucygpXG4gICAgICB0aGlzLmluaXRpYWxpemVBdWRpbygpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWIneWni+WMlumAmuefpeadg+mZkFxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBpbml0aWFsaXplTm90aWZpY2F0aW9ucygpIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ05vdGlmaWNhdGlvbicgaW4gd2luZG93KSB7XG4gICAgICB0aGlzLm5vdGlmaWNhdGlvblBlcm1pc3Npb24gPSBOb3RpZmljYXRpb24ucGVybWlzc2lvblxuXG4gICAgICBpZiAodGhpcy5ub3RpZmljYXRpb25QZXJtaXNzaW9uID09PSAnZGVmYXVsdCcpIHtcbiAgICAgICAgdGhpcy5ub3RpZmljYXRpb25QZXJtaXNzaW9uID0gYXdhaXQgTm90aWZpY2F0aW9uLnJlcXVlc3RQZXJtaXNzaW9uKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5Yid5aeL5YyW6Z+z6aKR57O757ufXG4gICAqL1xuICBwcml2YXRlIGluaXRpYWxpemVBdWRpbygpIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOmihOWKoOi9veaPkOmGkumfs+aViFxuICAgICAgdGhpcy5yZW1pbmRlckF1ZGlvID0gbmV3IEF1ZGlvKClcbiAgICAgIHRoaXMucmVtaW5kZXJBdWRpby5wcmVsb2FkID0gJ2F1dG8nXG4gICAgICB0aGlzLnJlbWluZGVyQXVkaW8udm9sdW1lID0gMC43XG5cbiAgICAgIC8vIOWwneivleWKoOi9veWkmuenjemfs+aViOagvOW8j1xuICAgICAgY29uc3QgYXVkaW9Tb3VyY2VzID0gW1xuICAgICAgICAnL3NvdW5kcy9yZW1pbmRlci5tcDMnLFxuICAgICAgICAnL3NvdW5kcy9yZW1pbmRlci53YXYnLFxuICAgICAgICAnL3NvdW5kcy9yZW1pbmRlci5vZ2cnXG4gICAgICBdXG5cbiAgICAgIC8vIOS9v+eUqOesrOS4gOS4quWPr+eUqOeahOmfs+mikeagvOW8j1xuICAgICAgZm9yIChjb25zdCBzcmMgb2YgYXVkaW9Tb3VyY2VzKSB7XG4gICAgICAgIHRoaXMucmVtaW5kZXJBdWRpby5zcmMgPSBzcmNcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c5rKh5pyJ6Z+z6aKR5paH5Lu277yM5Yib5bu65ZCI5oiQ6Z+z5pWIXG4gICAgICBpZiAoIXRoaXMucmVtaW5kZXJBdWRpby5zcmMpIHtcbiAgICAgICAgdGhpcy5jcmVhdGVTeW50aGV0aWNSZW1pbmRlclNvdW5kKClcbiAgICAgIH1cblxuICAgICAgdGhpcy5pc0F1ZGlvSW5pdGlhbGl6ZWQgPSB0cnVlXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2Fybign6Z+z6aKR5Yid5aeL5YyW5aSx6LSl77yM5bCG5L2/55So5ZCI5oiQ6Z+z5pWIOicsIGVycm9yKVxuICAgICAgdGhpcy5jcmVhdGVTeW50aGV0aWNSZW1pbmRlclNvdW5kKClcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5Yib5bu65ZCI5oiQ5o+Q6YaS6Z+z5pWIXG4gICAqL1xuICBwcml2YXRlIGNyZWF0ZVN5bnRoZXRpY1JlbWluZGVyU291bmQoKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICAvLyDkvb/nlKhXZWIgQXVkaW8gQVBJ5Yib5bu65ZCI5oiQ6Z+z5pWIXG4gICAgICBjb25zdCBhdWRpb0NvbnRleHQgPSBuZXcgKHdpbmRvdy5BdWRpb0NvbnRleHQgfHwgKHdpbmRvdyBhcyBhbnkpLndlYmtpdEF1ZGlvQ29udGV4dCkoKVxuXG4gICAgICBjb25zdCBjcmVhdGVCZWVwID0gKGZyZXF1ZW5jeTogbnVtYmVyLCBkdXJhdGlvbjogbnVtYmVyLCBkZWxheTogbnVtYmVyID0gMCkgPT4ge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2U8dm9pZD4oKHJlc29sdmUpID0+IHtcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IG9zY2lsbGF0b3IgPSBhdWRpb0NvbnRleHQuY3JlYXRlT3NjaWxsYXRvcigpXG4gICAgICAgICAgICBjb25zdCBnYWluTm9kZSA9IGF1ZGlvQ29udGV4dC5jcmVhdGVHYWluKClcblxuICAgICAgICAgICAgb3NjaWxsYXRvci5jb25uZWN0KGdhaW5Ob2RlKVxuICAgICAgICAgICAgZ2Fpbk5vZGUuY29ubmVjdChhdWRpb0NvbnRleHQuZGVzdGluYXRpb24pXG5cbiAgICAgICAgICAgIG9zY2lsbGF0b3IuZnJlcXVlbmN5LnZhbHVlID0gZnJlcXVlbmN5XG4gICAgICAgICAgICBvc2NpbGxhdG9yLnR5cGUgPSAnc2luZSdcblxuICAgICAgICAgICAgZ2Fpbk5vZGUuZ2Fpbi5zZXRWYWx1ZUF0VGltZSgwLjMsIGF1ZGlvQ29udGV4dC5jdXJyZW50VGltZSlcbiAgICAgICAgICAgIGdhaW5Ob2RlLmdhaW4uZXhwb25lbnRpYWxSYW1wVG9WYWx1ZUF0VGltZSgwLjAxLCBhdWRpb0NvbnRleHQuY3VycmVudFRpbWUgKyBkdXJhdGlvbilcblxuICAgICAgICAgICAgb3NjaWxsYXRvci5zdGFydChhdWRpb0NvbnRleHQuY3VycmVudFRpbWUpXG4gICAgICAgICAgICBvc2NpbGxhdG9yLnN0b3AoYXVkaW9Db250ZXh0LmN1cnJlbnRUaW1lICsgZHVyYXRpb24pXG5cbiAgICAgICAgICAgIHNldFRpbWVvdXQocmVzb2x2ZSwgZHVyYXRpb24gKiAxMDAwKVxuICAgICAgICAgIH0sIGRlbGF5KVxuICAgICAgICB9KVxuICAgICAgfVxuXG4gICAgICAvLyDliJvlu7roh6rlrprkuYnmj5DphpLpn7PmlYjmkq3mlL7lh73mlbBcbiAgICAgIHRoaXMucGxheUN1c3RvbVJlbWluZGVyU291bmQgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgY3JlYXRlQmVlcCg4MDAsIDAuMiwgMCkgICAgLy8g56ys5LiA5aOwXG4gICAgICAgICAgYXdhaXQgY3JlYXRlQmVlcCgxMDAwLCAwLjIsIDEwMCkgLy8g56ys5LqM5aOwXG4gICAgICAgICAgYXdhaXQgY3JlYXRlQmVlcCg4MDAsIDAuMywgMjAwKSAgLy8g56ys5LiJ5aOwXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcign5pKt5pS+5ZCI5oiQ6Z+z5pWI5aSx6LSlOicsIGVycm9yKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignV2ViIEF1ZGlvIEFQSeS4jeWPr+eUqDonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHBsYXlDdXN0b21SZW1pbmRlclNvdW5kOiAoKCkgPT4gUHJvbWlzZTx2b2lkPikgfCBudWxsID0gbnVsbFxuXG4gIC8qKlxuICAgKiDor7fmsYLpgJrnn6XmnYPpmZBcbiAgICovXG4gIGFzeW5jIHJlcXVlc3ROb3RpZmljYXRpb25QZXJtaXNzaW9uKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIGlmICghKCdOb3RpZmljYXRpb24nIGluIHdpbmRvdykpIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cblxuICAgIGlmIChOb3RpZmljYXRpb24ucGVybWlzc2lvbiA9PT0gJ2dyYW50ZWQnKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cblxuICAgIGNvbnN0IHBlcm1pc3Npb24gPSBhd2FpdCBOb3RpZmljYXRpb24ucmVxdWVzdFBlcm1pc3Npb24oKVxuICAgIHRoaXMubm90aWZpY2F0aW9uUGVybWlzc2lvbiA9IHBlcm1pc3Npb25cbiAgICByZXR1cm4gcGVybWlzc2lvbiA9PT0gJ2dyYW50ZWQnXG4gIH1cblxuICAvKipcbiAgICog5Yib5bu655So6I2v5o+Q6YaSXG4gICAqL1xuICBzY2hlZHVsZVJlbWluZGVyKFxuICAgIHJlbWluZGVyOiBNZWRpY2luZVJlbWluZGVyLFxuICAgIHNldHRpbmdzOiBSZW1pbmRlclNldHRpbmdzLFxuICAgIG9uQ29uZmlybTogKHJlbWluZGVySWQ6IHN0cmluZywgY29uZmlybWVkOiBib29sZWFuKSA9PiB2b2lkXG4gICk6IHZvaWQge1xuICAgIHJlbWluZGVyLnNjaGVkdWxlZFRpbWVzLmZvckVhY2godGltZSA9PiB7XG4gICAgICBjb25zdCBzY2hlZHVsZWREYXRlVGltZSA9IHRoaXMuZ2V0TmV4dFNjaGVkdWxlZERhdGVUaW1lKHRpbWUpXG4gICAgICBjb25zdCByZW1pbmRlcklkID0gYCR7cmVtaW5kZXIuaWR9LSR7dGltZX1gXG4gICAgICBcbiAgICAgIC8vIOiuoeeul+esrOS4gOe6p+aPkOmGkuaXtumXtFxuICAgICAgY29uc3QgZmlyc3RSZW1pbmRlclRpbWUgPSBuZXcgRGF0ZShzY2hlZHVsZWREYXRlVGltZS5nZXRUaW1lKCkgLSBzZXR0aW5ncy5maXJzdFJlbWluZGVyTWludXRlcyAqIDYwMDAwKVxuICAgICAgXG4gICAgICBjb25zdCB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHRoaXMudHJpZ2dlclJlbWluZGVyKHJlbWluZGVyLCB0aW1lLCBzZXR0aW5ncywgb25Db25maXJtKVxuICAgICAgfSwgZmlyc3RSZW1pbmRlclRpbWUuZ2V0VGltZSgpIC0gRGF0ZS5ub3coKSlcblxuICAgICAgdGhpcy5yZW1pbmRlclRpbWVvdXRzLnNldChyZW1pbmRlcklkLCB0aW1lb3V0KVxuICAgIH0pXG4gIH1cblxuICAvKipcbiAgICog6Kem5Y+R5o+Q6YaSXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHRyaWdnZXJSZW1pbmRlcihcbiAgICByZW1pbmRlcjogTWVkaWNpbmVSZW1pbmRlcixcbiAgICBzY2hlZHVsZWRUaW1lOiBzdHJpbmcsXG4gICAgc2V0dGluZ3M6IFJlbWluZGVyU2V0dGluZ3MsXG4gICAgb25Db25maXJtOiAocmVtaW5kZXJJZDogc3RyaW5nLCBjb25maXJtZWQ6IGJvb2xlYW4pID0+IHZvaWRcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uSWQgPSBgJHtyZW1pbmRlci5pZH0tJHtzY2hlZHVsZWRUaW1lfS0ke0RhdGUubm93KCl9YFxuICAgIFxuICAgIGNvbnN0IG5vdGlmaWNhdGlvbjogUmVtaW5kZXJOb3RpZmljYXRpb24gPSB7XG4gICAgICBpZDogbm90aWZpY2F0aW9uSWQsXG4gICAgICByZW1pbmRlcklkOiByZW1pbmRlci5pZCxcbiAgICAgIG1lZGljaW5lTmFtZTogcmVtaW5kZXIubWVkaWNpbmVOYW1lLFxuICAgICAgZG9zYWdlOiByZW1pbmRlci5kb3NhZ2UsXG4gICAgICB1c2FnZTogcmVtaW5kZXIudXNhZ2UsXG4gICAgICBzY2hlZHVsZWRUaW1lLFxuICAgICAgbGV2ZWw6IDEsXG4gICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKVxuICAgIH1cblxuICAgIHRoaXMuYWN0aXZlUmVtaW5kZXJzLnNldChub3RpZmljYXRpb25JZCwgbm90aWZpY2F0aW9uKVxuXG4gICAgLy8g56ys5LiA57qn5o+Q6YaSXG4gICAgYXdhaXQgdGhpcy5zaG93Rmlyc3RMZXZlbFJlbWluZGVyKG5vdGlmaWNhdGlvbiwgc2V0dGluZ3MpXG5cbiAgICAvLyDorr7nva7ph43lpI3mj5DphpJcbiAgICB0aGlzLnNjaGVkdWxlUmVwZWF0ZWRSZW1pbmRlcnMobm90aWZpY2F0aW9uLCBzZXR0aW5ncywgb25Db25maXJtKVxuICB9XG5cbiAgLyoqXG4gICAqIOesrOS4gOe6p+aPkOmGku+8muW8ueeqlyArIOWjsOmfs1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBzaG93Rmlyc3RMZXZlbFJlbWluZGVyKFxuICAgIG5vdGlmaWNhdGlvbjogUmVtaW5kZXJOb3RpZmljYXRpb24sXG4gICAgc2V0dGluZ3M6IFJlbWluZGVyU2V0dGluZ3NcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgLy8g5rWP6KeI5Zmo6YCa55+lXG4gICAgaWYgKHNldHRpbmdzLm5vdGlmaWNhdGlvbkVuYWJsZWQgJiYgdGhpcy5ub3RpZmljYXRpb25QZXJtaXNzaW9uID09PSAnZ3JhbnRlZCcpIHtcbiAgICAgIGNvbnN0IGJyb3dzZXJOb3RpZmljYXRpb24gPSBuZXcgTm90aWZpY2F0aW9uKGDnlKjoja/mj5DphpLvvJoke25vdGlmaWNhdGlvbi5tZWRpY2luZU5hbWV9YCwge1xuICAgICAgICBib2R5OiBg6K+35pyN55SoICR7bm90aWZpY2F0aW9uLmRvc2FnZX1gLFxuICAgICAgICBpY29uOiAnL2ljb25zL21lZGljaW5lLnBuZycsXG4gICAgICAgIGJhZGdlOiAnL2ljb25zL2JhZGdlLnBuZycsXG4gICAgICAgIHRhZzogbm90aWZpY2F0aW9uLmlkLFxuICAgICAgICByZXF1aXJlSW50ZXJhY3Rpb246IHRydWUsXG4gICAgICAgIGFjdGlvbnM6IFtcbiAgICAgICAgICB7IGFjdGlvbjogJ2NvbmZpcm0nLCB0aXRsZTogJ+W3suacjeiNrycgfSxcbiAgICAgICAgICB7IGFjdGlvbjogJ3Nub296ZScsIHRpdGxlOiAn56iN5ZCO5o+Q6YaSJyB9XG4gICAgICAgIF1cbiAgICAgIH0pXG5cbiAgICAgIGJyb3dzZXJOb3RpZmljYXRpb24ub25jbGljayA9ICgpID0+IHtcbiAgICAgICAgdGhpcy5oYW5kbGVSZW1pbmRlckNvbmZpcm1hdGlvbihub3RpZmljYXRpb24uaWQsIHRydWUpXG4gICAgICAgIGJyb3dzZXJOb3RpZmljYXRpb24uY2xvc2UoKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWjsOmfs+aPkOmGklxuICAgIGlmIChzZXR0aW5ncy5zb3VuZEVuYWJsZWQpIHtcbiAgICAgIHRoaXMucGxheVJlbWluZGVyU291bmQoKVxuICAgIH1cblxuICAgIC8vIOmhtemdouW8ueeql++8iOmAmui/h+S6i+S7tumAmuefpVVJ57uE5Lu277yJXG4gICAgdGhpcy5ub3RpZnlVSSgncmVtaW5kZXItcG9wdXAnLCBub3RpZmljYXRpb24pXG4gIH1cblxuICAvKipcbiAgICog56ys5LqM57qn5o+Q6YaS77ya5aKe5Yqg6K+t6Z+z5pKt5oqlXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHNob3dTZWNvbmRMZXZlbFJlbWluZGVyKFxuICAgIG5vdGlmaWNhdGlvbjogUmVtaW5kZXJOb3RpZmljYXRpb24sXG4gICAgc2V0dGluZ3M6IFJlbWluZGVyU2V0dGluZ3NcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgbm90aWZpY2F0aW9uLmxldmVsID0gMlxuXG4gICAgLy8g6YeN5aSN56ys5LiA57qn5o+Q6YaSXG4gICAgYXdhaXQgdGhpcy5zaG93Rmlyc3RMZXZlbFJlbWluZGVyKG5vdGlmaWNhdGlvbiwgc2V0dGluZ3MpXG5cbiAgICAvLyDor63pn7Pmkq3miqVcbiAgICBpZiAoc2V0dGluZ3Mudm9pY2VFbmFibGVkICYmIHNwZWVjaFNlcnZpY2UuaXNTcGVlY2hTeW50aGVzaXNTdXBwb3J0ZWQoKSkge1xuICAgICAgY29uc3Qgc3BlZWNoVGV4dCA9IHNwZWVjaFNlcnZpY2UuZ2VuZXJhdGVSZW1pbmRlclNwZWVjaChcbiAgICAgICAgbm90aWZpY2F0aW9uLm1lZGljaW5lTmFtZSxcbiAgICAgICAgbm90aWZpY2F0aW9uLmRvc2FnZSxcbiAgICAgICAgbm90aWZpY2F0aW9uLnVzYWdlXG4gICAgICApXG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHNwZWVjaFNlcnZpY2Uuc3BlYWsoeyB0ZXh0OiBzcGVlY2hUZXh0IH0pXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfor63pn7Pmkq3miqXlpLHotKU6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOesrOS4iee6p+aPkOmGku+8muehruiupOaYr+WQpuW3suacjeiNr1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBzaG93VGhpcmRMZXZlbFJlbWluZGVyKFxuICAgIG5vdGlmaWNhdGlvbjogUmVtaW5kZXJOb3RpZmljYXRpb24sXG4gICAgc2V0dGluZ3M6IFJlbWluZGVyU2V0dGluZ3NcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgbm90aWZpY2F0aW9uLmxldmVsID0gM1xuXG4gICAgLy8g6K+i6Zeu5piv5ZCm5bey5pyN6I2vXG4gICAgY29uc3QgY29uZmlybWF0aW9uVGV4dCA9IGDmgqjmmK/lkKblt7Lnu4/mnI3nlKjkuoYke25vdGlmaWNhdGlvbi5tZWRpY2luZU5hbWV977yfYFxuICAgIFxuICAgIGlmIChzZXR0aW5ncy52b2ljZUVuYWJsZWQgJiYgc3BlZWNoU2VydmljZS5pc1NwZWVjaFN5bnRoZXNpc1N1cHBvcnRlZCgpKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBzcGVlY2hTZXJ2aWNlLnNwZWFrKHsgdGV4dDogY29uZmlybWF0aW9uVGV4dCB9KVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign6K+t6Z+z5pKt5oql5aSx6LSlOicsIGVycm9yKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaYvuekuuehruiupOWvueivneahhlxuICAgIHRoaXMubm90aWZ5VUkoJ2NvbmZpcm1hdGlvbi1kaWFsb2cnLCBub3RpZmljYXRpb24pXG4gIH1cblxuICAvKipcbiAgICog6K6+572u6YeN5aSN5o+Q6YaSXG4gICAqL1xuICBwcml2YXRlIHNjaGVkdWxlUmVwZWF0ZWRSZW1pbmRlcnMoXG4gICAgbm90aWZpY2F0aW9uOiBSZW1pbmRlck5vdGlmaWNhdGlvbixcbiAgICBzZXR0aW5nczogUmVtaW5kZXJTZXR0aW5ncyxcbiAgICBvbkNvbmZpcm06IChyZW1pbmRlcklkOiBzdHJpbmcsIGNvbmZpcm1lZDogYm9vbGVhbikgPT4gdm9pZFxuICApOiB2b2lkIHtcbiAgICBsZXQgcmVtaW5kZXJDb3VudCA9IDFcbiAgICBjb25zdCBtYXhSZW1pbmRlcnMgPSBzZXR0aW5ncy5tYXhSZW1pbmRlcnNcblxuICAgIGNvbnN0IHNjaGVkdWxlTmV4dCA9ICgpID0+IHtcbiAgICAgIGlmICghdGhpcy5hY3RpdmVSZW1pbmRlcnMuaGFzKG5vdGlmaWNhdGlvbi5pZCkgfHwgcmVtaW5kZXJDb3VudCA+PSBtYXhSZW1pbmRlcnMpIHtcbiAgICAgICAgLy8g6L6+5Yiw5pyA5aSn5o+Q6YaS5qyh5pWw77yM6YCa55+l55uR5oqk5Lq6XG4gICAgICAgIGlmIChyZW1pbmRlckNvdW50ID49IG1heFJlbWluZGVycykge1xuICAgICAgICAgIHRoaXMubm90aWZ5R3VhcmRpYW5zKG5vdGlmaWNhdGlvbiwgc2V0dGluZ3MpXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRpbWVvdXQgPSBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHtcbiAgICAgICAgaWYgKCF0aGlzLmFjdGl2ZVJlbWluZGVycy5oYXMobm90aWZpY2F0aW9uLmlkKSkgcmV0dXJuXG5cbiAgICAgICAgcmVtaW5kZXJDb3VudCsrXG5cbiAgICAgICAgaWYgKHJlbWluZGVyQ291bnQgPT09IDIpIHtcbiAgICAgICAgICBhd2FpdCB0aGlzLnNob3dTZWNvbmRMZXZlbFJlbWluZGVyKG5vdGlmaWNhdGlvbiwgc2V0dGluZ3MpXG4gICAgICAgIH0gZWxzZSBpZiAocmVtaW5kZXJDb3VudCA+PSAzKSB7XG4gICAgICAgICAgYXdhaXQgdGhpcy5zaG93VGhpcmRMZXZlbFJlbWluZGVyKG5vdGlmaWNhdGlvbiwgc2V0dGluZ3MpXG4gICAgICAgIH1cblxuICAgICAgICBzY2hlZHVsZU5leHQoKVxuICAgICAgfSwgc2V0dGluZ3MucmVtaW5kZXJJbnRlcnZhbCAqIDYwMDAwKVxuXG4gICAgICB0aGlzLnJlbWluZGVyVGltZW91dHMuc2V0KGAke25vdGlmaWNhdGlvbi5pZH0tcmVwZWF0LSR7cmVtaW5kZXJDb3VudH1gLCB0aW1lb3V0KVxuICAgIH1cblxuICAgIHNjaGVkdWxlTmV4dCgpXG4gIH1cblxuICAvKipcbiAgICog5aSE55CG5o+Q6YaS56Gu6K6kXG4gICAqL1xuICBoYW5kbGVSZW1pbmRlckNvbmZpcm1hdGlvbihub3RpZmljYXRpb25JZDogc3RyaW5nLCBjb25maXJtZWQ6IGJvb2xlYW4pOiB2b2lkIHtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSB0aGlzLmFjdGl2ZVJlbWluZGVycy5nZXQobm90aWZpY2F0aW9uSWQpXG4gICAgaWYgKCFub3RpZmljYXRpb24pIHJldHVyblxuXG4gICAgbm90aWZpY2F0aW9uLmlzQWN0aXZlID0gZmFsc2VcbiAgICB0aGlzLmFjdGl2ZVJlbWluZGVycy5kZWxldGUobm90aWZpY2F0aW9uSWQpXG5cbiAgICAvLyDmuIXpmaTnm7jlhbPnmoTlrprml7blmahcbiAgICB0aGlzLmNsZWFyUmVtaW5kZXJUaW1lb3V0cyhub3RpZmljYXRpb25JZClcblxuICAgIC8vIOmAmuefpVVJ5pu05pawXG4gICAgdGhpcy5ub3RpZnlVSSgncmVtaW5kZXItY29uZmlybWVkJywgeyBub3RpZmljYXRpb25JZCwgY29uZmlybWVkIH0pXG4gIH1cblxuICAvKipcbiAgICog6YCa55+l55uR5oqk5Lq6XG4gICAqL1xuICBwcml2YXRlIGFzeW5jIG5vdGlmeUd1YXJkaWFucyhcbiAgICBub3RpZmljYXRpb246IFJlbWluZGVyTm90aWZpY2F0aW9uLFxuICAgIHNldHRpbmdzOiBSZW1pbmRlclNldHRpbmdzXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIC8vIOW7tui/n+mAmuefpeebkeaKpOS6ulxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgdGhpcy5ub3RpZnlVSSgnZ3VhcmRpYW4tbm90aWZpY2F0aW9uJywge1xuICAgICAgICBub3RpZmljYXRpb24sXG4gICAgICAgIG1lc3NhZ2U6IGDmgqPogIXlj6/og73lv5jorrDmnI3nlKgke25vdGlmaWNhdGlvbi5tZWRpY2luZU5hbWV977yM6K+35Y+K5pe25YWz5rOo44CCYFxuICAgICAgfSlcbiAgICB9LCBzZXR0aW5ncy5ndWFyZGlhbk5vdGlmaWNhdGlvbkRlbGF5ICogNjAwMDApXG4gIH1cblxuICAvKipcbiAgICog5pKt5pS+5o+Q6YaS5aOw6Z+zXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHBsYXlSZW1pbmRlclNvdW5kKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDkvJjlhYjkvb/nlKjpooTliqDovb3nmoTpn7PpopFcbiAgICAgIGlmICh0aGlzLnJlbWluZGVyQXVkaW8gJiYgdGhpcy5pc0F1ZGlvSW5pdGlhbGl6ZWQpIHtcbiAgICAgICAgdGhpcy5yZW1pbmRlckF1ZGlvLmN1cnJlbnRUaW1lID0gMCAvLyDph43nva7mkq3mlL7kvY3nva5cbiAgICAgICAgYXdhaXQgdGhpcy5yZW1pbmRlckF1ZGlvLnBsYXkoKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c6aKE5Yqg6L296Z+z6aKR5LiN5Y+v55So77yM5L2/55So5ZCI5oiQ6Z+z5pWIXG4gICAgICBpZiAodGhpcy5wbGF5Q3VzdG9tUmVtaW5kZXJTb3VuZCkge1xuICAgICAgICBhd2FpdCB0aGlzLnBsYXlDdXN0b21SZW1pbmRlclNvdW5kKClcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIC8vIOacgOWQjueahOWkh+mAieaWueahiO+8mueugOWNleeahGJlZXDpn7PmlYhcbiAgICAgIHRoaXMucGxheUZhbGxiYWNrU291bmQoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfmkq3mlL7mj5DphpLlo7Dpn7PlpLHotKU6JywgZXJyb3IpXG4gICAgICAvLyDlpoLmnpzmiYDmnInpn7PmlYjpg73lpLHotKXvvIzlsJ3or5XlpIfpgInmlrnmoYhcbiAgICAgIHRoaXMucGxheUZhbGxiYWNrU291bmQoKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDlpIfpgInpn7PmlYjvvIjns7vnu59iZWVw77yJXG4gICAqL1xuICBwcml2YXRlIHBsYXlGYWxsYmFja1NvdW5kKCk6IHZvaWQge1xuICAgIHRyeSB7XG4gICAgICAvLyDkvb/nlKjns7vnu5/pu5jorqTpn7PmlYhcbiAgICAgIGlmICgnc3BlZWNoU3ludGhlc2lzJyBpbiB3aW5kb3cpIHtcbiAgICAgICAgY29uc3QgdXR0ZXJhbmNlID0gbmV3IFNwZWVjaFN5bnRoZXNpc1V0dGVyYW5jZSgnJylcbiAgICAgICAgdXR0ZXJhbmNlLnZvbHVtZSA9IDAuMVxuICAgICAgICBzcGVlY2hTeW50aGVzaXMuc3BlYWsodXR0ZXJhbmNlKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+Wkh+mAiemfs+aViOS5n+aXoOazleaSreaUvjonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5pKt5pS+6L+e57ut5o+Q6YaS6Z+z5pWI77yI55So5LqO6YeN6KaB5o+Q6YaS77yJXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHBsYXlJbnRlbnNpdmVSZW1pbmRlclNvdW5kKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMzsgaSsrKSB7XG4gICAgICBhd2FpdCB0aGlzLnBsYXlSZW1pbmRlclNvdW5kKClcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKSAvLyDpl7TpmpQ1MDBtc1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDpgJrnn6VVSee7hOS7tlxuICAgKi9cbiAgcHJpdmF0ZSBub3RpZnlVSShldmVudDogc3RyaW5nLCBkYXRhOiBhbnkpOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudChgbWVkaWNhdGlvbi0ke2V2ZW50fWAsIHsgZGV0YWlsOiBkYXRhIH0pKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDmuIXpmaTmj5DphpLlrprml7blmahcbiAgICovXG4gIHByaXZhdGUgY2xlYXJSZW1pbmRlclRpbWVvdXRzKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICAvLyDmuIXpmaTkuLvlrprml7blmahcbiAgICBjb25zdCBtYWluVGltZW91dCA9IHRoaXMucmVtaW5kZXJUaW1lb3V0cy5nZXQobm90aWZpY2F0aW9uSWQpXG4gICAgaWYgKG1haW5UaW1lb3V0KSB7XG4gICAgICBjbGVhclRpbWVvdXQobWFpblRpbWVvdXQpXG4gICAgICB0aGlzLnJlbWluZGVyVGltZW91dHMuZGVsZXRlKG5vdGlmaWNhdGlvbklkKVxuICAgIH1cblxuICAgIC8vIOa4hemZpOmHjeWkjeaPkOmGkuWumuaXtuWZqFxuICAgIGZvciAoY29uc3QgW2tleSwgdGltZW91dF0gb2YgdGhpcy5yZW1pbmRlclRpbWVvdXRzLmVudHJpZXMoKSkge1xuICAgICAgaWYgKGtleS5zdGFydHNXaXRoKGAke25vdGlmaWNhdGlvbklkfS1yZXBlYXQtYCkpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpXG4gICAgICAgIHRoaXMucmVtaW5kZXJUaW1lb3V0cy5kZWxldGUoa2V5KVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDojrflj5bkuIvmrKHorqHliJLml7bpl7RcbiAgICovXG4gIHByaXZhdGUgZ2V0TmV4dFNjaGVkdWxlZERhdGVUaW1lKHRpbWU6IHN0cmluZyk6IERhdGUge1xuICAgIGNvbnN0IFtob3VycywgbWludXRlc10gPSB0aW1lLnNwbGl0KCc6JykubWFwKE51bWJlcilcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gICAgY29uc3Qgc2NoZWR1bGVkID0gbmV3IERhdGUoKVxuICAgIFxuICAgIHNjaGVkdWxlZC5zZXRIb3Vycyhob3VycywgbWludXRlcywgMCwgMClcbiAgICBcbiAgICAvLyDlpoLmnpzml7bpl7Tlt7Lov4fvvIzorr7nva7kuLrmmI7lpKlcbiAgICBpZiAoc2NoZWR1bGVkIDw9IG5vdykge1xuICAgICAgc2NoZWR1bGVkLnNldERhdGUoc2NoZWR1bGVkLmdldERhdGUoKSArIDEpXG4gICAgfVxuICAgIFxuICAgIHJldHVybiBzY2hlZHVsZWRcbiAgfVxuXG4gIC8qKlxuICAgKiDlj5bmtojmiYDmnInmtLvliqjmj5DphpJcbiAgICovXG4gIGNhbmNlbEFsbFJlbWluZGVycygpOiB2b2lkIHtcbiAgICB0aGlzLmFjdGl2ZVJlbWluZGVycy5jbGVhcigpXG4gICAgXG4gICAgZm9yIChjb25zdCB0aW1lb3V0IG9mIHRoaXMucmVtaW5kZXJUaW1lb3V0cy52YWx1ZXMoKSkge1xuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpXG4gICAgfVxuICAgIHRoaXMucmVtaW5kZXJUaW1lb3V0cy5jbGVhcigpXG4gIH1cblxuICAvKipcbiAgICog5Y+W5raI54m55a6a5o+Q6YaSXG4gICAqL1xuICBjYW5jZWxSZW1pbmRlcihyZW1pbmRlcklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICAvLyDmib7liLDlubbliKDpmaTnm7jlhbPnmoTmtLvliqjmj5DphpJcbiAgICBmb3IgKGNvbnN0IFtpZCwgbm90aWZpY2F0aW9uXSBvZiB0aGlzLmFjdGl2ZVJlbWluZGVycy5lbnRyaWVzKCkpIHtcbiAgICAgIGlmIChub3RpZmljYXRpb24ucmVtaW5kZXJJZCA9PT0gcmVtaW5kZXJJZCkge1xuICAgICAgICB0aGlzLmFjdGl2ZVJlbWluZGVycy5kZWxldGUoaWQpXG4gICAgICAgIHRoaXMuY2xlYXJSZW1pbmRlclRpbWVvdXRzKGlkKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDojrflj5bmtLvliqjmj5DphpLliJfooahcbiAgICovXG4gIGdldEFjdGl2ZVJlbWluZGVycygpOiBSZW1pbmRlck5vdGlmaWNhdGlvbltdIHtcbiAgICByZXR1cm4gQXJyYXkuZnJvbSh0aGlzLmFjdGl2ZVJlbWluZGVycy52YWx1ZXMoKSlcbiAgfVxuXG4gIC8qKlxuICAgKiDmo4Dmn6XpgJrnn6XmnYPpmZDnirbmgIFcbiAgICovXG4gIGdldE5vdGlmaWNhdGlvblBlcm1pc3Npb24oKTogTm90aWZpY2F0aW9uUGVybWlzc2lvbiB7XG4gICAgcmV0dXJuIHRoaXMubm90aWZpY2F0aW9uUGVybWlzc2lvblxuICB9XG59XG5cbi8vIOW7tui/n+WIneWni+WMlu+8jOmBv+WFjeacjeWKoeWZqOerr+a4suafk+mXrumimFxubGV0IG5vdGlmaWNhdGlvblNlcnZpY2VJbnN0YW5jZTogTm90aWZpY2F0aW9uU2VydmljZSB8IG51bGwgPSBudWxsXG5cbmV4cG9ydCBjb25zdCBnZXROb3RpZmljYXRpb25TZXJ2aWNlID0gKCk6IE5vdGlmaWNhdGlvblNlcnZpY2UgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAvLyDmnI3liqHlmajnq6/ov5Tlm57kuIDkuKrnqbrnmoTlrp7njrBcbiAgICByZXR1cm4ge1xuICAgICAgcmVxdWVzdE5vdGlmaWNhdGlvblBlcm1pc3Npb246IGFzeW5jICgpID0+IGZhbHNlLFxuICAgICAgc2NoZWR1bGVSZW1pbmRlcjogKCkgPT4ge30sXG4gICAgICBjYW5jZWxBbGxSZW1pbmRlcnM6ICgpID0+IHt9LFxuICAgICAgY2FuY2VsUmVtaW5kZXI6ICgpID0+IHt9LFxuICAgICAgZ2V0QWN0aXZlUmVtaW5kZXJzOiAoKSA9PiBbXSxcbiAgICAgIGdldE5vdGlmaWNhdGlvblBlcm1pc3Npb246ICgpID0+ICdkZWZhdWx0JyBhcyBOb3RpZmljYXRpb25QZXJtaXNzaW9uLFxuICAgICAgaGFuZGxlUmVtaW5kZXJDb25maXJtYXRpb246ICgpID0+IHt9XG4gICAgfSBhcyBhbnlcbiAgfVxuXG4gIGlmICghbm90aWZpY2F0aW9uU2VydmljZUluc3RhbmNlKSB7XG4gICAgbm90aWZpY2F0aW9uU2VydmljZUluc3RhbmNlID0gbmV3IE5vdGlmaWNhdGlvblNlcnZpY2UoKVxuICB9XG5cbiAgcmV0dXJuIG5vdGlmaWNhdGlvblNlcnZpY2VJbnN0YW5jZVxufVxuXG4vLyDlj6rlnKjlrqLmiLfnq6/lr7zlh7rlrp7kvotcbmV4cG9ydCBjb25zdCBub3RpZmljYXRpb25TZXJ2aWNlID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBnZXROb3RpZmljYXRpb25TZXJ2aWNlKCkgOiBudWxsIGFzIGFueVxuIl0sIm5hbWVzIjpbInNwZWVjaFNlcnZpY2UiLCJOb3RpZmljYXRpb25TZXJ2aWNlIiwiaW5pdGlhbGl6ZU5vdGlmaWNhdGlvbnMiLCJ3aW5kb3ciLCJub3RpZmljYXRpb25QZXJtaXNzaW9uIiwiTm90aWZpY2F0aW9uIiwicGVybWlzc2lvbiIsInJlcXVlc3RQZXJtaXNzaW9uIiwiaW5pdGlhbGl6ZUF1ZGlvIiwicmVtaW5kZXJBdWRpbyIsIkF1ZGlvIiwicHJlbG9hZCIsInZvbHVtZSIsImF1ZGlvU291cmNlcyIsInNyYyIsImNyZWF0ZVN5bnRoZXRpY1JlbWluZGVyU291bmQiLCJpc0F1ZGlvSW5pdGlhbGl6ZWQiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwiYXVkaW9Db250ZXh0IiwiQXVkaW9Db250ZXh0Iiwid2Via2l0QXVkaW9Db250ZXh0IiwiY3JlYXRlQmVlcCIsImZyZXF1ZW5jeSIsImR1cmF0aW9uIiwiZGVsYXkiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJvc2NpbGxhdG9yIiwiY3JlYXRlT3NjaWxsYXRvciIsImdhaW5Ob2RlIiwiY3JlYXRlR2FpbiIsImNvbm5lY3QiLCJkZXN0aW5hdGlvbiIsInZhbHVlIiwidHlwZSIsImdhaW4iLCJzZXRWYWx1ZUF0VGltZSIsImN1cnJlbnRUaW1lIiwiZXhwb25lbnRpYWxSYW1wVG9WYWx1ZUF0VGltZSIsInN0YXJ0Iiwic3RvcCIsInBsYXlDdXN0b21SZW1pbmRlclNvdW5kIiwicmVxdWVzdE5vdGlmaWNhdGlvblBlcm1pc3Npb24iLCJzY2hlZHVsZVJlbWluZGVyIiwicmVtaW5kZXIiLCJzZXR0aW5ncyIsIm9uQ29uZmlybSIsInNjaGVkdWxlZFRpbWVzIiwiZm9yRWFjaCIsInRpbWUiLCJzY2hlZHVsZWREYXRlVGltZSIsImdldE5leHRTY2hlZHVsZWREYXRlVGltZSIsInJlbWluZGVySWQiLCJpZCIsImZpcnN0UmVtaW5kZXJUaW1lIiwiRGF0ZSIsImdldFRpbWUiLCJmaXJzdFJlbWluZGVyTWludXRlcyIsInRpbWVvdXQiLCJ0cmlnZ2VyUmVtaW5kZXIiLCJub3ciLCJyZW1pbmRlclRpbWVvdXRzIiwic2V0Iiwic2NoZWR1bGVkVGltZSIsIm5vdGlmaWNhdGlvbklkIiwibm90aWZpY2F0aW9uIiwibWVkaWNpbmVOYW1lIiwiZG9zYWdlIiwidXNhZ2UiLCJsZXZlbCIsImlzQWN0aXZlIiwiY3JlYXRlZEF0IiwiYWN0aXZlUmVtaW5kZXJzIiwic2hvd0ZpcnN0TGV2ZWxSZW1pbmRlciIsInNjaGVkdWxlUmVwZWF0ZWRSZW1pbmRlcnMiLCJub3RpZmljYXRpb25FbmFibGVkIiwiYnJvd3Nlck5vdGlmaWNhdGlvbiIsImJvZHkiLCJpY29uIiwiYmFkZ2UiLCJ0YWciLCJyZXF1aXJlSW50ZXJhY3Rpb24iLCJhY3Rpb25zIiwiYWN0aW9uIiwidGl0bGUiLCJvbmNsaWNrIiwiaGFuZGxlUmVtaW5kZXJDb25maXJtYXRpb24iLCJjbG9zZSIsInNvdW5kRW5hYmxlZCIsInBsYXlSZW1pbmRlclNvdW5kIiwibm90aWZ5VUkiLCJzaG93U2Vjb25kTGV2ZWxSZW1pbmRlciIsInZvaWNlRW5hYmxlZCIsImlzU3BlZWNoU3ludGhlc2lzU3VwcG9ydGVkIiwic3BlZWNoVGV4dCIsImdlbmVyYXRlUmVtaW5kZXJTcGVlY2giLCJzcGVhayIsInRleHQiLCJzaG93VGhpcmRMZXZlbFJlbWluZGVyIiwiY29uZmlybWF0aW9uVGV4dCIsInJlbWluZGVyQ291bnQiLCJtYXhSZW1pbmRlcnMiLCJzY2hlZHVsZU5leHQiLCJoYXMiLCJub3RpZnlHdWFyZGlhbnMiLCJyZW1pbmRlckludGVydmFsIiwiY29uZmlybWVkIiwiZ2V0IiwiZGVsZXRlIiwiY2xlYXJSZW1pbmRlclRpbWVvdXRzIiwibWVzc2FnZSIsImd1YXJkaWFuTm90aWZpY2F0aW9uRGVsYXkiLCJwbGF5IiwicGxheUZhbGxiYWNrU291bmQiLCJ1dHRlcmFuY2UiLCJTcGVlY2hTeW50aGVzaXNVdHRlcmFuY2UiLCJzcGVlY2hTeW50aGVzaXMiLCJwbGF5SW50ZW5zaXZlUmVtaW5kZXJTb3VuZCIsImkiLCJldmVudCIsImRhdGEiLCJkaXNwYXRjaEV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkZXRhaWwiLCJtYWluVGltZW91dCIsImNsZWFyVGltZW91dCIsImtleSIsImVudHJpZXMiLCJzdGFydHNXaXRoIiwiaG91cnMiLCJtaW51dGVzIiwic3BsaXQiLCJtYXAiLCJOdW1iZXIiLCJzY2hlZHVsZWQiLCJzZXRIb3VycyIsInNldERhdGUiLCJnZXREYXRlIiwiY2FuY2VsQWxsUmVtaW5kZXJzIiwiY2xlYXIiLCJ2YWx1ZXMiLCJjYW5jZWxSZW1pbmRlciIsImdldEFjdGl2ZVJlbWluZGVycyIsIkFycmF5IiwiZnJvbSIsImdldE5vdGlmaWNhdGlvblBlcm1pc3Npb24iLCJjb25zdHJ1Y3RvciIsIk1hcCIsIm5vdGlmaWNhdGlvblNlcnZpY2VJbnN0YW5jZSIsImdldE5vdGlmaWNhdGlvblNlcnZpY2UiLCJub3RpZmljYXRpb25TZXJ2aWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});