"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/store/medication.ts":
/*!*********************************!*\
  !*** ./src/store/medication.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMedication: () => (/* binding */ useMedication),\n/* harmony export */   useMedicationStore: () => (/* binding */ useMedicationStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/medication-service */ \"(app-pages-browser)/./src/lib/medication-service.ts\");\n/* harmony import */ var _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/medication-scheduler */ \"(app-pages-browser)/./src/lib/medication-scheduler.ts\");\n\n\n\nconst useMedicationStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)((set, get)=>({\n        // 初始状态\n        reminders: [],\n        dailySchedule: null,\n        reminderSettings: null,\n        guardianContacts: [],\n        medicationRecords: [],\n        loading: false,\n        error: null,\n        // 加载用药提醒列表\n        loadReminders: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const reminders = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getMedicineReminders(userId);\n                set({\n                    reminders,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 添加用药提醒\n        addReminder: async (reminderData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                // 如果有作息时间，自动计算用药时间\n                const { dailySchedule } = get();\n                if (dailySchedule && reminderData.scheduledTimes.length === 0) {\n                    const calculatedTimes = _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.calculateMedicationTimes(reminderData.usage, reminderData.frequency, dailySchedule);\n                    reminderData.scheduledTimes = _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.validateAndAdjustTimes(calculatedTimes);\n                }\n                const reminder = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.createMedicineReminder(reminderData);\n                const { reminders } = get();\n                set({\n                    reminders: [\n                        reminder,\n                        ...reminders\n                    ],\n                    loading: false\n                });\n                // 如果启用了提醒，重新启动提醒系统\n                if (reminder.isEnabled) {\n                    const { reminderSettings } = get();\n                    if (reminderSettings) {\n                        notificationService.scheduleReminder(reminder, reminderSettings, (reminderId, confirmed)=>{\n                            get().recordMedication({\n                                reminderId,\n                                scheduledTime: new Date().toISOString(),\n                                status: confirmed ? 'taken' : 'missed',\n                                confirmationMethod: 'manual',\n                                userId: reminderData.userId\n                            });\n                        });\n                    }\n                }\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '添加提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 更新用药提醒\n        updateReminder: async (id, updates)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const updatedReminder = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.updateMedicineReminder(id, updates);\n                const { reminders } = get();\n                const newReminders = reminders.map((r)=>r.id === id ? updatedReminder : r);\n                set({\n                    reminders: newReminders,\n                    loading: false\n                });\n                // 更新提醒系统\n                notificationService.cancelReminder(id);\n                if (updatedReminder.isEnabled) {\n                    const { reminderSettings } = get();\n                    if (reminderSettings) {\n                        notificationService.scheduleReminder(updatedReminder, reminderSettings, (reminderId, confirmed)=>{\n                            get().recordMedication({\n                                reminderId,\n                                scheduledTime: new Date().toISOString(),\n                                status: confirmed ? 'taken' : 'missed',\n                                confirmationMethod: 'manual',\n                                userId: updatedReminder.userId\n                            });\n                        });\n                    }\n                }\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '更新提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 删除用药提醒\n        deleteReminder: async (id)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.deleteMedicineReminder(id);\n                const { reminders } = get();\n                set({\n                    reminders: reminders.filter((r)=>r.id !== id),\n                    loading: false\n                });\n                // 取消相关提醒\n                notificationService.cancelReminder(id);\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '删除提醒失败',\n                    loading: false\n                });\n            }\n        },\n        // 加载作息时间\n        loadDailySchedule: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const schedule = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getDailySchedule(userId);\n                set({\n                    dailySchedule: schedule,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载作息时间失败',\n                    loading: false\n                });\n            }\n        },\n        // 保存作息时间\n        saveDailySchedule: async (scheduleData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const schedule = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.saveDailySchedule(scheduleData);\n                set({\n                    dailySchedule: schedule,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '保存作息时间失败',\n                    loading: false\n                });\n            }\n        },\n        // 加载提醒设置\n        loadReminderSettings: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const settings = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getReminderSettings(userId);\n                set({\n                    reminderSettings: settings,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载提醒设置失败',\n                    loading: false\n                });\n            }\n        },\n        // 保存提醒设置\n        saveReminderSettings: async (settingsData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const settings = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.saveReminderSettings(settingsData);\n                set({\n                    reminderSettings: settings,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '保存提醒设置失败',\n                    loading: false\n                });\n            }\n        },\n        // 加载监护人联系人\n        loadGuardianContacts: async (userId)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const contacts = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getGuardianContacts(userId);\n                set({\n                    guardianContacts: contacts,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载监护人联系人失败',\n                    loading: false\n                });\n            }\n        },\n        // 添加监护人联系人\n        addGuardianContact: async (contactData)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const contact = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.addGuardianContact(contactData);\n                const { guardianContacts } = get();\n                set({\n                    guardianContacts: [\n                        ...guardianContacts,\n                        contact\n                    ],\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '添加监护人联系人失败',\n                    loading: false\n                });\n            }\n        },\n        // 记录用药\n        recordMedication: async (recordData)=>{\n            try {\n                const record = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.recordMedication(recordData);\n                const { medicationRecords } = get();\n                set({\n                    medicationRecords: [\n                        record,\n                        ...medicationRecords\n                    ]\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '记录用药失败'\n                });\n            }\n        },\n        // 加载用药记录\n        loadMedicationRecords: async (userId, startDate, endDate)=>{\n            try {\n                set({\n                    loading: true,\n                    error: null\n                });\n                const records = await _lib_medication_service__WEBPACK_IMPORTED_MODULE_0__.medicationService.getMedicationRecords(userId, startDate, endDate);\n                set({\n                    medicationRecords: records,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '加载用药记录失败',\n                    loading: false\n                });\n            }\n        },\n        // 计算用药时间\n        calculateMedicationTimes: (usage, frequency)=>{\n            const { dailySchedule } = get();\n            if (!dailySchedule) {\n                return [\n                    '08:00'\n                ] // 默认时间\n                ;\n            }\n            const times = _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.calculateMedicationTimes(usage, frequency, dailySchedule);\n            return _lib_medication_scheduler__WEBPACK_IMPORTED_MODULE_1__.medicationScheduler.validateAndAdjustTimes(times);\n        },\n        // 启动提醒系统\n        startReminderSystem: async (userId)=>{\n            try {\n                const { reminders, reminderSettings } = get();\n                if (!reminderSettings) {\n                    // 使用默认设置\n                    const defaultSettings = {\n                        userId,\n                        firstReminderMinutes: 15,\n                        reminderInterval: 5,\n                        maxReminders: 6,\n                        soundEnabled: true,\n                        voiceEnabled: true,\n                        notificationEnabled: true,\n                        guardianNotificationDelay: 30\n                    };\n                    await get().saveReminderSettings(defaultSettings);\n                }\n                const settings = get().reminderSettings;\n                // 为所有启用的提醒设置通知\n                reminders.filter((r)=>r.isEnabled).forEach((reminder)=>{\n                    notificationService.scheduleReminder(reminder, settings, (reminderId, confirmed)=>{\n                        get().recordMedication({\n                            reminderId,\n                            scheduledTime: new Date().toISOString(),\n                            status: confirmed ? 'taken' : 'missed',\n                            confirmationMethod: 'manual',\n                            userId\n                        });\n                    });\n                });\n            } catch (error) {\n                set({\n                    error: error instanceof Error ? error.message : '启动提醒系统失败'\n                });\n            }\n        },\n        // 停止提醒系统\n        stopReminderSystem: ()=>{\n            notificationService.cancelAllReminders();\n        },\n        // 清除错误\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        }\n    }));\n// 便捷的hooks\nconst useMedication = ()=>{\n    const store = useMedicationStore();\n    return {\n        // 数据\n        reminders: store.reminders,\n        dailySchedule: store.dailySchedule,\n        reminderSettings: store.reminderSettings,\n        guardianContacts: store.guardianContacts,\n        medicationRecords: store.medicationRecords,\n        // 状态\n        loading: store.loading,\n        error: store.error,\n        // 方法\n        loadReminders: store.loadReminders,\n        addReminder: store.addReminder,\n        updateReminder: store.updateReminder,\n        deleteReminder: store.deleteReminder,\n        loadDailySchedule: store.loadDailySchedule,\n        saveDailySchedule: store.saveDailySchedule,\n        loadReminderSettings: store.loadReminderSettings,\n        saveReminderSettings: store.saveReminderSettings,\n        loadGuardianContacts: store.loadGuardianContacts,\n        addGuardianContact: store.addGuardianContact,\n        recordMedication: store.recordMedication,\n        loadMedicationRecords: store.loadMedicationRecords,\n        calculateMedicationTimes: store.calculateMedicationTimes,\n        startReminderSystem: store.startReminderSystem,\n        stopReminderSystem: store.stopReminderSystem,\n        clearError: store.clearError\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/medication.ts\n"));

/***/ })

});