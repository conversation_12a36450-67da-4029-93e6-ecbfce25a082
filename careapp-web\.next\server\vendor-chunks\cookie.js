"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cookie";
exports.ids = ["vendor-chunks/cookie"];
exports.modules = {

/***/ "(ssr)/./node_modules/cookie/dist/index.js":
/*!*******************************************!*\
  !*** ./node_modules/cookie/dist/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parse = parse;\nexports.serialize = serialize;\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\nconst __toString = Object.prototype.toString;\nconst NullObject = /* @__PURE__ */ (() => {\n    const C = function () { };\n    C.prototype = Object.create(null);\n    return C;\n})();\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nfunction parse(str, options) {\n    const obj = new NullObject();\n    const len = str.length;\n    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n    if (len < 2)\n        return obj;\n    const dec = options?.decode || decode;\n    let index = 0;\n    do {\n        const eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1)\n            break; // No more cookie pairs.\n        const colonIdx = str.indexOf(\";\", index);\n        const endIdx = colonIdx === -1 ? len : colonIdx;\n        if (eqIdx > endIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        const keyStartIdx = startIndex(str, index, eqIdx);\n        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        const key = str.slice(keyStartIdx, keyEndIdx);\n        // only assign once\n        if (obj[key] === undefined) {\n            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n            const value = dec(str.slice(valStartIdx, valEndIdx));\n            obj[key] = value;\n        }\n        index = endIdx + 1;\n    } while (index < len);\n    return obj;\n}\nfunction startIndex(str, index, max) {\n    do {\n        const code = str.charCodeAt(index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index;\n    } while (++index < max);\n    return max;\n}\nfunction endIndex(str, index, min) {\n    while (index > min) {\n        const code = str.charCodeAt(--index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index + 1;\n    }\n    return min;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nfunction serialize(name, val, options) {\n    const enc = options?.encode || encodeURIComponent;\n    if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(`argument name is invalid: ${name}`);\n    }\n    const value = enc(val);\n    if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(`argument val is invalid: ${val}`);\n    }\n    let str = name + \"=\" + value;\n    if (!options)\n        return str;\n    if (options.maxAge !== undefined) {\n        if (!Number.isInteger(options.maxAge)) {\n            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n        }\n        str += \"; Max-Age=\" + options.maxAge;\n    }\n    if (options.domain) {\n        if (!domainValueRegExp.test(options.domain)) {\n            throw new TypeError(`option domain is invalid: ${options.domain}`);\n        }\n        str += \"; Domain=\" + options.domain;\n    }\n    if (options.path) {\n        if (!pathValueRegExp.test(options.path)) {\n            throw new TypeError(`option path is invalid: ${options.path}`);\n        }\n        str += \"; Path=\" + options.path;\n    }\n    if (options.expires) {\n        if (!isDate(options.expires) ||\n            !Number.isFinite(options.expires.valueOf())) {\n            throw new TypeError(`option expires is invalid: ${options.expires}`);\n        }\n        str += \"; Expires=\" + options.expires.toUTCString();\n    }\n    if (options.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (options.secure) {\n        str += \"; Secure\";\n    }\n    if (options.partitioned) {\n        str += \"; Partitioned\";\n    }\n    if (options.priority) {\n        const priority = typeof options.priority === \"string\"\n            ? options.priority.toLowerCase()\n            : undefined;\n        switch (priority) {\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(`option priority is invalid: ${options.priority}`);\n        }\n    }\n    if (options.sameSite) {\n        const sameSite = typeof options.sameSite === \"string\"\n            ? options.sameSite.toLowerCase()\n            : options.sameSite;\n        switch (sameSite) {\n            case true:\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str) {\n    if (str.indexOf(\"%\") === -1)\n        return str;\n    try {\n        return decodeURIComponent(str);\n    }\n    catch (e) {\n        return str;\n    }\n}\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val) {\n    return __toString.call(val) === \"[object Date]\";\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/cookie/dist/index.js\n");

/***/ })

};
;