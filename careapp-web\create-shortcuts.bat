@echo off
title CareApp Desktop Shortcuts Creator

echo.
echo ========================================
echo     CareApp Desktop Shortcuts Creator
echo ========================================
echo.

echo Creating desktop shortcuts...
echo.

REM Get desktop path
for /f "tokens=3*" %%i in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP=%%i %%j

REM Create CareApp Home shortcut
echo [InternetShortcut] > "%DESKTOP%\CareApp - Home.url"
echo URL=http://localhost:3000 >> "%DESKTOP%\CareApp - Home.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\CareApp - Home.url"
echo IconIndex=13 >> "%DESKTOP%\CareApp - Home.url"

REM Create Medication Reminder shortcut
echo [InternetShortcut] > "%DESKTOP%\CareApp - Medication.url"
echo URL=http://localhost:3000/medication >> "%DESKTOP%\CareApp - Medication.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\CareApp - Medication.url"
echo IconIndex=23 >> "%DESKTOP%\CareApp - Medication.url"

REM Create Auth shortcut
echo [InternetShortcut] > "%DESKTOP%\CareApp - Login.url"
echo URL=http://localhost:3000/auth-demo >> "%DESKTOP%\CareApp - Login.url"
echo IconFile=%%SystemRoot%%\system32\SHELL32.dll >> "%DESKTOP%\CareApp - Login.url"
echo IconIndex=48 >> "%DESKTOP%\CareApp - Login.url"

echo Desktop shortcuts created successfully!
echo.
echo Created shortcuts:
echo - CareApp - Home.url
echo - CareApp - Medication.url  
echo - CareApp - Login.url
echo.
echo Note: Make sure the development server is running (npm run dev)
echo Double-click desktop shortcuts to access CareApp system!
echo.

REM Ask if user wants to start dev server
set /p choice="Start development server now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting development server...
    start "CareApp Dev Server" cmd /k "cd /d %~dp0 && npm run dev"
    echo Development server started in new window!
)

echo.
echo Script completed!
pause
