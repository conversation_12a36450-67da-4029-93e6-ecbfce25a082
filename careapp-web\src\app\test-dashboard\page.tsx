'use client'

import { useRouter } from 'next/navigation'

export default function TestDashboardPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <h1 className="text-2xl font-bold mb-6">测试仪表板</h1>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <p className="text-green-600">✅ 页面跳转成功！没有抖动问题。</p>
        </div>

        <div className="space-y-4">
          <button
            type="button"
            onClick={() => router.replace('/test-auth')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
          >
            返回测试认证
          </button>

          <button
            type="button"
            onClick={() => router.replace('/')}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
          >
            返回首页
          </button>
        </div>
      </div>
    </div>
  )
}
