import { Heart } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <div className="flex items-center justify-center mb-8">
          <Heart className="w-16 h-16 text-red-500 mr-4" />
          <h1 className="text-5xl font-bold text-gray-800">照护宝</h1>
        </div>
        <p className="text-2xl text-gray-600 mb-8">
          智能健康护理平台 - WEB版
        </p>
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
          <div className="space-y-4">
            <div className="text-green-600 text-lg font-semibold">
              ✅ WEB版本开发完成
            </div>
            <div className="text-blue-600 text-lg font-semibold">
              🔐 用户认证系统已就绪
            </div>
            <div className="text-purple-600 text-lg font-semibold">
              🚀 核心功能开发中
            </div>
          </div>
          <div className="mt-6 space-x-4">
            <a
              href="/test"
              className="inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              测试页面
            </a>
            <a
              href="/auth"
              className="inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              认证系统
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}




