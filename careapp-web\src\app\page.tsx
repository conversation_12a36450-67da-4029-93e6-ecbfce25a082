import { Heart } from 'lucide-react'
import { DemoNotice } from '@/components/DemoNotice'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <DemoNotice />
      <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="flex items-center justify-center mb-8">
          <Heart className="w-16 h-16 text-red-500 mr-4" />
          <h1 className="text-5xl font-bold text-gray-800">照护宝</h1>
        </div>
        <p className="text-2xl text-gray-600 mb-8">
          智能健康护理平台 - WEB版
        </p>
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
          <div className="space-y-4">
            <div className="text-green-600 text-lg font-semibold">
              ✅ WEB版本开发完成
            </div>
            <div className="text-blue-600 text-lg font-semibold">
              🔐 用户认证系统已就绪
            </div>
            <div className="text-purple-600 text-lg font-semibold">
              🚀 核心功能开发中
            </div>
          </div>
          <div className="mt-6 space-y-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <a
                href="/test"
                className="inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center"
              >
                测试页面
              </a>
              <a
                href="/auth-demo"
                className="inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors text-center"
              >
                认证演示
              </a>
              <a
                href="/medication"
                className="inline-block bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center"
              >
                💊 用药提醒
              </a>
              <a
                href="/dashboard"
                className="inline-block bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors text-center"
              >
                📊 仪表板
              </a>
              <a
                href="/test-auth"
                className="inline-block bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors text-center"
              >
                🧪 测试认证
              </a>
            </div>
            <div className="text-sm text-gray-500 text-center">
              点击上方按钮体验系统功能
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  )
}




