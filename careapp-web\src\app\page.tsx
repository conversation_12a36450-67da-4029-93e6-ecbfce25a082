'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/store/auth'
import { Heart } from 'lucide-react'

export default function HomePage() {
  const { user, initialized, initialize } = useAuth()
  const router = useRouter()

  useEffect(() => {
    initialize()
  }, [initialize])

  useEffect(() => {
    if (initialized) {
      if (user) {
        router.push('/dashboard')
      } else {
        router.push('/auth')
      }
    }
  }, [user, initialized, router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <div className="flex items-center justify-center mb-8">
          <Heart className="w-16 h-16 text-red-500 mr-4" />
          <h1 className="text-5xl font-bold text-gray-800">照护宝</h1>
        </div>
        <p className="text-2xl text-gray-600 mb-8">
          智能健康护理平台 - WEB版
        </p>
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600">正在初始化系统...</p>
        </div>
      </div>
    </div>
  )
}




