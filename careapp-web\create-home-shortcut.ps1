# 照护宝主页桌面快捷方式创建脚本
# PowerShell script to create CareApp home page desktop shortcut

Write-Host "正在创建照护宝主页桌面快捷方式..." -ForegroundColor Green

# 获取桌面路径
$DesktopPath = [Environment]::GetFolderPath("Desktop")

# 创建照护宝主页快捷方式
$shortcutPath = Join-Path $DesktopPath "照护宝.url"

# 创建URL快捷方式内容
$content = @"
[InternetShortcut]
URL=http://localhost:3000
IconFile=%SystemRoot%\system32\SHELL32.dll
IconIndex=13
"@

# 写入文件
$content | Out-File -FilePath $shortcutPath -Encoding ASCII

Write-Host "✅ 桌面快捷方式创建成功！" -ForegroundColor Green
Write-Host ""
Write-Host "已创建快捷方式：" -ForegroundColor Yellow
Write-Host "📱 照护宝.url - 直达主页" -ForegroundColor Cyan
Write-Host ""
Write-Host "使用说明：" -ForegroundColor Yellow
Write-Host "1. 双击 '启动服务器.bat' 启动开发服务器" -ForegroundColor White
Write-Host "2. 双击桌面上的 '照护宝.url' 打开系统" -ForegroundColor White
Write-Host "3. 在主页点击相应按钮进入各功能模块" -ForegroundColor White
Write-Host ""
Write-Host "功能导航：" -ForegroundColor Yellow
Write-Host "- 💊 用药提醒：智能用药管理系统" -ForegroundColor White
Write-Host "- 🔐 认证演示：用户注册登录" -ForegroundColor White
Write-Host "- 📊 仪表板：系统概览" -ForegroundColor White
Write-Host "- 🧪 测试认证：简化版认证测试" -ForegroundColor White

# 询问是否启动开发服务器
Write-Host ""
$response = Read-Host "是否现在启动开发服务器？(y/n)"
if ($response -eq "y" -or $response -eq "Y") {
    Write-Host "正在启动开发服务器..." -ForegroundColor Green
    Set-Location $PSScriptRoot
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev"
    Write-Host "开发服务器已在新窗口中启动！" -ForegroundColor Green
    Write-Host "等待服务器启动完成后，双击桌面快捷方式即可访问！" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "脚本执行完成！按任意键退出..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
