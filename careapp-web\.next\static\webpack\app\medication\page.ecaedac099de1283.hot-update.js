"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/speech-service.ts":
/*!***********************************!*\
  !*** ./src/lib/speech-service.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeechService: () => (/* binding */ SpeechService),\n/* harmony export */   speechService: () => (/* binding */ speechService)\n/* harmony export */ });\nclass SpeechService {\n    /**\n   * 初始化语音识别\n   */ initializeSpeechRecognition() {\n        if (true) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (SpeechRecognition) {\n                this.recognition = new SpeechRecognition();\n                this.recognition.continuous = false;\n                this.recognition.interimResults = true;\n                this.recognition.lang = 'zh-CN';\n                this.recognition.maxAlternatives = 1;\n            }\n        }\n    }\n    /**\n   * 初始化语音合成\n   */ initializeSpeechSynthesis() {\n        if ( true && 'speechSynthesis' in window) {\n            this.synthesis = window.speechSynthesis;\n        }\n    }\n    /**\n   * 初始化药品数据库\n   */ initializeMedicineDatabase() {\n        // 常用药品及其别名\n        const medicines = [\n            // 心血管药物\n            {\n                name: '阿司匹林',\n                aliases: [\n                    '阿斯匹林',\n                    '阿司匹林肠溶片',\n                    '拜阿司匹灵'\n                ]\n            },\n            {\n                name: '硝苯地平',\n                aliases: [\n                    '硝苯地平片',\n                    '心痛定',\n                    '拜新同'\n                ]\n            },\n            {\n                name: '卡托普利',\n                aliases: [\n                    '卡托普利片',\n                    '开博通'\n                ]\n            },\n            {\n                name: '美托洛尔',\n                aliases: [\n                    '美托洛尔片',\n                    '倍他乐克',\n                    '酒石酸美托洛尔'\n                ]\n            },\n            // 降糖药物\n            {\n                name: '二甲双胍',\n                aliases: [\n                    '二甲双胍片',\n                    '格华止',\n                    '美迪康'\n                ]\n            },\n            {\n                name: '格列齐特',\n                aliases: [\n                    '格列齐特片',\n                    '达美康',\n                    '迪沙片'\n                ]\n            },\n            {\n                name: '胰岛素',\n                aliases: [\n                    '胰岛素注射液',\n                    '诺和灵',\n                    '优泌林'\n                ]\n            },\n            // 抗生素\n            {\n                name: '阿莫西林',\n                aliases: [\n                    '阿莫西林胶囊',\n                    '阿莫西林片',\n                    '再林'\n                ]\n            },\n            {\n                name: '头孢拉定',\n                aliases: [\n                    '头孢拉定胶囊',\n                    '头孢拉定片'\n                ]\n            },\n            {\n                name: '左氧氟沙星',\n                aliases: [\n                    '左氧氟沙星片',\n                    '可乐必妥',\n                    '来立信'\n                ]\n            },\n            // 消化系统药物\n            {\n                name: '奥美拉唑',\n                aliases: [\n                    '奥美拉唑肠溶胶囊',\n                    '洛赛克',\n                    '奥克'\n                ]\n            },\n            {\n                name: '多潘立酮',\n                aliases: [\n                    '多潘立酮片',\n                    '吗丁啉',\n                    '胃复安'\n                ]\n            },\n            {\n                name: '蒙脱石散',\n                aliases: [\n                    '思密达',\n                    '必奇'\n                ]\n            },\n            // 感冒药物\n            {\n                name: '对乙酰氨基酚',\n                aliases: [\n                    '对乙酰氨基酚片',\n                    '泰诺林',\n                    '百服宁',\n                    '扑热息痛'\n                ]\n            },\n            {\n                name: '布洛芬',\n                aliases: [\n                    '布洛芬片',\n                    '芬必得',\n                    '美林'\n                ]\n            },\n            {\n                name: '复方氨酚烷胺',\n                aliases: [\n                    '快克',\n                    '感康'\n                ]\n            },\n            // 维生素类\n            {\n                name: '维生素C',\n                aliases: [\n                    '维生素C片',\n                    'VC片',\n                    '维C'\n                ]\n            },\n            {\n                name: '维生素D',\n                aliases: [\n                    '维生素D3',\n                    'VD3',\n                    '钙尔奇D'\n                ]\n            },\n            {\n                name: '复合维生素B',\n                aliases: [\n                    '维生素B族',\n                    'VB片',\n                    '复合VB'\n                ]\n            }\n        ];\n        // 构建药品数据库\n        medicines.forEach((medicine)=>{\n            const allNames = [\n                medicine.name,\n                ...medicine.aliases\n            ];\n            this.medicineDatabase.set(medicine.name, allNames);\n            this.commonMedicines.push(medicine.name);\n            // 为每个别名也建立映射\n            medicine.aliases.forEach((alias)=>{\n                this.medicineDatabase.set(alias, allNames);\n            });\n        });\n    }\n    /**\n   * 检查浏览器是否支持语音识别\n   */ isSpeechRecognitionSupported() {\n        return this.recognition !== null;\n    }\n    /**\n   * 检查浏览器是否支持语音合成\n   */ isSpeechSynthesisSupported() {\n        return this.synthesis !== null;\n    }\n    /**\n   * 开始语音识别\n   */ async startListening(onResult, onError) {\n        if (!this.recognition) {\n            throw new Error('语音识别不支持');\n        }\n        if (this.isListening) {\n            this.stopListening();\n        }\n        return new Promise((resolve, reject)=>{\n            this.recognition.onstart = ()=>{\n                this.isListening = true;\n                resolve();\n            };\n            this.recognition.onresult = (event)=>{\n                const result = event.results[event.results.length - 1];\n                const transcript = result[0].transcript;\n                const confidence = result[0].confidence;\n                const isFinal = result.isFinal;\n                onResult({\n                    transcript: transcript.trim(),\n                    confidence,\n                    isFinal\n                });\n            };\n            this.recognition.onerror = (event)=>{\n                this.isListening = false;\n                const errorMessage = this.getErrorMessage(event.error);\n                onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n                reject(new Error(errorMessage));\n            };\n            this.recognition.onend = ()=>{\n                this.isListening = false;\n            };\n            try {\n                this.recognition.start();\n            } catch (error) {\n                this.isListening = false;\n                reject(error);\n            }\n        });\n    }\n    /**\n   * 停止语音识别\n   */ stopListening() {\n        if (this.recognition && this.isListening) {\n            this.recognition.stop();\n            this.isListening = false;\n        }\n    }\n    /**\n   * 语音播报\n   */ async speak(options) {\n        if (!this.synthesis) {\n            throw new Error('语音合成不支持');\n        }\n        // 停止当前播报\n        this.synthesis.cancel();\n        return new Promise((resolve, reject)=>{\n            const utterance = new SpeechSynthesisUtterance(options.text);\n            utterance.lang = options.lang || 'zh-CN';\n            utterance.rate = options.rate || 1;\n            utterance.pitch = options.pitch || 1;\n            utterance.volume = options.volume || 1;\n            utterance.onend = ()=>resolve();\n            utterance.onerror = (event)=>reject(new Error(\"语音播报失败: \".concat(event.error)));\n            // 获取中文语音\n            const voices = this.synthesis.getVoices();\n            const chineseVoice = voices.find((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n            if (chineseVoice) {\n                utterance.voice = chineseVoice;\n            }\n            this.synthesis.speak(utterance);\n        });\n    }\n    /**\n   * 停止语音播报\n   */ stopSpeaking() {\n        if (this.synthesis) {\n            this.synthesis.cancel();\n        }\n    }\n    /**\n   * 解析药品信息的语音输入（增强版）\n   */ parseMedicineInput(transcript) {\n        const result = {};\n        const cleanTranscript = this.cleanTranscript(transcript);\n        // 智能提取药品名称\n        const medicineInfo = this.extractMedicineName(cleanTranscript);\n        if (medicineInfo) {\n            result.medicineName = medicineInfo.name;\n            result.confidence = medicineInfo.confidence;\n            result.suggestions = medicineInfo.suggestions;\n        }\n        // 提取剂量信息\n        const dosagePatterns = [\n            /(\\d+)\\s*片/,\n            /(\\d+)\\s*粒/,\n            /(\\d+)\\s*毫升/,\n            /(\\d+)\\s*ml/,\n            /(\\d+)\\s*毫克/,\n            /(\\d+)\\s*mg/,\n            /(\\d+)\\s*滴/\n        ];\n        for (const pattern of dosagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                const unit = match[0].replace(match[1], '').trim();\n                result.dosage = \"\".concat(match[1]).concat(unit);\n                break;\n            }\n        }\n        // 提取用法信息\n        const usagePatterns = [\n            /餐前\\s*(\\d+)?\\s*分钟?/,\n            /饭前\\s*(\\d+)?\\s*分钟?/,\n            /餐后\\s*(\\d+)?\\s*分钟?/,\n            /饭后\\s*(\\d+)?\\s*分钟?/,\n            /睡前\\s*(\\d+)?\\s*分钟?/,\n            /晨起/,\n            /起床后/\n        ];\n        for (const pattern of usagePatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.usage = match[0];\n                break;\n            }\n        }\n        // 提取频次信息\n        const frequencyPatterns = [\n            /每日\\s*(\\d+)\\s*次/,\n            /一日\\s*(\\d+)\\s*次/,\n            /每天\\s*(\\d+)\\s*次/,\n            /(\\d+)\\s*次\\s*每日/,\n            /(\\d+)\\s*次\\s*一日/\n        ];\n        for (const pattern of frequencyPatterns){\n            const match = transcript.match(pattern);\n            if (match) {\n                result.frequency = match[0];\n                break;\n            }\n        }\n        return result;\n    }\n    /**\n   * 清理语音识别文本\n   */ cleanTranscript(transcript) {\n        return transcript.replace(/[，。！？；：\"\"''（）【】]/g, ' ') // 替换标点符号\n        .replace(/\\s+/g, ' ') // 合并多个空格\n        .trim().toLowerCase();\n    }\n    /**\n   * 智能提取药品名称\n   */ extractMedicineName(transcript) {\n        const words = transcript.split(' ');\n        let bestMatch = null;\n        // 1. 精确匹配\n        for (const [medicine, aliases] of this.medicineDatabase.entries()){\n            for (const alias of aliases){\n                if (transcript.includes(alias.toLowerCase())) {\n                    return {\n                        name: medicine,\n                        confidence: 1.0,\n                        suggestions: []\n                    };\n                }\n            }\n        }\n        // 2. 模糊匹配\n        for (const medicine of this.commonMedicines){\n            const similarity = this.calculateSimilarity(transcript, medicine.toLowerCase());\n            if (similarity > 0.6) {\n                if (!bestMatch || similarity > bestMatch.confidence) {\n                    bestMatch = {\n                        name: medicine,\n                        confidence: similarity,\n                        suggestions: this.getSimilarMedicines(medicine, 3)\n                    };\n                }\n            }\n        }\n        // 3. 部分匹配\n        if (!bestMatch) {\n            for (const word of words){\n                if (word.length >= 2) {\n                    for (const medicine of this.commonMedicines){\n                        if (medicine.toLowerCase().includes(word) || word.includes(medicine.toLowerCase().substring(0, 2))) {\n                            const similarity = this.calculateSimilarity(word, medicine.toLowerCase());\n                            if (similarity > 0.4) {\n                                if (!bestMatch || similarity > bestMatch.confidence) {\n                                    bestMatch = {\n                                        name: medicine,\n                                        confidence: similarity * 0.8,\n                                        suggestions: this.getSimilarMedicines(medicine, 5)\n                                    };\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        return bestMatch;\n    }\n    /**\n   * 计算字符串相似度（使用编辑距离）\n   */ calculateSimilarity(str1, str2) {\n        const len1 = str1.length;\n        const len2 = str2.length;\n        if (len1 === 0) return len2 === 0 ? 1 : 0;\n        if (len2 === 0) return 0;\n        const matrix = Array(len1 + 1).fill(null).map(()=>Array(len2 + 1).fill(null));\n        for(let i = 0; i <= len1; i++)matrix[i][0] = i;\n        for(let j = 0; j <= len2; j++)matrix[0][j] = j;\n        for(let i = 1; i <= len1; i++){\n            for(let j = 1; j <= len2; j++){\n                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;\n                matrix[i][j] = Math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + cost // substitution\n                );\n            }\n        }\n        const maxLen = Math.max(len1, len2);\n        return (maxLen - matrix[len1][len2]) / maxLen;\n    }\n    /**\n   * 获取相似药品建议\n   */ getSimilarMedicines(medicine, count) {\n        const similarities = this.commonMedicines.filter((m)=>m !== medicine).map((m)=>({\n                name: m,\n                similarity: this.calculateSimilarity(medicine.toLowerCase(), m.toLowerCase())\n            })).sort((a, b)=>b.similarity - a.similarity).slice(0, count).map((item)=>item.name);\n        return similarities;\n    }\n    /**\n   * 生成用药提醒的语音内容\n   */ generateReminderSpeech(medicineName, dosage, usage) {\n        let speech = \"请注意，现在是服用\".concat(medicineName, \"的时间。\");\n        if (dosage) {\n            speech += \"请服用\".concat(dosage, \"。\");\n        }\n        if (usage) {\n            speech += \"用法：\".concat(usage, \"。\");\n        }\n        speech += '请确认是否已经服药。';\n        return speech;\n    }\n    /**\n   * 获取错误信息\n   */ getErrorMessage(error) {\n        switch(error){\n            case 'no-speech':\n                return '没有检测到语音输入';\n            case 'audio-capture':\n                return '无法访问麦克风';\n            case 'not-allowed':\n                return '麦克风权限被拒绝';\n            case 'network':\n                return '网络错误';\n            case 'service-not-allowed':\n                return '语音识别服务不可用';\n            default:\n                return \"语音识别错误: \".concat(error);\n        }\n    }\n    /**\n   * 请求麦克风权限\n   */ async requestMicrophonePermission() {\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            stream.getTracks().forEach((track)=>track.stop());\n            return true;\n        } catch (error) {\n            console.error('麦克风权限请求失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 获取可用的语音列表\n   */ getAvailableVoices() {\n        if (!this.synthesis) return [];\n        return this.synthesis.getVoices().filter((voice)=>voice.lang.includes('zh') || voice.lang.includes('CN'));\n    }\n    /**\n   * 测试语音功能\n   */ async testSpeech() {\n        const result = {\n            recognition: this.isSpeechRecognitionSupported(),\n            synthesis: this.isSpeechSynthesisSupported()\n        };\n        if (result.synthesis) {\n            try {\n                await this.speak({\n                    text: '语音功能测试正常'\n                });\n            } catch (error) {\n                result.synthesis = false;\n            }\n        }\n        return result;\n    }\n    constructor(){\n        this.recognition = null;\n        this.synthesis = null;\n        this.isListening = false;\n        this.medicineDatabase = new Map();\n        this.commonMedicines = [];\n        this.initializeSpeechRecognition();\n        this.initializeSpeechSynthesis();\n        this.initializeMedicineDatabase();\n    }\n}\nconst speechService = new SpeechService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/speech-service.ts\n"));

/***/ })

});