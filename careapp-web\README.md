# 照护宝 WEB版

基于Android版本重构的现代化WEB应用，提供智能健康护理服务。

## 🚀 技术栈

- **前端**: Next.js 15 + React 18 + TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **UI组件**: Lucide React Icons

## 📦 功能特性

- ✅ 用户认证系统（注册/登录/登出）
- ✅ 响应式设计
- ✅ TypeScript 类型安全
- 🔄 用药提醒系统（开发中）
- 🔄 健康数据监测（开发中）
- 🔄 紧急呼叫系统（开发中）
- 🔄 家庭互动功能（开发中）

## 🛠️ 开发环境设置

### 1. 安装依赖

```bash
npm install
```

### 2. 配置Supabase

1. 访问 [Supabase](https://supabase.com) 创建新项目
2. 在项目设置中获取以下信息：
   - Project URL
   - Anon Key
   - Service Role Key

3. 复制 `.env.local.example` 为 `.env.local` 并填入配置：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. 初始化数据库

在Supabase SQL编辑器中执行以下文件：

1. `supabase/migrations/001_initial_schema.sql` - 创建数据表
2. `supabase/seed.sql` - 插入示例数据（可选）

### 4. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
careapp-web/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表板
│   │   └── globals.css     # 全局样式
│   ├── components/         # React组件
│   │   └── auth/          # 认证相关组件
│   ├── lib/               # 工具函数
│   │   ├── auth.ts        # 认证服务
│   │   ├── supabase.ts    # Supabase配置
│   │   └── utils.ts       # 通用工具
│   ├── store/             # 状态管理
│   │   └── auth.ts        # 认证状态
│   └── types/             # TypeScript类型
├── supabase/              # 数据库相关
│   ├── migrations/        # 数据库迁移
│   └── seed.sql          # 示例数据
└── 配置文件...
```

## 🗄️ 数据库设计

基于Android版Room数据库设计，包含以下主要表：

- `users` - 用户信息
- `medicine_reminders` - 用药提醒
- `health_data` - 健康数据
- `emergency_contacts` - 紧急联系人
- `emergency_calls` - 紧急呼叫记录
- `family_messages` - 家庭消息
- `devices` - 设备信息

## 🔐 认证系统

- 支持邮箱注册/登录
- 用户角色：患者、家属、护理员
- 会话管理和状态持久化
- 密码重置功能

## 🚧 开发进度

- [x] 项目架构设计
- [x] WEB项目初始化
- [x] 数据库设计与配置
- [x] 用户认证系统
- [ ] 核心功能模块开发
- [ ] 设备接口适配层
- [ ] 响应式UI优化
- [ ] 测试与部署

## 📝 开发说明

### 添加新功能

1. 在 `src/types/index.ts` 中定义类型
2. 在 `src/lib/` 中添加服务函数
3. 在 `src/components/` 中创建UI组件
4. 在 `src/app/` 中添加页面路由

### 数据库操作

使用Supabase客户端进行数据操作：

```typescript
import { createClient } from '@/lib/supabase'

const supabase = createClient()

// 查询数据
const { data, error } = await supabase
  .from('medicine_reminders')
  .select('*')
  .eq('user_id', userId)
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
