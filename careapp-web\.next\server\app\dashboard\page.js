/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDY2FyZWJhbyU1QyU1Q2NhcmVhcHAtd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2NhcmViYW8lNUMlNUNjYXJlYXBwLXdlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMkg7QUFDM0g7QUFDQSwwT0FBOEg7QUFDOUg7QUFDQSwwT0FBOEg7QUFDOUg7QUFDQSxvUkFBb0o7QUFDcEo7QUFDQSx3T0FBNkg7QUFDN0g7QUFDQSw0UEFBd0k7QUFDeEk7QUFDQSxrUUFBMkk7QUFDM0k7QUFDQSxzUUFBNEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNhcmViYW9cXFxcY2FyZWFwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjYXJlYmFvXFxcXGNhcmVhcHAtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNhcmViYW9cXFxcY2FyZWFwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNhcmViYW9cXFxcY2FyZWFwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjYXJlYmFvXFxcXGNhcmVhcHAtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQTRGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjYXJlYmFvXFxcXGNhcmVhcHAtd2ViXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   dynamic: () => (/* binding */ dynamic)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const dynamic = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\carebao\\careapp-web\\src\\app\\dashboard\\page.tsx",
"dynamic",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\carebao\\careapp-web\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcY2FyZWJhb1xcY2FyZWFwcC13ZWJcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY5NDBjNzdjOTA1N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: '照护宝 - 智能健康护理平台',\n    description: '专业的健康数据监测、用药提醒和紧急呼叫系统',\n    keywords: '健康监测,用药提醒,紧急呼叫,血压血糖,老人护理'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ1c7Z0JBQUlELFdBQVU7MEJBQ1pKOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkU6XFxjYXJlYmFvXFxjYXJlYXBwLXdlYlxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfnhafmiqTlrp0gLSDmmbrog73lgaXlurfmiqTnkIblubPlj7AnLFxuICBkZXNjcmlwdGlvbjogJ+S4k+S4mueahOWBpeW6t+aVsOaNruebkea1i+OAgeeUqOiNr+aPkOmGkuWSjOe0p+aApeWRvOWPq+ezu+e7nycsXG4gIGtleXdvcmRzOiAn5YGl5bq355uR5rWLLOeUqOiNr+aPkOmGkizntKfmgKXlkbzlj6ss6KGA5Y6L6KGA57OWLOiAgeS6uuaKpOeQhicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQTRGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjYXJlYmFvXFxcXGNhcmVhcHAtd2ViXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Heart,LogOut,Phone,Pill,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \n\n\n\n\n// 强制动态渲染\nconst dynamic = 'force-dynamic';\nfunction DashboardPage() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, initialized, signOut, initialize } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            setMounted(true);\n            initialize();\n        }\n    }[\"DashboardPage.useEffect\"], [\n        initialize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (mounted && initialized && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        user,\n        initialized,\n        router,\n        mounted\n    ]);\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            router.push('/auth');\n        } catch (error) {\n            console.error('登出失败:', error);\n        }\n    };\n    if (!mounted || !initialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载仪表板...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    const getRoleText = (role)=>{\n        switch(role){\n            case 'patient':\n                return '患者';\n            case 'family':\n                return '家属';\n            case 'caregiver':\n                return '护理员';\n            default:\n                return '用户';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-8 h-8 text-red-500 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"照护宝\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"欢迎回来\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold text-gray-800\",\n                                                children: [\n                                                    user.user_metadata?.name || user.email,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                        children: getRoleText(user.user_metadata?.role || 'patient')\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSignOut,\n                                                className: \"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-4\",\n                                children: \"欢迎使用照护宝 WEB 版！\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"您已成功登录系统。WEB版本正在开发中，更多功能即将上线。\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-800 text-sm\",\n                                    children: \"\\uD83D\\uDCA1 提示：当前为开发版本，部分功能可能尚未完善。我们正在努力为您提供更好的体验！\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                        children: \"用药提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"智能提醒，按时用药\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-green-100 text-green-700 py-2 px-4 rounded-lg hover:bg-green-200 transition-colors\",\n                                        children: \"即将上线\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                        children: \"健康监测\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"实时监控健康数据\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-blue-100 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-200 transition-colors\",\n                                        children: \"即将上线\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                        children: \"紧急呼叫\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"一键求助，及时救援\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-red-100 text-red-700 py-2 px-4 rounded-lg hover:bg-red-200 transition-colors\",\n                                        children: \"即将上线\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Heart_LogOut_Phone_Pill_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                        children: \"家庭互动\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-4\",\n                                        children: \"家人关爱，温暖陪伴\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-purple-100 text-purple-700 py-2 px-4 rounded-lg hover:bg-purple-200 transition-colors\",\n                                        children: \"即将上线\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-800 mb-4\",\n                                children: \"开发进度\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: \"✅ 项目架构设计\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-medium\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: \"✅ WEB项目初始化\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-medium\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: \"✅ 数据库设计\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-medium\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: \"✅ 用户认证系统\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-medium\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: \"\\uD83D\\uDD04 核心功能开发\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-medium\",\n                                                children: \"进行中\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// 检查是否配置了Supabase\nconst isSupabaseConfigured = ()=>{\n    return  false && 0;\n};\nclass AuthService {\n    // 用户注册\n    async signUp(data) {\n        // 检查Supabase配置\n        if (!isSupabaseConfigured()) {\n            // 演示模式：模拟注册成功\n            await new Promise((resolve)=>setTimeout(resolve, 1000)) // 模拟网络延迟\n            ;\n            const mockUser = {\n                id: `demo-${Date.now()}`,\n                email: data.email,\n                user_metadata: {\n                    name: data.name,\n                    phone: data.phone,\n                    role: data.role\n                },\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                aud: 'authenticated',\n                app_metadata: {},\n                role: 'authenticated'\n            };\n            // 存储到localStorage作为演示\n            localStorage.setItem('demo_user', JSON.stringify(mockUser));\n            return {\n                user: mockUser,\n                session: null\n            };\n        }\n        const { email, password, name, phone, role } = data;\n        const { data: authData, error } = await this.client.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    name,\n                    phone,\n                    role\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // 如果注册成功，创建用户档案\n        if (authData.user) {\n            const { error: profileError } = await this.client.from('users').insert({\n                id: authData.user.id,\n                email: authData.user.email,\n                name,\n                phone,\n                role\n            });\n            if (profileError) {\n                console.error('创建用户档案失败:', profileError);\n            }\n        }\n        return authData;\n    }\n    // 用户登录\n    async signIn(data) {\n        // 检查Supabase配置\n        if (!isSupabaseConfigured()) {\n            // 演示模式：检查是否有演示用户\n            await new Promise((resolve)=>setTimeout(resolve, 1000)) // 模拟网络延迟\n            ;\n            const demoUser = localStorage.getItem('demo_user');\n            if (demoUser) {\n                const user = JSON.parse(demoUser);\n                if (user.email === data.email) {\n                    return {\n                        user,\n                        session: null\n                    };\n                }\n            }\n            // 创建默认演示用户\n            const mockUser = {\n                id: `demo-${Date.now()}`,\n                email: data.email,\n                user_metadata: {\n                    name: '演示用户',\n                    role: 'patient'\n                },\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                aud: 'authenticated',\n                app_metadata: {},\n                role: 'authenticated'\n            };\n            localStorage.setItem('demo_user', JSON.stringify(mockUser));\n            return {\n                user: mockUser,\n                session: null\n            };\n        }\n        const { email, password } = data;\n        const { data: authData, error } = await this.client.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return authData;\n    }\n    // 用户登出\n    async signOut() {\n        // 检查Supabase配置\n        if (!isSupabaseConfigured()) {\n            // 演示模式：清除本地存储\n            localStorage.removeItem('demo_user');\n            return;\n        }\n        const { error } = await this.client.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    // 获取当前用户\n    async getCurrentUser() {\n        // 检查Supabase配置\n        if (!isSupabaseConfigured()) {\n            // 演示模式：从本地存储获取用户\n            const demoUser = localStorage.getItem('demo_user');\n            return demoUser ? JSON.parse(demoUser) : null;\n        }\n        const { data: { user }, error } = await this.client.auth.getUser();\n        if (error || !user) {\n            return null;\n        }\n        return user;\n    }\n    // 获取用户会话\n    async getSession() {\n        const { data: { session }, error } = await this.client.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    // 重置密码\n    async resetPassword(email) {\n        const { error } = await this.client.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    // 更新密码\n    async updatePassword(password) {\n        const { error } = await this.client.auth.updateUser({\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    // 监听认证状态变化\n    onAuthStateChange(callback) {\n        return this.client.auth.onAuthStateChange((event, session)=>{\n            callback(session?.user);\n        });\n    }\n    constructor(){\n        this.client = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase;\n    }\n}\n// 导出单例实例\nconst authService = new AuthService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// 浏览器端客户端 - 用于客户端组件\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://your-project.supabase.co\", \"your-anon-key\");\n}\n// 默认导出浏览器客户端实例\nconst supabase = createClient();\n// 数据库表名常量\nconst TABLES = {\n    USERS: 'users',\n    MEDICINE_REMINDERS: 'medicine_reminders',\n    HEALTH_DATA: 'health_data',\n    EMERGENCY_CONTACTS: 'emergency_contacts',\n    EMERGENCY_CALLS: 'emergency_calls',\n    FAMILY_MESSAGES: 'family_messages',\n    DEVICES: 'devices'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        loading: false,\n        initialized: false,\n        initialize: async ()=>{\n            try {\n                set({\n                    loading: true\n                });\n                // 获取当前用户\n                const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                set({\n                    user,\n                    initialized: true\n                });\n                // 监听认证状态变化\n                _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.onAuthStateChange((user)=>{\n                    set({\n                        user\n                    });\n                });\n            } catch (error) {\n                console.error('初始化认证状态失败:', error);\n                set({\n                    user: null,\n                    initialized: true\n                });\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        signIn: async (email, password)=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.signIn({\n                    email,\n                    password\n                });\n                // 获取更新后的用户信息\n                const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                set({\n                    user\n                });\n            } catch (error) {\n                console.error('登录失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        signUp: async (data)=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.signUp(data);\n                // 注册后自动获取用户信息\n                const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                set({\n                    user\n                });\n            } catch (error) {\n                console.error('注册失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        signOut: async ()=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.signOut();\n                set({\n                    user: null\n                });\n            } catch (error) {\n                console.error('登出失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        resetPassword: async (email)=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.resetPassword(email);\n            } catch (error) {\n                console.error('重置密码失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        }\n    }));\n// 便捷的hooks\nconst useAuth = ()=>{\n    const store = useAuthStore();\n    return {\n        user: store.user,\n        loading: store.loading,\n        initialized: store.initialized,\n        isAuthenticated: !!store.user,\n        signIn: store.signIn,\n        signUp: store.signUp,\n        signOut: store.signOut,\n        initialize: store.initialize,\n        resetPassword: store.resetPassword\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/use-sync-external-store","vendor-chunks/webidl-conversions","vendor-chunks/zustand"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();