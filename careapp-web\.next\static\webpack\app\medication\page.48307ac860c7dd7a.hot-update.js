"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/lib/audio-manager.ts":
/*!**********************************!*\
  !*** ./src/lib/audio-manager.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioManager: () => (/* binding */ AudioManager),\n/* harmony export */   audioManager: () => (/* binding */ audioManager)\n/* harmony export */ });\nclass AudioManager {\n    /**\n   * 初始化音频上下文\n   */ initializeAudioContext() {\n        if (false) {}\n        try {\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 获取预设音效列表\n   */ getAvailableSounds() {\n        return [\n            {\n                id: 'gentle',\n                name: '轻柔提醒',\n                description: '温和的铃声，适合日常提醒',\n                generator: ()=>this.generateGentleSound()\n            },\n            {\n                id: 'standard',\n                name: '标准提醒',\n                description: '清晰的提醒音，平衡音量',\n                generator: ()=>this.generateStandardSound()\n            },\n            {\n                id: 'urgent',\n                name: '紧急提醒',\n                description: '响亮的警示音，用于重要提醒',\n                generator: ()=>this.generateUrgentSound()\n            },\n            {\n                id: 'chime',\n                name: '钟声',\n                description: '悦耳的钟声效果',\n                generator: ()=>this.generateChimeSound()\n            },\n            {\n                id: 'beep',\n                name: '蜂鸣声',\n                description: '简单的蜂鸣提醒',\n                generator: ()=>this.generateBeepSound()\n            }\n        ];\n    }\n    /**\n   * 播放提醒音效\n   */ async playReminderSound() {\n        let level = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            // 根据提醒级别选择音效\n            const soundType = level === 3 ? 'urgent' : level === 2 ? 'standard' : this.settings.soundType;\n            // 停止当前播放的音频\n            this.stopCurrentAudio();\n            // 播放音效\n            await this.playSound(soundType);\n            // 触发震动\n            if (this.settings.enableVibration && level >= 2) {\n                this.triggerVibration(level);\n            }\n            // 触发闪烁效果\n            if (this.settings.enableFlash && level >= 2) {\n                this.triggerFlashEffect(level);\n            }\n        } catch (error) {\n            console.error('播放提醒音效失败:', error);\n        }\n    }\n    /**\n   * 播放指定音效\n   */ async playSound(soundType) {\n        const sound = this.getAvailableSounds().find((s)=>s.id === soundType);\n        if (sound === null || sound === void 0 ? void 0 : sound.generator) {\n            await sound.generator();\n        } else if (sound === null || sound === void 0 ? void 0 : sound.url) {\n            await this.playAudioFile(sound.url);\n        } else {\n            // 默认音效\n            await this.generateStandardSound();\n        }\n    }\n    /**\n   * 播放音频文件\n   */ async playAudioFile(url) {\n        return new Promise((resolve, reject)=>{\n            const audio = new Audio(url);\n            audio.volume = this.settings.volume;\n            audio.onended = ()=>resolve();\n            audio.onerror = ()=>reject(new Error('音频播放失败'));\n            this.currentAudio = audio;\n            audio.play().catch(reject);\n        });\n    }\n    /**\n   * 生成轻柔音效\n   */ async generateGentleSound() {\n        if (!this.audioContext) return;\n        const oscillator = this.audioContext.createOscillator();\n        const gainNode = this.audioContext.createGain();\n        oscillator.connect(gainNode);\n        gainNode.connect(this.audioContext.destination);\n        oscillator.frequency.setValueAtTime(440, this.audioContext.currentTime);\n        oscillator.frequency.exponentialRampToValueAtTime(880, this.audioContext.currentTime + 0.5);\n        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);\n        gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.3, this.audioContext.currentTime + 0.1);\n        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 1);\n        oscillator.type = 'sine';\n        oscillator.start(this.audioContext.currentTime);\n        oscillator.stop(this.audioContext.currentTime + 1);\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 1000);\n        });\n    }\n    /**\n   * 生成标准音效\n   */ async generateStandardSound() {\n        if (!this.audioContext) return;\n        const frequencies = [\n            523,\n            659,\n            784\n        ] // C5, E5, G5\n        ;\n        for(let i = 0; i < frequencies.length; i++){\n            const oscillator = this.audioContext.createOscillator();\n            const gainNode = this.audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(this.audioContext.destination);\n            oscillator.frequency.value = frequencies[i];\n            oscillator.type = 'triangle';\n            const startTime = this.audioContext.currentTime + i * 0.2;\n            const duration = 0.3;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.5, startTime + 0.05);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n        }\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 800);\n        });\n    }\n    /**\n   * 生成紧急音效\n   */ async generateUrgentSound() {\n        if (!this.audioContext) return;\n        for(let i = 0; i < 3; i++){\n            const oscillator = this.audioContext.createOscillator();\n            const gainNode = this.audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(this.audioContext.destination);\n            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + i * 0.4);\n            oscillator.frequency.setValueAtTime(1200, this.audioContext.currentTime + i * 0.4 + 0.1);\n            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + i * 0.4 + 0.2);\n            oscillator.type = 'square';\n            const startTime = this.audioContext.currentTime + i * 0.4;\n            const duration = 0.3;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.8, startTime + 0.02);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n        }\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 1500);\n        });\n    }\n    /**\n   * 生成钟声音效\n   */ async generateChimeSound() {\n        if (!this.audioContext) return;\n        const frequencies = [\n            523,\n            659,\n            784,\n            1047\n        ] // C5, E5, G5, C6\n        ;\n        frequencies.forEach((freq, index)=>{\n            const oscillator = this.audioContext.createOscillator();\n            const gainNode = this.audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(this.audioContext.destination);\n            oscillator.frequency.value = freq;\n            oscillator.type = 'sine';\n            const startTime = this.audioContext.currentTime + index * 0.3;\n            const duration = 2;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.4, startTime + 0.1);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n        });\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 2500);\n        });\n    }\n    /**\n   * 生成蜂鸣音效\n   */ async generateBeepSound() {\n        if (!this.audioContext) return;\n        for(let i = 0; i < 2; i++){\n            const oscillator = this.audioContext.createOscillator();\n            const gainNode = this.audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(this.audioContext.destination);\n            oscillator.frequency.value = 1000;\n            oscillator.type = 'square';\n            const startTime = this.audioContext.currentTime + i * 0.3;\n            const duration = 0.2;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.6, startTime + 0.01);\n            gainNode.gain.linearRampToValueAtTime(0, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n        }\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 700);\n        });\n    }\n    /**\n   * 触发震动\n   */ triggerVibration(level) {\n        if (!('vibrate' in navigator)) return;\n        const patterns = {\n            1: [\n                200\n            ],\n            2: [\n                200,\n                100,\n                200\n            ],\n            3: [\n                200,\n                100,\n                200,\n                100,\n                200\n            ]\n        };\n        navigator.vibrate(patterns[level] || patterns[1]);\n    }\n    /**\n   * 触发闪烁效果\n   */ triggerFlashEffect(level) {\n        if (typeof document === 'undefined') return;\n        const flashCount = level * 2;\n        let count = 0;\n        const flash = ()=>{\n            if (count >= flashCount) return;\n            document.body.style.backgroundColor = count % 2 === 0 ? '#ff6b6b' : '';\n            count++;\n            setTimeout(flash, 200);\n        };\n        flash();\n        // 恢复原始背景色\n        setTimeout(()=>{\n            document.body.style.backgroundColor = '';\n        }, flashCount * 200 + 100);\n    }\n    /**\n   * 停止当前音频\n   */ stopCurrentAudio() {\n        if (this.currentAudio) {\n            this.currentAudio.pause();\n            this.currentAudio.currentTime = 0;\n            this.currentAudio = null;\n        }\n    }\n    /**\n   * 更新设置\n   */ updateSettings(newSettings) {\n        this.settings = {\n            ...this.settings,\n            ...newSettings\n        };\n    }\n    /**\n   * 获取当前设置\n   */ getSettings() {\n        return {\n            ...this.settings\n        };\n    }\n    /**\n   * 测试音效\n   */ async testSound(soundType) {\n        await this.playSound(soundType);\n    }\n    constructor(){\n        this.audioContext = null;\n        this.currentAudio = null;\n        this.settings = {\n            volume: 0.7,\n            soundType: 'standard',\n            enableVibration: true,\n            enableFlash: true\n        };\n        this.initializeAudioContext();\n    }\n}\nconst audioManager = new AudioManager();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/audio-manager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/notification-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/notification-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   getNotificationService: () => (/* binding */ getNotificationService),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _speech_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n/* harmony import */ var _audio_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./audio-manager */ \"(app-pages-browser)/./src/lib/audio-manager.ts\");\n\n\nclass NotificationService {\n    /**\n   * 初始化通知权限\n   */ async initializeNotifications() {\n        if ( true && 'Notification' in window) {\n            this.notificationPermission = Notification.permission;\n            if (this.notificationPermission === 'default') {\n                this.notificationPermission = await Notification.requestPermission();\n            }\n        }\n    }\n    /**\n   * 初始化音频系统\n   */ initializeAudio() {\n        if (false) {}\n        try {\n            // 预加载提醒音效\n            this.reminderAudio = new Audio();\n            this.reminderAudio.preload = 'auto';\n            this.reminderAudio.volume = 0.7;\n            // 尝试加载多种音效格式\n            const audioSources = [\n                '/sounds/reminder.mp3',\n                '/sounds/reminder.wav',\n                '/sounds/reminder.ogg'\n            ];\n            // 使用第一个可用的音频格式\n            for (const src of audioSources){\n                this.reminderAudio.src = src;\n                break;\n            }\n            // 如果没有音频文件，创建合成音效\n            if (!this.reminderAudio.src) {\n                this.createSyntheticReminderSound();\n            }\n            this.isAudioInitialized = true;\n        } catch (error) {\n            console.warn('音频初始化失败，将使用合成音效:', error);\n            this.createSyntheticReminderSound();\n        }\n    }\n    /**\n   * 创建合成提醒音效\n   */ createSyntheticReminderSound() {\n        if (false) {}\n        try {\n            // 使用Web Audio API创建合成音效\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const createBeep = function(frequency, duration) {\n                let delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const oscillator = audioContext.createOscillator();\n                        const gainNode = audioContext.createGain();\n                        oscillator.connect(gainNode);\n                        gainNode.connect(audioContext.destination);\n                        oscillator.frequency.value = frequency;\n                        oscillator.type = 'sine';\n                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n                        oscillator.start(audioContext.currentTime);\n                        oscillator.stop(audioContext.currentTime + duration);\n                        setTimeout(resolve, duration * 1000);\n                    }, delay);\n                });\n            };\n            // 创建自定义提醒音效播放函数\n            this.playCustomReminderSound = async ()=>{\n                try {\n                    await createBeep(800, 0.2, 0) // 第一声\n                    ;\n                    await createBeep(1000, 0.2, 100) // 第二声\n                    ;\n                    await createBeep(800, 0.3, 200) // 第三声\n                    ;\n                } catch (error) {\n                    console.error('播放合成音效失败:', error);\n                }\n            };\n        } catch (error) {\n            console.warn('Web Audio API不可用:', error);\n        }\n    }\n    /**\n   * 请求通知权限\n   */ async requestNotificationPermission() {\n        if (!('Notification' in window)) {\n            return false;\n        }\n        if (Notification.permission === 'granted') {\n            return true;\n        }\n        const permission = await Notification.requestPermission();\n        this.notificationPermission = permission;\n        return permission === 'granted';\n    }\n    /**\n   * 创建用药提醒\n   */ scheduleReminder(reminder, settings, onConfirm) {\n        reminder.scheduledTimes.forEach((time)=>{\n            const scheduledDateTime = this.getNextScheduledDateTime(time);\n            const reminderId = \"\".concat(reminder.id, \"-\").concat(time);\n            // 计算第一级提醒时间\n            const firstReminderTime = new Date(scheduledDateTime.getTime() - settings.firstReminderMinutes * 60000);\n            const timeout = setTimeout(()=>{\n                this.triggerReminder(reminder, time, settings, onConfirm);\n            }, firstReminderTime.getTime() - Date.now());\n            this.reminderTimeouts.set(reminderId, timeout);\n        });\n    }\n    /**\n   * 触发提醒\n   */ async triggerReminder(reminder, scheduledTime, settings, onConfirm) {\n        const notificationId = \"\".concat(reminder.id, \"-\").concat(scheduledTime, \"-\").concat(Date.now());\n        const notification = {\n            id: notificationId,\n            reminderId: reminder.id,\n            medicineName: reminder.medicineName,\n            dosage: reminder.dosage,\n            usage: reminder.usage,\n            scheduledTime,\n            level: 1,\n            isActive: true,\n            createdAt: new Date()\n        };\n        this.activeReminders.set(notificationId, notification);\n        // 第一级提醒\n        await this.showFirstLevelReminder(notification, settings);\n        // 设置重复提醒\n        this.scheduleRepeatedReminders(notification, settings, onConfirm);\n    }\n    /**\n   * 第一级提醒：弹窗 + 声音\n   */ async showFirstLevelReminder(notification, settings) {\n        // 浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"用药提醒：\".concat(notification.medicineName), {\n                body: \"请服用 \".concat(notification.dosage),\n                icon: '/icons/medicine.png',\n                badge: '/icons/badge.png',\n                tag: notification.id,\n                requireInteraction: true,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 声音提醒（使用音效管理器）\n        if (settings.soundEnabled) {\n            await _audio_manager__WEBPACK_IMPORTED_MODULE_1__.audioManager.playReminderSound(1);\n        }\n        // 页面弹窗（通过事件通知UI组件）\n        this.notifyUI('reminder-popup', notification);\n    }\n    /**\n   * 第二级提醒：增加语音播报和强化音效\n   */ async showSecondLevelReminder(notification, settings) {\n        notification.level = 2;\n        // 浏览器通知（更紧急）\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"⚠️ 重要提醒：\".concat(notification.medicineName), {\n                body: \"请立即服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-urgent.png',\n                badge: '/icons/badge-urgent.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后提醒'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 强化音效提醒\n        if (settings.soundEnabled) {\n            await this.playIntensiveReminderSound();\n        }\n        // 语音播报\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            const speechText = _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: speechText\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 页面弹窗（更显眼的样式）\n        this.notifyUI('reminder-popup-urgent', notification);\n    }\n    /**\n   * 第三级提醒：确认是否已服药（最高级别）\n   */ async showThirdLevelReminder(notification, settings) {\n        notification.level = 3;\n        // 最高级别浏览器通知\n        if (settings.notificationEnabled && this.notificationPermission === 'granted') {\n            const browserNotification = new Notification(\"\\uD83D\\uDEA8 紧急提醒：\".concat(notification.medicineName), {\n                body: \"您可能忘记服药了！请确认是否已服用 \".concat(notification.dosage),\n                icon: '/icons/medicine-emergency.png',\n                badge: '/icons/badge-emergency.png',\n                tag: notification.id,\n                requireInteraction: true,\n                silent: false,\n                vibrate: [\n                    200,\n                    100,\n                    200\n                ],\n                actions: [\n                    {\n                        action: 'confirm',\n                        title: '已服药'\n                    },\n                    {\n                        action: 'missed',\n                        title: '忘记了'\n                    },\n                    {\n                        action: 'snooze',\n                        title: '稍后确认'\n                    }\n                ]\n            });\n            browserNotification.onclick = ()=>{\n                this.handleReminderConfirmation(notification.id, true);\n                browserNotification.close();\n            };\n        }\n        // 连续强化音效\n        if (settings.soundEnabled) {\n            await this.playIntensiveReminderSound();\n            // 间隔后再次播放\n            setTimeout(async ()=>{\n                await this.playIntensiveReminderSound();\n            }, 2000);\n        }\n        // 询问是否已服药\n        const confirmationText = \"重要提醒！您是否已经服用了\".concat(notification.medicineName, \"？如果忘记了，请立即服用。\");\n        if (settings.voiceEnabled && _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.isSpeechSynthesisSupported()) {\n            try {\n                await _speech_service__WEBPACK_IMPORTED_MODULE_0__.speechService.speak({\n                    text: confirmationText,\n                    rate: 0.9,\n                    pitch: 1.1,\n                    volume: 1.0 // 最大音量\n                });\n            } catch (error) {\n                console.error('语音播报失败:', error);\n            }\n        }\n        // 显示紧急确认对话框\n        this.notifyUI('confirmation-dialog-urgent', notification);\n        // 如果支持，尝试使页面闪烁提醒\n        this.triggerPageFlash();\n    }\n    /**\n   * 触发页面闪烁效果\n   */ triggerPageFlash() {\n        if (typeof document === 'undefined') return;\n        try {\n            const originalTitle = document.title;\n            let flashCount = 0;\n            const maxFlashes = 6;\n            const flashInterval = setInterval(()=>{\n                document.title = flashCount % 2 === 0 ? '🚨 用药提醒！' : originalTitle;\n                flashCount++;\n                if (flashCount >= maxFlashes) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                }\n            }, 500);\n            // 页面可见性变化时停止闪烁\n            const handleVisibilityChange = ()=>{\n                if (!document.hidden) {\n                    clearInterval(flashInterval);\n                    document.title = originalTitle;\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                }\n            };\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n        } catch (error) {\n            console.error('页面闪烁效果失败:', error);\n        }\n    }\n    /**\n   * 设置重复提醒\n   */ scheduleRepeatedReminders(notification, settings, onConfirm) {\n        let reminderCount = 1;\n        const maxReminders = settings.maxReminders;\n        const scheduleNext = ()=>{\n            if (!this.activeReminders.has(notification.id) || reminderCount >= maxReminders) {\n                // 达到最大提醒次数，通知监护人\n                if (reminderCount >= maxReminders) {\n                    this.notifyGuardians(notification, settings);\n                }\n                return;\n            }\n            const timeout = setTimeout(async ()=>{\n                if (!this.activeReminders.has(notification.id)) return;\n                reminderCount++;\n                if (reminderCount === 2) {\n                    await this.showSecondLevelReminder(notification, settings);\n                } else if (reminderCount >= 3) {\n                    await this.showThirdLevelReminder(notification, settings);\n                }\n                scheduleNext();\n            }, settings.reminderInterval * 60000);\n            this.reminderTimeouts.set(\"\".concat(notification.id, \"-repeat-\").concat(reminderCount), timeout);\n        };\n        scheduleNext();\n    }\n    /**\n   * 处理提醒确认\n   */ handleReminderConfirmation(notificationId, confirmed) {\n        const notification = this.activeReminders.get(notificationId);\n        if (!notification) return;\n        notification.isActive = false;\n        this.activeReminders.delete(notificationId);\n        // 清除相关的定时器\n        this.clearReminderTimeouts(notificationId);\n        // 通知UI更新\n        this.notifyUI('reminder-confirmed', {\n            notificationId,\n            confirmed\n        });\n    }\n    /**\n   * 通知监护人\n   */ async notifyGuardians(notification, settings) {\n        // 延迟通知监护人\n        setTimeout(()=>{\n            this.notifyUI('guardian-notification', {\n                notification,\n                message: \"患者可能忘记服用\".concat(notification.medicineName, \"，请及时关注。\")\n            });\n        }, settings.guardianNotificationDelay * 60000);\n    }\n    /**\n   * 播放提醒声音\n   */ async playReminderSound() {\n        try {\n            // 优先使用预加载的音频\n            if (this.reminderAudio && this.isAudioInitialized) {\n                this.reminderAudio.currentTime = 0 // 重置播放位置\n                ;\n                await this.reminderAudio.play();\n                return;\n            }\n            // 如果预加载音频不可用，使用合成音效\n            if (this.playCustomReminderSound) {\n                await this.playCustomReminderSound();\n                return;\n            }\n            // 最后的备选方案：简单的beep音效\n            this.playFallbackSound();\n        } catch (error) {\n            console.error('播放提醒声音失败:', error);\n            // 如果所有音效都失败，尝试备选方案\n            this.playFallbackSound();\n        }\n    }\n    /**\n   * 备选音效（系统beep）\n   */ playFallbackSound() {\n        try {\n            // 使用系统默认音效\n            if ('speechSynthesis' in window) {\n                const utterance = new SpeechSynthesisUtterance('');\n                utterance.volume = 0.1;\n                speechSynthesis.speak(utterance);\n            }\n        } catch (error) {\n            console.warn('备选音效也无法播放:', error);\n        }\n    }\n    /**\n   * 播放连续提醒音效（用于重要提醒）\n   */ async playIntensiveReminderSound() {\n        for(let i = 0; i < 3; i++){\n            await this.playReminderSound();\n            await new Promise((resolve)=>setTimeout(resolve, 500)) // 间隔500ms\n            ;\n        }\n    }\n    /**\n   * 通知UI组件\n   */ notifyUI(event, data) {\n        if (true) {\n            window.dispatchEvent(new CustomEvent(\"medication-\".concat(event), {\n                detail: data\n            }));\n        }\n    }\n    /**\n   * 清除提醒定时器\n   */ clearReminderTimeouts(notificationId) {\n        // 清除主定时器\n        const mainTimeout = this.reminderTimeouts.get(notificationId);\n        if (mainTimeout) {\n            clearTimeout(mainTimeout);\n            this.reminderTimeouts.delete(notificationId);\n        }\n        // 清除重复提醒定时器\n        for (const [key, timeout] of this.reminderTimeouts.entries()){\n            if (key.startsWith(\"\".concat(notificationId, \"-repeat-\"))) {\n                clearTimeout(timeout);\n                this.reminderTimeouts.delete(key);\n            }\n        }\n    }\n    /**\n   * 获取下次计划时间\n   */ getNextScheduledDateTime(time) {\n        const [hours, minutes] = time.split(':').map(Number);\n        const now = new Date();\n        const scheduled = new Date();\n        scheduled.setHours(hours, minutes, 0, 0);\n        // 如果时间已过，设置为明天\n        if (scheduled <= now) {\n            scheduled.setDate(scheduled.getDate() + 1);\n        }\n        return scheduled;\n    }\n    /**\n   * 取消所有活动提醒\n   */ cancelAllReminders() {\n        this.activeReminders.clear();\n        for (const timeout of this.reminderTimeouts.values()){\n            clearTimeout(timeout);\n        }\n        this.reminderTimeouts.clear();\n    }\n    /**\n   * 取消特定提醒\n   */ cancelReminder(reminderId) {\n        // 找到并删除相关的活动提醒\n        for (const [id, notification] of this.activeReminders.entries()){\n            if (notification.reminderId === reminderId) {\n                this.activeReminders.delete(id);\n                this.clearReminderTimeouts(id);\n            }\n        }\n    }\n    /**\n   * 获取活动提醒列表\n   */ getActiveReminders() {\n        return Array.from(this.activeReminders.values());\n    }\n    /**\n   * 检查通知权限状态\n   */ getNotificationPermission() {\n        return this.notificationPermission;\n    }\n    constructor(){\n        this.activeReminders = new Map();\n        this.reminderTimeouts = new Map();\n        this.notificationPermission = 'default';\n        this.reminderAudio = null;\n        this.isAudioInitialized = false;\n        this.playCustomReminderSound = null;\n        if (true) {\n            this.initializeNotifications();\n            this.initializeAudio();\n        }\n    }\n}\n// 延迟初始化，避免服务器端渲染问题\nlet notificationServiceInstance = null;\nconst getNotificationService = ()=>{\n    if (false) {}\n    if (!notificationServiceInstance) {\n        notificationServiceInstance = new NotificationService();\n    }\n    return notificationServiceInstance;\n};\n// 只在客户端导出实例\nconst notificationService =  true ? getNotificationService() : 0;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/notification-service.ts\n"));

/***/ })

});