/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/page";
exports.ids = ["app/auth/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(rsc)/./src/app/auth/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/page\",\n        pathname: \"/auth\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDY2FyZWJhbyU1QyU1Q2NhcmVhcHAtd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2NhcmViYW8lNUMlNUNjYXJlYXBwLXdlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMkg7QUFDM0g7QUFDQSwwT0FBOEg7QUFDOUg7QUFDQSwwT0FBOEg7QUFDOUg7QUFDQSxvUkFBb0o7QUFDcEo7QUFDQSx3T0FBNkg7QUFDN0g7QUFDQSw0UEFBd0k7QUFDeEk7QUFDQSxrUUFBMkk7QUFDM0k7QUFDQSxzUUFBNEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNhcmViYW9cXFxcY2FyZWFwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjYXJlYmFvXFxcXGNhcmVhcHAtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNhcmViYW9cXFxcY2FyZWFwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNhcmViYW9cXFxcY2FyZWFwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjYXJlYmFvXFxcXGNhcmVhcHAtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(rsc)/./src/app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUF1RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\carebao\\careapp-web\\src\\app\\auth\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcY2FyZWJhb1xcY2FyZWFwcC13ZWJcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY5NDBjNzdjOTA1N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: '照护宝 - 智能健康护理平台',\n    description: '专业的健康数据监测、用药提醒和紧急呼叫系统',\n    keywords: '健康监测,用药提醒,紧急呼叫,血压血糖,老人护理'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ1c7Z0JBQUlELFdBQVU7MEJBQ1pKOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkU6XFxjYXJlYmFvXFxjYXJlYXBwLXdlYlxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfnhafmiqTlrp0gLSDmmbrog73lgaXlurfmiqTnkIblubPlj7AnLFxuICBkZXNjcmlwdGlvbjogJ+S4k+S4mueahOWBpeW6t+aVsOaNruebkea1i+OAgeeUqOiNr+aPkOmGkuWSjOe0p+aApeWRvOWPq+ezu+e7nycsXG4gIGtleXdvcmRzOiAn5YGl5bq355uR5rWLLOeUqOiNr+aPkOmGkizntKfmgKXlkbzlj6ss6KGA5Y6L6KGA57OWLOiAgeS6uuaKpOeQhicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(ssr)/./src/app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNjYXJlYmFvJTVDJTVDY2FyZWFwcC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUF1RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcY2FyZWJhb1xcXFxjYXJlYXBwLXdlYlxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ccarebao%5C%5Ccareapp-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _components_auth_SignInForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/SignInForm */ \"(ssr)/./src/components/auth/SignInForm.tsx\");\n/* harmony import */ var _components_auth_SignUpForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/SignUpForm */ \"(ssr)/./src/components/auth/SignUpForm.tsx\");\n/* harmony import */ var _barrel_optimize_names_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AuthPage() {\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const { user, initialized } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthPage.useEffect\": ()=>{\n            if (initialized && user) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AuthPage.useEffect\"], [\n        user,\n        initialized,\n        router\n    ]);\n    const handleAuthSuccess = ()=>{\n        router.push('/dashboard');\n    };\n    if (!initialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在加载...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-12 h-12 text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-800\",\n                                    children: \"照护宝\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600\",\n                            children: \"智能健康护理平台\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: mode === 'signin' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_SignInForm__WEBPACK_IMPORTED_MODULE_4__.SignInForm, {\n                        onSuccess: handleAuthSuccess,\n                        onSwitchToSignUp: ()=>setMode('signup')\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_SignUpForm__WEBPACK_IMPORTED_MODULE_5__.SignUpForm, {\n                        onSuccess: handleAuthSuccess,\n                        onSwitchToSignIn: ()=>setMode('signin')\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-center text-gray-800 mb-8\",\n                            children: \"为什么选择照护宝？\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xl\",\n                                                children: \"\\uD83D\\uDC8A\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-800 mb-2\",\n                                            children: \"智能用药提醒\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"多条件触发，确保用药安全\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xl\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-800 mb-2\",\n                                            children: \"健康数据监测\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"实时监控血压血糖等指标\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xl\",\n                                                children: \"\\uD83D\\uDEA8\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-800 mb-2\",\n                                            children: \"紧急呼叫系统\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"一键呼叫，及时救援\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xl\",\n                                                children: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-800 mb-2\",\n                                            children: \"家庭互动\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"让家人时刻关注健康\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\app\\\\auth\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/SignInForm.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/SignInForm.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignInForm: () => (/* binding */ SignInForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.mjs\");\n/* __next_internal_client_entry_do_not_use__ SignInForm auto */ \n\n\n\nfunction SignInForm({ onSuccess, onSwitchToSignUp }) {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { signIn, loading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (!email || !password) {\n            setError('请填写所有必填字段');\n            return;\n        }\n        try {\n            await signIn(email, password);\n            onSuccess?.();\n        } catch (error) {\n            setError(error instanceof Error ? error.message : '登录失败');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-md mx-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-800 mb-2\",\n                            children: \"欢迎回来\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"登录您的照护宝账户\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"邮箱地址\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            type: \"email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请输入邮箱地址\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"密码\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            type: showPassword ? 'text' : 'password',\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请输入密码\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"登录\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"还没有账户？\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSwitchToSignUp,\n                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                children: \"立即注册\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/SignInForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/SignUpForm.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/SignUpForm.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignUpForm: () => (/* binding */ SignUpForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.mjs\");\n/* __next_internal_client_entry_do_not_use__ SignUpForm auto */ \n\n\n\nfunction SignUpForm({ onSuccess, onSwitchToSignIn }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        confirmPassword: '',\n        name: '',\n        phone: '',\n        role: 'patient'\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { signUp, loading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        // 验证表单\n        if (!formData.email || !formData.password || !formData.name) {\n            setError('请填写所有必填字段');\n            return;\n        }\n        if (formData.password !== formData.confirmPassword) {\n            setError('两次输入的密码不一致');\n            return;\n        }\n        if (formData.password.length < 6) {\n            setError('密码长度至少6位');\n            return;\n        }\n        try {\n            await signUp({\n                email: formData.email,\n                password: formData.password,\n                name: formData.name,\n                phone: formData.phone || undefined,\n                role: formData.role\n            });\n            onSuccess?.();\n        } catch (error) {\n            setError(error instanceof Error ? error.message : '注册失败');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-md mx-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-800 mb-2\",\n                            children: \"创建账户\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"加入照护宝，开始智能健康管理\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"name\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"姓名 *\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"name\",\n                                            name: \"name\",\n                                            type: \"text\",\n                                            value: formData.name,\n                                            onChange: handleChange,\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请输入您的姓名\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"邮箱地址 *\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请输入邮箱地址\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"phone\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"手机号码\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"phone\",\n                                            name: \"phone\",\n                                            type: \"tel\",\n                                            value: formData.phone,\n                                            onChange: handleChange,\n                                            className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请输入手机号码（可选）\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"role\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"用户角色 *\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"role\",\n                                    name: \"role\",\n                                    value: formData.role,\n                                    onChange: handleChange,\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"patient\",\n                                            children: \"患者/长辈\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"family\",\n                                            children: \"家属\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"caregiver\",\n                                            children: \"护理员\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"密码 *\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: showPassword ? 'text' : 'password',\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            className: \"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请输入密码（至少6位）\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"确认密码 *\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: showConfirmPassword ? 'text' : 'password',\n                                            value: formData.confirmPassword,\n                                            onChange: handleChange,\n                                            className: \"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"请再次输入密码\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 40\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 73\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"注册账户\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"已有账户？\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSwitchToSignIn,\n                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                children: \"立即登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/SignUpForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass AuthService {\n    // 用户注册\n    async signUp(data) {\n        const { email, password, name, phone, role } = data;\n        const { data: authData, error } = await this.client.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    name,\n                    phone,\n                    role\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // 如果注册成功，创建用户档案\n        if (authData.user) {\n            const { error: profileError } = await this.client.from('users').insert({\n                id: authData.user.id,\n                email: authData.user.email,\n                name,\n                phone,\n                role\n            });\n            if (profileError) {\n                console.error('创建用户档案失败:', profileError);\n            }\n        }\n        return authData;\n    }\n    // 用户登录\n    async signIn(data) {\n        const { email, password } = data;\n        const { data: authData, error } = await this.client.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return authData;\n    }\n    // 用户登出\n    async signOut() {\n        const { error } = await this.client.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    // 获取当前用户\n    async getCurrentUser() {\n        const { data: { user }, error } = await this.client.auth.getUser();\n        if (error || !user) {\n            return null;\n        }\n        return user;\n    }\n    // 获取用户会话\n    async getSession() {\n        const { data: { session }, error } = await this.client.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    // 重置密码\n    async resetPassword(email) {\n        const { error } = await this.client.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    // 更新密码\n    async updatePassword(password) {\n        const { error } = await this.client.auth.updateUser({\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    // 监听认证状态变化\n    onAuthStateChange(callback) {\n        return this.client.auth.onAuthStateChange((event, session)=>{\n            callback(session?.user);\n        });\n    }\n    constructor(){\n        this.client = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase;\n    }\n}\n// 导出单例实例\nconst authService = new AuthService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// 浏览器端客户端 - 用于客户端组件\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://your-project.supabase.co\", \"your-anon-key\");\n}\n// 默认导出浏览器客户端实例\nconst supabase = createClient();\n// 数据库表名常量\nconst TABLES = {\n    USERS: 'users',\n    MEDICINE_REMINDERS: 'medicine_reminders',\n    HEALTH_DATA: 'health_data',\n    EMERGENCY_CONTACTS: 'emergency_contacts',\n    EMERGENCY_CALLS: 'emergency_calls',\n    FAMILY_MESSAGES: 'family_messages',\n    DEVICES: 'devices'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        loading: false,\n        initialized: false,\n        initialize: async ()=>{\n            try {\n                set({\n                    loading: true\n                });\n                // 获取当前用户\n                const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                set({\n                    user,\n                    initialized: true\n                });\n                // 监听认证状态变化\n                _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.onAuthStateChange((user)=>{\n                    set({\n                        user\n                    });\n                });\n            } catch (error) {\n                console.error('初始化认证状态失败:', error);\n                set({\n                    user: null,\n                    initialized: true\n                });\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        signIn: async (email, password)=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.signIn({\n                    email,\n                    password\n                });\n                // 获取更新后的用户信息\n                const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                set({\n                    user\n                });\n            } catch (error) {\n                console.error('登录失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        signUp: async (data)=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.signUp(data);\n                // 注册后自动获取用户信息\n                const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.getCurrentUser();\n                set({\n                    user\n                });\n            } catch (error) {\n                console.error('注册失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        signOut: async ()=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.signOut();\n                set({\n                    user: null\n                });\n            } catch (error) {\n                console.error('登出失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        resetPassword: async (email)=>{\n            try {\n                set({\n                    loading: true\n                });\n                await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.authService.resetPassword(email);\n            } catch (error) {\n                console.error('重置密码失败:', error);\n                throw error;\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        }\n    }));\n// 便捷的hooks\nconst useAuth = ()=>{\n    const store = useAuthStore();\n    return {\n        user: store.user,\n        loading: store.loading,\n        initialized: store.initialized,\n        isAuthenticated: !!store.user,\n        signIn: store.signIn,\n        signUp: store.signUp,\n        signOut: store.signOut,\n        initialize: store.initialize,\n        resetPassword: store.resetPassword\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/tr46","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=E%3A%5Ccarebao%5Ccareapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccarebao%5Ccareapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();