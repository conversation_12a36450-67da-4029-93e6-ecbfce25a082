'use client'

import { useState, useEffect } from 'react'
import { Clock, Sun, Coffee, Utensils, Moon, Save } from 'lucide-react'
import { useMedication } from '@/store/medication'

interface DailyScheduleFormProps {
  userId: string
  onSuccess?: () => void
}

export function DailyScheduleForm({ userId, onSuccess }: DailyScheduleFormProps) {
  const [formData, setFormData] = useState({
    wakeUpTime: '07:00',
    breakfastTime: '08:00',
    lunchTime: '12:00',
    dinnerTime: '18:00',
    bedTime: '22:00'
  })

  const { 
    dailySchedule, 
    saveDailySchedule, 
    loadDailySchedule, 
    loading, 
    error, 
    clearError 
  } = useMedication()

  useEffect(() => {
    loadDailySchedule(userId)
  }, [userId, loadDailySchedule])

  useEffect(() => {
    if (dailySchedule) {
      setFormData({
        wakeUpTime: dailySchedule.wakeUpTime,
        breakfastTime: dailySchedule.breakfastTime,
        lunchTime: dailySchedule.lunchTime,
        dinnerTime: dailySchedule.dinnerTime,
        bedTime: dailySchedule.bedTime
      })
    }
  }, [dailySchedule])

  const handleTimeChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    clearError()
  }

  const validateTimes = (): boolean => {
    const times = [
      { name: '起床时间', time: formData.wakeUpTime },
      { name: '早餐时间', time: formData.breakfastTime },
      { name: '午餐时间', time: formData.lunchTime },
      { name: '晚餐时间', time: formData.dinnerTime },
      { name: '就寝时间', time: formData.bedTime }
    ]

    // 转换为分钟数进行比较
    const timeInMinutes = times.map(t => {
      const [hours, minutes] = t.time.split(':').map(Number)
      return { ...t, minutes: hours * 60 + minutes }
    })

    // 检查时间顺序
    for (let i = 0; i < timeInMinutes.length - 1; i++) {
      if (timeInMinutes[i].minutes >= timeInMinutes[i + 1].minutes) {
        alert(`${timeInMinutes[i].name}不能晚于或等于${timeInMinutes[i + 1].name}`)
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateTimes()) return

    try {
      await saveDailySchedule({
        userId,
        wakeUpTime: formData.wakeUpTime,
        breakfastTime: formData.breakfastTime,
        lunchTime: formData.lunchTime,
        dinnerTime: formData.dinnerTime,
        bedTime: formData.bedTime
      })

      onSuccess?.()
    } catch (error) {
      console.error('保存作息时间失败:', error)
    }
  }

  const timeSlots = [
    {
      key: 'wakeUpTime',
      label: '起床时间',
      icon: Sun,
      color: 'text-yellow-500',
      description: '每天起床的时间'
    },
    {
      key: 'breakfastTime',
      label: '早餐时间',
      icon: Coffee,
      color: 'text-orange-500',
      description: '早餐用餐时间'
    },
    {
      key: 'lunchTime',
      label: '午餐时间',
      icon: Utensils,
      color: 'text-green-500',
      description: '午餐用餐时间'
    },
    {
      key: 'dinnerTime',
      label: '晚餐时间',
      icon: Utensils,
      color: 'text-blue-500',
      description: '晚餐用餐时间'
    },
    {
      key: 'bedTime',
      label: '就寝时间',
      icon: Moon,
      color: 'text-purple-500',
      description: '每天睡觉的时间'
    }
  ]

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center mb-6">
        <Clock className="w-6 h-6 text-blue-500 mr-3" />
        <h2 className="text-xl font-semibold text-gray-800">个人作息时间配置</h2>
      </div>

      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-blue-800 text-sm">
          💡 设置您的日常作息时间，系统将根据这些时间自动计算最佳的用药时间。
        </p>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {timeSlots.map(slot => {
          const IconComponent = slot.icon
          return (
            <div key={slot.key} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex-shrink-0">
                <IconComponent className={`w-6 h-6 ${slot.color}`} />
              </div>
              
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {slot.label}
                </label>
                <p className="text-xs text-gray-500">{slot.description}</p>
              </div>
              
              <div className="flex-shrink-0">
                <input
                  type="time"
                  value={formData[slot.key as keyof typeof formData]}
                  onChange={(e) => handleTimeChange(slot.key, e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-mono"
                  required
                />
              </div>
            </div>
          )
        })}

        <div className="pt-4 border-t border-gray-200">
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <>
                <Save className="w-5 h-5 mr-2" />
                保存作息时间
              </>
            )}
          </button>
        </div>
      </form>

      {/* 时间预览 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-sm font-medium text-gray-800 mb-3">时间安排预览</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
          {timeSlots.map(slot => {
            const IconComponent = slot.icon
            return (
              <div key={slot.key} className="text-center">
                <IconComponent className={`w-4 h-4 ${slot.color} mx-auto mb-1`} />
                <div className="text-xs text-gray-600">{slot.label}</div>
                <div className="text-sm font-mono font-semibold text-gray-800">
                  {formData[slot.key as keyof typeof formData]}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 用药时间计算示例 */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="text-sm font-medium text-green-800 mb-2">用药时间计算示例</h3>
        <div className="text-sm text-green-700 space-y-1">
          <div>• 餐前30分钟：早餐前 {
            (() => {
              const [h, m] = formData.breakfastTime.split(':').map(Number)
              const time = new Date()
              time.setHours(h, m - 30)
              return time.toTimeString().slice(0, 5)
            })()
          }</div>
          <div>• 餐后1小时：午餐后 {
            (() => {
              const [h, m] = formData.lunchTime.split(':').map(Number)
              const time = new Date()
              time.setHours(h, m + 60)
              return time.toTimeString().slice(0, 5)
            })()
          }</div>
          <div>• 睡前30分钟：{
            (() => {
              const [h, m] = formData.bedTime.split(':').map(Number)
              const time = new Date()
              time.setHours(h, m - 30)
              return time.toTimeString().slice(0, 5)
            })()
          }</div>
        </div>
      </div>
    </div>
  )
}
