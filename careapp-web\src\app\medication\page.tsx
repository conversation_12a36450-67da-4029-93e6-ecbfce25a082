'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/store/auth'
import { useMedication } from '@/store/medication'
import { notificationService } from '@/lib/notification-service'
import { Plus, Setting<PERSON>, Clock, Pill, Bell, BellOff } from 'lucide-react'
import { MedicineInputForm } from '@/components/medication/MedicineInputForm'
import { DailyScheduleForm } from '@/components/medication/DailyScheduleForm'
import { MedicineReminderList } from '@/components/medication/MedicineReminderList'
import { ReminderPopup, ConfirmationDialog } from '@/components/medication/ReminderPopup'
import type { ReminderNotification } from '@/lib/notification-service'
import type { MedicineReminder } from '@/types'

// 强制动态渲染
export const dynamic = 'force-dynamic'

export default function MedicationPage() {
  const [activeTab, setActiveTab] = useState<'list' | 'add' | 'schedule' | 'settings'>('list')
  const [showReminderPopup, setShowReminderPopup] = useState<ReminderNotification | null>(null)
  const [showConfirmationDialog, setShowConfirmationDialog] = useState<ReminderNotification | null>(null)
  const [reminderSystemActive, setReminderSystemActive] = useState(false)
  const [editingReminder, setEditingReminder] = useState<MedicineReminder | null>(null)
  const [mounted, setMounted] = useState(false)

  const { user, initialized, initialize } = useAuth()
  const { 
    reminders, 
    dailySchedule, 
    reminderSettings,
    startReminderSystem, 
    stopReminderSystem,
    loadDailySchedule,
    loadReminderSettings
  } = useMedication()
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
    initialize()
  }, [initialize])

  useEffect(() => {
    if (mounted && initialized && !user) {
      router.push('/auth')
    }
  }, [user, initialized, router, mounted])

  useEffect(() => {
    if (user) {
      loadDailySchedule(user.id)
      loadReminderSettings(user.id)
    }
  }, [user, loadDailySchedule, loadReminderSettings])

  // 监听提醒事件
  useEffect(() => {
    const handleReminderPopup = (event: CustomEvent) => {
      setShowReminderPopup(event.detail)
    }

    const handleConfirmationDialog = (event: CustomEvent) => {
      setShowConfirmationDialog(event.detail)
    }

    const handleReminderConfirmed = (event: CustomEvent) => {
      setShowReminderPopup(null)
      setShowConfirmationDialog(null)
    }

    window.addEventListener('medication-reminder-popup', handleReminderPopup as EventListener)
    window.addEventListener('medication-confirmation-dialog', handleConfirmationDialog as EventListener)
    window.addEventListener('medication-reminder-confirmed', handleReminderConfirmed as EventListener)

    return () => {
      window.removeEventListener('medication-reminder-popup', handleReminderPopup as EventListener)
      window.removeEventListener('medication-confirmation-dialog', handleConfirmationDialog as EventListener)
      window.removeEventListener('medication-reminder-confirmed', handleReminderConfirmed as EventListener)
    }
  }, [])

  const handleStartReminderSystem = async () => {
    if (!user) return

    try {
      // 请求通知权限
      const hasPermission = await notificationService.requestNotificationPermission()
      if (!hasPermission) {
        alert('需要通知权限才能启用提醒系统')
        return
      }

      await startReminderSystem(user.id)
      setReminderSystemActive(true)
    } catch (error) {
      console.error('启动提醒系统失败:', error)
      alert('启动提醒系统失败，请检查设置')
    }
  }

  const handleStopReminderSystem = () => {
    stopReminderSystem()
    setReminderSystemActive(false)
  }

  const handleReminderConfirm = (confirmed: boolean) => {
    if (showReminderPopup && user) {
      // 这里可以记录用药状态
      console.log(`用药确认: ${showReminderPopup.medicineName} - ${confirmed ? '已服药' : '未服药'}`)
    }
    setShowReminderPopup(null)
  }

  const handleConfirmationSubmit = (confirmed: boolean) => {
    if (showConfirmationDialog && user) {
      // 记录用药状态
      console.log(`用药确认: ${showConfirmationDialog.medicineName} - ${confirmed ? '已服药' : '错过'}`)
    }
    setShowConfirmationDialog(null)
  }

  if (!mounted || !initialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600">正在加载用药提醒系统...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const tabs = [
    { id: 'list', label: '提醒列表', icon: Pill },
    { id: 'add', label: '添加提醒', icon: Plus },
    { id: 'schedule', label: '作息设置', icon: Clock },
    { id: 'settings', label: '提醒设置', icon: Settings }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 头部 */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Pill className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-800">用药提醒系统</h1>
                <p className="text-sm text-gray-600">智能用药管理，健康生活助手</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* 提醒系统状态 */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">提醒系统</span>
                <button
                  onClick={reminderSystemActive ? handleStopReminderSystem : handleStartReminderSystem}
                  className={`flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    reminderSystemActive
                      ? 'bg-green-100 text-green-700 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {reminderSystemActive ? (
                    <>
                      <Bell className="w-4 h-4 mr-1" />
                      已启用
                    </>
                  ) : (
                    <>
                      <BellOff className="w-4 h-4 mr-1" />
                      已禁用
                    </>
                  )}
                </button>
              </div>

              {/* 返回按钮 */}
              <button
                onClick={() => router.push('/dashboard')}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              >
                返回主页
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex space-x-8">
            {tabs.map(tab => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center px-4 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <IconComponent className="w-5 h-5 mr-2" />
                  {tab.label}
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        {activeTab === 'list' && (
          <MedicineReminderList 
            userId={user.id} 
            onEdit={(reminder) => {
              setEditingReminder(reminder)
              setActiveTab('add')
            }}
          />
        )}

        {activeTab === 'add' && (
          <MedicineInputForm
            userId={user.id}
            onSuccess={() => {
              setActiveTab('list')
              setEditingReminder(null)
            }}
            onCancel={() => {
              setActiveTab('list')
              setEditingReminder(null)
            }}
          />
        )}

        {activeTab === 'schedule' && (
          <DailyScheduleForm
            userId={user.id}
            onSuccess={() => {
              // 可以选择切换到其他标签页或显示成功消息
            }}
          />
        )}

        {activeTab === 'settings' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">提醒设置</h2>
            <p className="text-gray-600">提醒设置功能开发中...</p>
          </div>
        )}
      </div>

      {/* 提醒弹窗 */}
      {showReminderPopup && (
        <ReminderPopup
          notification={showReminderPopup}
          onConfirm={handleReminderConfirm}
          onClose={() => setShowReminderPopup(null)}
        />
      )}

      {/* 确认对话框 */}
      {showConfirmationDialog && (
        <ConfirmationDialog
          notification={showConfirmationDialog}
          onConfirm={handleConfirmationSubmit}
          onClose={() => setShowConfirmationDialog(null)}
        />
      )}
    </div>
  )
}
