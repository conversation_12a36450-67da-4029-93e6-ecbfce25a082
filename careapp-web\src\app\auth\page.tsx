'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/store/auth'
import { SignInForm } from '@/components/auth/SignInForm'
import { SignUpForm } from '@/components/auth/SignUpForm'
import { Heart } from 'lucide-react'

export default function AuthPage() {
  const [mode, setMode] = useState<'signin' | 'signup'>('signin')
  const { user, initialized } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (initialized && user) {
      router.push('/dashboard')
    }
  }, [user, initialized, router])

  const handleAuthSuccess = () => {
    router.push('/dashboard')
  }

  if (!initialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Heart className="w-12 h-12 text-red-500 mr-3" />
            <h1 className="text-4xl font-bold text-gray-800">照护宝</h1>
          </div>
          <p className="text-xl text-gray-600">智能健康护理平台</p>
        </div>

        {/* Auth Forms */}
        <div className="flex justify-center">
          {mode === 'signin' ? (
            <SignInForm
              onSuccess={handleAuthSuccess}
              onSwitchToSignUp={() => setMode('signup')}
            />
          ) : (
            <SignUpForm
              onSuccess={handleAuthSuccess}
              onSwitchToSignIn={() => setMode('signin')}
            />
          )}
        </div>

        {/* Features Preview */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-8">
            为什么选择照护宝？
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">💊</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">智能用药提醒</h3>
              <p className="text-gray-600 text-sm">多条件触发，确保用药安全</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📊</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">健康数据监测</h3>
              <p className="text-gray-600 text-sm">实时监控血压血糖等指标</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🚨</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">紧急呼叫系统</h3>
              <p className="text-gray-600 text-sm">一键呼叫，及时救援</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">👨‍👩‍👧‍👦</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">家庭互动</h3>
              <p className="text-gray-600 text-sm">让家人时刻关注健康</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
