# 照护宝桌面快捷方式使用指南

## 🎯 快捷方式已创建成功！

您的桌面现在应该有以下快捷方式：

### 📱 桌面快捷方式

| 快捷方式名称 | 功能描述 | 访问地址 |
|-------------|----------|----------|
| **CareApp - Home** | 照护宝主页，查看所有功能 | http://localhost:3000 |
| **CareApp - Medication** | 用药提醒系统，智能用药管理 | http://localhost:3000/medication |
| **CareApp - Login** | 用户登录注册页面 | http://localhost:3000/auth-demo |

## 🚀 如何使用

### 第一步：启动开发服务器

**方法一：使用启动脚本（推荐）**
- 双击 `start-server.bat` 文件
- 等待服务器启动完成

**方法二：手动启动**
- 打开命令行
- 进入项目目录
- 运行 `npm run dev`

### 第二步：访问系统

- 双击桌面上的任意快捷方式
- 系统将在默认浏览器中打开

## 💡 使用技巧

### ✅ 推荐使用流程

1. **首次使用**
   - 双击 `start-server.bat` 启动服务器
   - 双击 `CareApp - Home` 进入主页
   - 点击"认证演示"进行注册/登录

2. **日常使用**
   - 确保服务器正在运行
   - 直接双击 `CareApp - Medication` 使用用药提醒
   - 或双击 `CareApp - Home` 查看所有功能

### 🔧 服务器管理

**启动服务器**
```bash
# 方法一：双击脚本
start-server.bat

# 方法二：命令行
npm run dev
```

**停止服务器**
- 在服务器窗口按 `Ctrl + C`
- 或直接关闭命令行窗口

### 📋 功能特色

- **一键访问**：桌面快捷方式直达功能页面
- **智能提醒**：用药提醒系统支持语音输入
- **演示模式**：无需配置数据库即可体验
- **响应式设计**：适配桌面和移动设备

## ⚠️ 注意事项

### 必要条件

- ✅ Node.js 已安装
- ✅ 开发服务器正在运行
- ✅ 3000端口未被占用
- ✅ 现代浏览器（Chrome、Edge、Firefox）

### 常见问题

**Q: 点击快捷方式无法打开？**
A: 请确保开发服务器正在运行，双击 `start-server.bat` 启动

**Q: 页面显示错误？**
A: 刷新浏览器页面，或重启开发服务器

**Q: 如何重新创建快捷方式？**
A: 运行 `create-shortcuts.ps1` 脚本

**Q: 端口被占用怎么办？**
A: 检查其他程序是否占用3000端口，或修改配置使用其他端口

## 🎉 开始体验

现在您可以：

1. **体验用药提醒系统**
   - 双击 `CareApp - Medication`
   - 设置个人作息时间
   - 添加用药提醒（支持语音输入）
   - 体验智能时间计算

2. **测试用户认证**
   - 双击 `CareApp - Login`
   - 注册新账户或登录
   - 体验演示模式功能

3. **探索更多功能**
   - 双击 `CareApp - Home`
   - 查看系统概览
   - 访问各个功能模块

---

**享受便捷的照护宝体验！** 🎉

如有问题，请查看项目文档或联系技术支持。
