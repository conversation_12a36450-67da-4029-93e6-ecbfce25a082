@echo off
chcp 65001 >nul
title 照护宝开发服务器

echo.
echo ========================================
echo              照护宝开发服务器
echo ========================================
echo.

echo 正在启动照护宝开发服务器...
echo 服务器地址: http://localhost:3000
echo.
echo 💡 提示：
echo    - 服务器启动后，可通过桌面快捷方式快速访问
echo    - 按 Ctrl+C 可停止服务器
echo    - 修改代码后会自动重新加载
echo.

REM 检查是否安装了依赖
if not exist "node_modules" (
    echo ⚠️  检测到未安装依赖，正在安装...
    echo.
    npm install
    echo.
)

REM 启动开发服务器
echo 🚀 启动中...
echo.
npm run dev

echo.
echo 服务器已停止运行
pause
