"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/medication/page",{

/***/ "(app-pages-browser)/./src/components/medication/ReminderPopup.tsx":
/*!*****************************************************!*\
  !*** ./src/components/medication/ReminderPopup.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfirmationDialog: () => (/* binding */ ConfirmationDialog),\n/* harmony export */   ReminderPopup: () => (/* binding */ ReminderPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Pill,Timer,Volume2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pill.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Pill,Timer,Volume2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Pill,Timer,Volume2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Pill,Timer,Volume2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Pill,Timer,Volume2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Pill,Timer,Volume2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.mjs\");\n/* harmony import */ var _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/speech-service */ \"(app-pages-browser)/./src/lib/speech-service.ts\");\n/* __next_internal_client_entry_do_not_use__ ReminderPopup,ConfirmationDialog auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nfunction ReminderPopup(param) {\n    let { notification, onConfirm, onClose } = param;\n    _s();\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(20) // 20秒自动关闭\n    ;\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReminderPopup.useEffect\": ()=>{\n            // 倒计时\n            const timer = setInterval({\n                \"ReminderPopup.useEffect.timer\": ()=>{\n                    setTimeLeft({\n                        \"ReminderPopup.useEffect.timer\": (prev)=>{\n                            if (prev <= 1) {\n                                clearInterval(timer);\n                                onClose() // 自动关闭\n                                ;\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"ReminderPopup.useEffect.timer\"]);\n                }\n            }[\"ReminderPopup.useEffect.timer\"], 1000);\n            return ({\n                \"ReminderPopup.useEffect\": ()=>clearInterval(timer)\n            })[\"ReminderPopup.useEffect\"];\n        }\n    }[\"ReminderPopup.useEffect\"], [\n        onClose\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReminderPopup.useEffect\": ()=>{\n            // 播放语音提醒\n            if (notification.level >= 2) {\n                playVoiceReminder();\n            }\n        }\n    }[\"ReminderPopup.useEffect\"], [\n        notification\n    ]);\n    const playVoiceReminder = async ()=>{\n        if (!_lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.isSpeechSynthesisSupported()) return;\n        setIsPlaying(true);\n        try {\n            const speechText = _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.generateReminderSpeech(notification.medicineName, notification.dosage, notification.usage);\n            await _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.speak({\n                text: speechText\n            });\n        } catch (error) {\n            console.error('语音播报失败:', error);\n        } finally{\n            setIsPlaying(false);\n        }\n    };\n    const handleConfirm = (confirmed)=>{\n        onConfirm(confirmed);\n        onClose();\n    };\n    const getLevelColor = ()=>{\n        switch(notification.level){\n            case 1:\n                return 'bg-blue-500';\n            case 2:\n                return 'bg-orange-500';\n            case 3:\n                return 'bg-red-500';\n            default:\n                return 'bg-blue-500';\n        }\n    };\n    const getLevelText = ()=>{\n        switch(notification.level){\n            case 1:\n                return '第一次提醒';\n            case 2:\n                return '重要提醒';\n            case 3:\n                return '紧急提醒';\n            default:\n                return '用药提醒';\n        }\n    };\n    const getAnimationClass = ()=>{\n        switch(notification.level){\n            case 1:\n                return 'animate-bounce';\n            case 2:\n                return 'animate-pulse';\n            case 3:\n                return 'animate-ping';\n            default:\n                return 'animate-bounce';\n        }\n    };\n    const getBackgroundClass = ()=>{\n        switch(notification.level){\n            case 1:\n                return 'bg-black bg-opacity-50';\n            case 2:\n                return 'bg-orange-900 bg-opacity-60';\n            case 3:\n                return 'bg-red-900 bg-opacity-70';\n            default:\n                return 'bg-black bg-opacity-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 \".concat(getBackgroundClass(), \" flex items-center justify-center z-50 p-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-2xl max-w-md w-full \".concat(getAnimationClass()),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(getLevelColor(), \" text-white p-4 rounded-t-lg\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: getLevelText()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm bg-white bg-opacity-20 px-2 py-1 rounded\",\n                                        children: [\n                                            timeLeft,\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"text-white hover:bg-white hover:bg-opacity-20 p-1 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                    children: notification.medicineName\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"剂量：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 18\n                                                }, this),\n                                                notification.dosage\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        notification.usage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"用法：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 20\n                                                }, this),\n                                                notification.usage\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        \"计划时间：\",\n                                                        notification.scheduledTime\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        _lib_speech_service__WEBPACK_IMPORTED_MODULE_2__.speechService.isSpeechSynthesisSupported() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: playVoiceReminder,\n                                disabled: isPlaying,\n                                className: \"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this),\n                                    isPlaying ? '播放中...' : '重新播放'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>handleConfirm(true),\n                                    className: \"w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 flex items-center justify-center font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"已服药\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>handleConfirm(false),\n                                            className: \"bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"暂不服药\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onClose,\n                                            className: \"bg-blue-100 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-200 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"稍后提醒\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        notification.level >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-800 text-sm text-center\",\n                                children: notification.level === 2 ? '⚠️ 这是第二次提醒，请及时服药' : '🚨 多次提醒未响应，请确认是否已服药'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-1 bg-gray-200 rounded-b-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full \".concat(getLevelColor(), \" transition-all duration-1000 ease-linear\"),\n                        style: {\n                            width: \"\".concat(timeLeft / 20 * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(ReminderPopup, \"5PW83Dcb8H3ldUiCv3l/G7QT9aU=\");\n_c = ReminderPopup;\nfunction ConfirmationDialog(param) {\n    let { notification, onConfirm, onClose } = param;\n    _s1();\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSubmit = ()=>{\n        if (selectedOption) {\n            onConfirm(selectedOption === 'taken');\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-2xl max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-8 h-8 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"用药确认\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"您是否已经服用了 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: notification.medicineName\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 24\n                                    }, this),\n                                    \"？\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setSelectedOption('taken'),\n                                className: \"w-full p-3 border-2 rounded-lg text-left transition-colors \".concat(selectedOption === 'taken' ? 'border-green-500 bg-green-50 text-green-700' : 'border-gray-200 hover:border-gray-300'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: \"是的，已经服药\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"我已按时服用了药物\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setSelectedOption('missed'),\n                                className: \"w-full p-3 border-2 rounded-lg text-left transition-colors \".concat(selectedOption === 'missed' ? 'border-red-500 bg-red-50 text-red-700' : 'border-gray-200 hover:border-gray-300'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Pill_Timer_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: \"没有，忘记服药\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"我错过了这次用药时间\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClose,\n                                className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                disabled: !selectedOption,\n                                className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"确认\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\carebao\\\\careapp-web\\\\src\\\\components\\\\medication\\\\ReminderPopup.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s1(ConfirmationDialog, \"JA8CxE9ZrczvRffCFoauEAbBIYg=\");\n_c1 = ConfirmationDialog;\nvar _c, _c1;\n$RefreshReg$(_c, \"ReminderPopup\");\n$RefreshReg$(_c1, \"ConfirmationDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/medication/ReminderPopup.tsx\n"));

/***/ })

});