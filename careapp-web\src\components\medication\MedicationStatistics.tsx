'use client'

import { useState, useEffect } from 'react'
import { Calendar, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, BarChart3 } from 'lucide-react'
import { useMedicationStore } from '@/store/medication'
import type { MedicationRecord } from '@/types'

interface StatisticsData {
  totalReminders: number
  takenCount: number
  missedCount: number
  skippedCount: number
  adherenceRate: number
  weeklyData: Array<{
    date: string
    taken: number
    missed: number
    total: number
  }>
  medicineStats: Array<{
    medicineName: string
    totalDoses: number
    takenDoses: number
    adherenceRate: number
  }>
}

export function MedicationStatistics({ userId }: { userId: string }) {
  const { medicationRecords, getMedicationRecords } = useMedicationStore()
  const [statistics, setStatistics] = useState<StatisticsData | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadStatistics()
  }, [userId, selectedPeriod])

  const loadStatistics = async () => {
    setLoading(true)
    try {
      await getMedicationRecords(userId)
      const stats = calculateStatistics(medicationRecords, selectedPeriod)
      setStatistics(stats)
    } catch (error) {
      console.error('加载统计数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStatistics = (records: MedicationRecord[], period: string): StatisticsData => {
    const now = new Date()
    const periodStart = getPeriodStart(now, period)
    
    // 过滤指定时间段的记录
    const filteredRecords = records.filter(record => 
      new Date(record.createdAt) >= periodStart
    )

    const totalReminders = filteredRecords.length
    const takenCount = filteredRecords.filter(r => r.status === 'taken').length
    const missedCount = filteredRecords.filter(r => r.status === 'missed').length
    const skippedCount = filteredRecords.filter(r => r.status === 'skipped').length
    const adherenceRate = totalReminders > 0 ? (takenCount / totalReminders) * 100 : 0

    // 计算每日数据
    const weeklyData = generateDailyData(filteredRecords, period)

    // 计算各药物统计
    const medicineStats = calculateMedicineStats(filteredRecords)

    return {
      totalReminders,
      takenCount,
      missedCount,
      skippedCount,
      adherenceRate,
      weeklyData,
      medicineStats
    }
  }

  const getPeriodStart = (now: Date, period: string): Date => {
    const start = new Date(now)
    switch (period) {
      case 'week':
        start.setDate(now.getDate() - 7)
        break
      case 'month':
        start.setMonth(now.getMonth() - 1)
        break
      case 'year':
        start.setFullYear(now.getFullYear() - 1)
        break
    }
    return start
  }

  const generateDailyData = (records: MedicationRecord[], period: string) => {
    const days = period === 'week' ? 7 : period === 'month' ? 30 : 365
    const data = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      const dayRecords = records.filter(record => 
        record.scheduledTime.startsWith(dateStr)
      )
      
      data.push({
        date: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        taken: dayRecords.filter(r => r.status === 'taken').length,
        missed: dayRecords.filter(r => r.status === 'missed').length,
        total: dayRecords.length
      })
    }
    
    return data
  }

  const calculateMedicineStats = (records: MedicationRecord[]) => {
    const medicineMap = new Map<string, { total: number, taken: number }>()
    
    records.forEach(record => {
      // 这里需要根据reminderId获取药品名称，暂时使用reminderId
      const medicineName = record.reminderId // 实际应该从提醒数据中获取药品名称
      
      if (!medicineMap.has(medicineName)) {
        medicineMap.set(medicineName, { total: 0, taken: 0 })
      }
      
      const stats = medicineMap.get(medicineName)!
      stats.total++
      if (record.status === 'taken') {
        stats.taken++
      }
    })
    
    return Array.from(medicineMap.entries()).map(([medicineName, stats]) => ({
      medicineName,
      totalDoses: stats.total,
      takenDoses: stats.taken,
      adherenceRate: stats.total > 0 ? (stats.taken / stats.total) * 100 : 0
    }))
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!statistics) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 text-center">
        <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">暂无统计数据</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 时间段选择 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center">
            <BarChart3 className="w-6 h-6 mr-2" />
            用药统计
          </h2>
          <div className="flex space-x-2">
            {(['week', 'month', 'year'] as const).map(period => (
              <button
                key={period}
                type="button"
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                  selectedPeriod === period
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {period === 'week' ? '近7天' : period === 'month' ? '近30天' : '近1年'}
              </button>
            ))}
          </div>
        </div>

        {/* 总体统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">总提醒次数</p>
                <p className="text-2xl font-bold text-blue-700">{statistics.totalReminders}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">已服药</p>
                <p className="text-2xl font-bold text-green-700">{statistics.takenCount}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-600 font-medium">错过</p>
                <p className="text-2xl font-bold text-red-700">{statistics.missedCount}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-500" />
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">服药率</p>
                <p className="text-2xl font-bold text-purple-700">
                  {statistics.adherenceRate.toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </div>
        </div>

        {/* 服药率趋势图 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-700 mb-4">服药趋势</h3>
          <div className="h-64 bg-gray-50 rounded-lg p-4">
            {/* 简单的条形图 */}
            <div className="h-full flex items-end space-x-2">
              {statistics.weeklyData.map((day, index) => {
                const maxHeight = Math.max(...statistics.weeklyData.map(d => d.total))
                const takenHeight = maxHeight > 0 ? (day.taken / maxHeight) * 100 : 0
                const missedHeight = maxHeight > 0 ? (day.missed / maxHeight) * 100 : 0
                
                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div className="w-full h-40 flex flex-col justify-end">
                      <div 
                        className="w-full bg-green-400 rounded-t"
                        style={{ height: `${takenHeight}%` }}
                        title={`已服药: ${day.taken}`}
                      />
                      <div 
                        className="w-full bg-red-400"
                        style={{ height: `${missedHeight}%` }}
                        title={`错过: ${day.missed}`}
                      />
                    </div>
                    <p className="text-xs text-gray-600 mt-2">{day.date}</p>
                  </div>
                )
              })}
            </div>
          </div>
          <div className="flex justify-center space-x-4 mt-2">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-400 rounded mr-2"></div>
              <span className="text-sm text-gray-600">已服药</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-400 rounded mr-2"></div>
              <span className="text-sm text-gray-600">错过</span>
            </div>
          </div>
        </div>

        {/* 各药物统计 */}
        {statistics.medicineStats.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-700 mb-4">各药物服药情况</h3>
            <div className="space-y-3">
              {statistics.medicineStats.map((medicine, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-800">{medicine.medicineName}</h4>
                    <span className="text-sm text-gray-600">
                      {medicine.takenDoses}/{medicine.totalDoses} 次
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${medicine.adherenceRate}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    服药率: {medicine.adherenceRate.toFixed(1)}%
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
