import { supabase } from './supabase'
import type { 
  MedicineR<PERSON>inder, 
  DailySchedule, 
  MedicationRecord, 
  ReminderSettings,
  GuardianContact 
} from '@/types'

export class MedicationService {
  // 药品提醒相关方法
  async createMedicineReminder(reminder: Omit<MedicineReminder, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data, error } = await supabase
      .from('medicine_reminders')
      .insert({
        medicine_name: reminder.medicineName,
        dosage: reminder.dosage,
        usage: reminder.usage,
        frequency: reminder.frequency,
        duration: reminder.duration,
        scheduled_times: reminder.scheduledTimes,
        is_enabled: reminder.isEnabled,
        user_id: reminder.userId
      })
      .select()
      .single()

    if (error) throw new Error(error.message)
    return this.mapMedicineReminderFromDB(data)
  }

  async getMedicineReminders(userId: string): Promise<MedicineReminder[]> {
    const { data, error } = await supabase
      .from('medicine_reminders')
      .select('*')
      .eq('user_id', userId)
      .eq('is_enabled', true)
      .order('created_at', { ascending: false })

    if (error) throw new Error(error.message)
    return data.map(this.mapMedicineReminderFromDB)
  }

  async updateMedicineReminder(id: string, updates: Partial<MedicineReminder>) {
    const { data, error } = await supabase
      .from('medicine_reminders')
      .update({
        medicine_name: updates.medicineName,
        dosage: updates.dosage,
        usage: updates.usage,
        frequency: updates.frequency,
        duration: updates.duration,
        scheduled_times: updates.scheduledTimes,
        is_enabled: updates.isEnabled,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw new Error(error.message)
    return this.mapMedicineReminderFromDB(data)
  }

  async deleteMedicineReminder(id: string) {
    const { error } = await supabase
      .from('medicine_reminders')
      .delete()
      .eq('id', id)

    if (error) throw new Error(error.message)
  }

  // 作息时间配置相关方法
  async saveDailySchedule(schedule: Omit<DailySchedule, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data, error } = await supabase
      .from('daily_schedules')
      .upsert({
        user_id: schedule.userId,
        wake_up_time: schedule.wakeUpTime,
        breakfast_time: schedule.breakfastTime,
        lunch_time: schedule.lunchTime,
        dinner_time: schedule.dinnerTime,
        bed_time: schedule.bedTime,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw new Error(error.message)
    return this.mapDailyScheduleFromDB(data)
  }

  async getDailySchedule(userId: string): Promise<DailySchedule | null> {
    const { data, error } = await supabase
      .from('daily_schedules')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error && error.code !== 'PGRST116') throw new Error(error.message)
    return data ? this.mapDailyScheduleFromDB(data) : null
  }

  // 用药记录相关方法
  async recordMedication(record: Omit<MedicationRecord, 'id' | 'createdAt'>) {
    const { data, error } = await supabase
      .from('medication_records')
      .insert({
        reminder_id: record.reminderId,
        scheduled_time: record.scheduledTime,
        actual_time: record.actualTime,
        status: record.status,
        confirmation_method: record.confirmationMethod,
        notes: record.notes,
        user_id: record.userId
      })
      .select()
      .single()

    if (error) throw new Error(error.message)
    return this.mapMedicationRecordFromDB(data)
  }

  async getMedicationRecords(userId: string, startDate?: string, endDate?: string): Promise<MedicationRecord[]> {
    let query = supabase
      .from('medication_records')
      .select('*')
      .eq('user_id', userId)
      .order('scheduled_time', { ascending: false })

    if (startDate) {
      query = query.gte('scheduled_time', startDate)
    }
    if (endDate) {
      query = query.lte('scheduled_time', endDate)
    }

    const { data, error } = await query
    if (error) throw new Error(error.message)
    return data.map(this.mapMedicationRecordFromDB)
  }

  // 提醒设置相关方法
  async saveReminderSettings(settings: Omit<ReminderSettings, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data, error } = await supabase
      .from('reminder_settings')
      .upsert({
        user_id: settings.userId,
        first_reminder_minutes: settings.firstReminderMinutes,
        reminder_interval: settings.reminderInterval,
        max_reminders: settings.maxReminders,
        sound_enabled: settings.soundEnabled,
        voice_enabled: settings.voiceEnabled,
        notification_enabled: settings.notificationEnabled,
        guardian_notification_delay: settings.guardianNotificationDelay,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw new Error(error.message)
    return this.mapReminderSettingsFromDB(data)
  }

  async getReminderSettings(userId: string): Promise<ReminderSettings | null> {
    const { data, error } = await supabase
      .from('reminder_settings')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error && error.code !== 'PGRST116') throw new Error(error.message)
    return data ? this.mapReminderSettingsFromDB(data) : null
  }

  // 监护人联系人相关方法
  async addGuardianContact(contact: Omit<GuardianContact, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data, error } = await supabase
      .from('guardian_contacts')
      .insert({
        user_id: contact.userId,
        guardian_name: contact.guardianName,
        guardian_email: contact.guardianEmail,
        guardian_phone: contact.guardianPhone,
        relationship: contact.relationship,
        priority: contact.priority,
        notification_methods: contact.notificationMethods,
        is_enabled: contact.isEnabled
      })
      .select()
      .single()

    if (error) throw new Error(error.message)
    return this.mapGuardianContactFromDB(data)
  }

  async getGuardianContacts(userId: string): Promise<GuardianContact[]> {
    const { data, error } = await supabase
      .from('guardian_contacts')
      .select('*')
      .eq('user_id', userId)
      .eq('is_enabled', true)
      .order('priority', { ascending: true })

    if (error) throw new Error(error.message)
    return data.map(this.mapGuardianContactFromDB)
  }

  // 数据映射方法
  private mapMedicineReminderFromDB(data: any): MedicineReminder {
    return {
      id: data.id,
      medicineName: data.medicine_name,
      dosage: data.dosage,
      usage: data.usage,
      frequency: data.frequency,
      duration: data.duration,
      scheduledTimes: data.scheduled_times || [],
      isEnabled: data.is_enabled,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      userId: data.user_id
    }
  }

  private mapDailyScheduleFromDB(data: any): DailySchedule {
    return {
      id: data.id,
      userId: data.user_id,
      wakeUpTime: data.wake_up_time,
      breakfastTime: data.breakfast_time,
      lunchTime: data.lunch_time,
      dinnerTime: data.dinner_time,
      bedTime: data.bed_time,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    }
  }

  private mapMedicationRecordFromDB(data: any): MedicationRecord {
    return {
      id: data.id,
      reminderId: data.reminder_id,
      scheduledTime: data.scheduled_time,
      actualTime: data.actual_time,
      status: data.status,
      confirmationMethod: data.confirmation_method,
      notes: data.notes,
      createdAt: data.created_at,
      userId: data.user_id
    }
  }

  private mapReminderSettingsFromDB(data: any): ReminderSettings {
    return {
      id: data.id,
      userId: data.user_id,
      firstReminderMinutes: data.first_reminder_minutes,
      reminderInterval: data.reminder_interval,
      maxReminders: data.max_reminders,
      soundEnabled: data.sound_enabled,
      voiceEnabled: data.voice_enabled,
      notificationEnabled: data.notification_enabled,
      guardianNotificationDelay: data.guardian_notification_delay,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    }
  }

  private mapGuardianContactFromDB(data: any): GuardianContact {
    return {
      id: data.id,
      userId: data.user_id,
      guardianName: data.guardian_name,
      guardianEmail: data.guardian_email,
      guardianPhone: data.guardian_phone,
      relationship: data.relationship,
      priority: data.priority,
      notificationMethods: data.notification_methods || [],
      isEnabled: data.is_enabled,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    }
  }
}

export const medicationService = new MedicationService()
