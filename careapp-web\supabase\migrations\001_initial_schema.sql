-- 照护宝数据库初始化脚本
-- 基于Android版Room数据库设计

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar TEXT,
    role VARCHAR(20) DEFAULT 'patient' CHECK (role IN ('patient', 'family', 'caregiver')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用药提醒表
CREATE TABLE medicine_reminders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    medicine_name VARCHAR(100) NOT NULL,
    dosage VARCHAR(50) NOT NULL,
    usage VARCHAR(200) NOT NULL, -- 用法说明（餐前30分钟、餐后1小时等）
    frequency VARCHAR(100) NOT NULL, -- 用药频次（每日1次、每日3次等）
    duration INTEGER, -- 疗程时长（天数）
    scheduled_times JSONB, -- 计算出的用药时间列表
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- 个人作息时间配置表
CREATE TABLE daily_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    wake_up_time TIME NOT NULL, -- 起床时间
    breakfast_time TIME NOT NULL, -- 早餐时间
    lunch_time TIME NOT NULL, -- 午餐时间
    dinner_time TIME NOT NULL, -- 晚餐时间
    bed_time TIME NOT NULL, -- 就寝时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 用药记录表
CREATE TABLE medication_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reminder_id UUID NOT NULL REFERENCES medicine_reminders(id) ON DELETE CASCADE,
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL, -- 计划用药时间
    actual_time TIMESTAMP WITH TIME ZONE, -- 实际用药时间
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'taken', 'missed', 'skipped')),
    confirmation_method VARCHAR(20) NOT NULL CHECK (confirmation_method IN ('manual', 'auto', 'guardian')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- 提醒设置表
CREATE TABLE reminder_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    first_reminder_minutes INTEGER DEFAULT 15, -- 第一级提醒提前时间（分钟）
    reminder_interval INTEGER DEFAULT 5, -- 重复提醒间隔（分钟）
    max_reminders INTEGER DEFAULT 6, -- 最大提醒次数
    sound_enabled BOOLEAN DEFAULT true, -- 声音提醒
    voice_enabled BOOLEAN DEFAULT true, -- 语音播报
    notification_enabled BOOLEAN DEFAULT true, -- 浏览器通知
    guardian_notification_delay INTEGER DEFAULT 30, -- 监护人通知延迟（分钟）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 监护人联系人表
CREATE TABLE guardian_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 患者ID
    guardian_name VARCHAR(100) NOT NULL,
    guardian_email VARCHAR(255),
    guardian_phone VARCHAR(20),
    relationship VARCHAR(50) NOT NULL, -- 关系（子女、配偶等）
    priority INTEGER NOT NULL DEFAULT 1, -- 优先级
    notification_methods JSONB, -- 通知方式数组 ['email', 'sms', 'app']
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 健康数据表
CREATE TABLE health_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('blood_pressure', 'blood_sugar', 'heart_rate', 'temperature')),
    value DECIMAL(10,2) NOT NULL,
    unit VARCHAR(10) NOT NULL,
    measured_at TIMESTAMP WITH TIME ZONE NOT NULL,
    notes TEXT,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id UUID,
    -- 血压专用字段
    systolic INTEGER, -- 收缩压
    diastolic INTEGER, -- 舒张压
    pulse INTEGER, -- 脉搏
    -- 血糖专用字段
    measurement_type VARCHAR(20) CHECK (measurement_type IN ('fasting', 'postprandial', 'random')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 紧急联系人表
CREATE TABLE emergency_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    priority INTEGER NOT NULL DEFAULT 1,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 紧急呼叫记录表
CREATE TABLE emergency_calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('manual', 'automatic')),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    address TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'responded', 'resolved')),
    contacted_persons JSONB, -- 存储已联系的人员信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- 家庭消息表
CREATE TABLE family_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    receiver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'text' CHECK (type IN ('text', 'voice', 'image')),
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('normal', 'important', 'urgent')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- 设备表
CREATE TABLE devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(30) NOT NULL CHECK (type IN ('blood_pressure_monitor', 'glucose_meter', 'smart_watch', 'other')),
    brand VARCHAR(50),
    model VARCHAR(50),
    connection_type VARCHAR(20) CHECK (connection_type IN ('bluetooth', 'wifi', 'usb')),
    is_connected BOOLEAN DEFAULT false,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_medicine_reminders_user_id ON medicine_reminders(user_id);
CREATE INDEX idx_medicine_reminders_enabled ON medicine_reminders(is_enabled);
CREATE INDEX idx_daily_schedules_user_id ON daily_schedules(user_id);
CREATE INDEX idx_medication_records_reminder_id ON medication_records(reminder_id);
CREATE INDEX idx_medication_records_user_id ON medication_records(user_id);
CREATE INDEX idx_medication_records_scheduled_time ON medication_records(scheduled_time);
CREATE INDEX idx_medication_records_status ON medication_records(status);
CREATE INDEX idx_reminder_settings_user_id ON reminder_settings(user_id);
CREATE INDEX idx_guardian_contacts_user_id ON guardian_contacts(user_id);
CREATE INDEX idx_guardian_contacts_priority ON guardian_contacts(priority);
CREATE INDEX idx_health_data_user_id ON health_data(user_id);
CREATE INDEX idx_health_data_type ON health_data(type);
CREATE INDEX idx_health_data_measured_at ON health_data(measured_at);
CREATE INDEX idx_emergency_contacts_user_id ON emergency_contacts(user_id);
CREATE INDEX idx_emergency_calls_user_id ON emergency_calls(user_id);
CREATE INDEX idx_family_messages_sender_id ON family_messages(sender_id);
CREATE INDEX idx_family_messages_receiver_id ON family_messages(receiver_id);
CREATE INDEX idx_devices_user_id ON devices(user_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_medicine_reminders_updated_at BEFORE UPDATE ON medicine_reminders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_schedules_updated_at BEFORE UPDATE ON daily_schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reminder_settings_updated_at BEFORE UPDATE ON reminder_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_guardian_contacts_updated_at BEFORE UPDATE ON guardian_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_emergency_contacts_updated_at BEFORE UPDATE ON emergency_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_devices_updated_at BEFORE UPDATE ON devices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
