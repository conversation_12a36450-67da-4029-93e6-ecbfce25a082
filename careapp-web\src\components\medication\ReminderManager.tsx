'use client'

import { useState, useEffect } from 'react'
import { ReminderPopup, ConfirmationDialog } from './ReminderPopup'
import { getNotificationService } from '@/lib/notification-service'
import type { ReminderNotification } from '@/lib/notification-service'

export function ReminderManager() {
  const [currentReminder, setCurrentReminder] = useState<ReminderNotification | null>(null)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [confirmationReminder, setConfirmationReminder] = useState<ReminderNotification | null>(null)

  useEffect(() => {
    if (typeof window === 'undefined') return

    // 监听提醒事件
    const handleReminderPopup = (event: CustomEvent) => {
      const notification = event.detail as ReminderNotification
      setCurrentReminder(notification)
    }

    const handleReminderPopupUrgent = (event: CustomEvent) => {
      const notification = event.detail as ReminderNotification
      setCurrentReminder(notification)
    }

    const handleConfirmationDialog = (event: CustomEvent) => {
      const notification = event.detail as ReminderNotification
      setConfirmationReminder(notification)
      setShowConfirmation(true)
    }

    const handleConfirmationDialogUrgent = (event: CustomEvent) => {
      const notification = event.detail as ReminderNotification
      setConfirmationReminder(notification)
      setShowConfirmation(true)
    }

    const handleGuardianNotification = (event: CustomEvent) => {
      const { notification, message } = event.detail
      // 显示监护人通知
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('监护人通知', {
          body: message,
          icon: '/icons/guardian.png',
          tag: 'guardian-notification'
        })
      }
      
      // 也可以在页面上显示通知
      console.log('监护人通知:', message, notification)
    }

    // 添加事件监听器
    window.addEventListener('medication-reminder-popup', handleReminderPopup as EventListener)
    window.addEventListener('medication-reminder-popup-urgent', handleReminderPopupUrgent as EventListener)
    window.addEventListener('medication-confirmation-dialog', handleConfirmationDialog as EventListener)
    window.addEventListener('medication-confirmation-dialog-urgent', handleConfirmationDialogUrgent as EventListener)
    window.addEventListener('medication-guardian-notification', handleGuardianNotification as EventListener)

    return () => {
      window.removeEventListener('medication-reminder-popup', handleReminderPopup as EventListener)
      window.removeEventListener('medication-reminder-popup-urgent', handleReminderPopupUrgent as EventListener)
      window.removeEventListener('medication-confirmation-dialog', handleConfirmationDialog as EventListener)
      window.removeEventListener('medication-confirmation-dialog-urgent', handleConfirmationDialogUrgent as EventListener)
      window.removeEventListener('medication-guardian-notification', handleGuardianNotification as EventListener)
    }
  }, [])

  const handleReminderConfirm = (confirmed: boolean) => {
    if (currentReminder) {
      const notificationService = getNotificationService()
      notificationService.handleReminderConfirmation(currentReminder.id, confirmed)
      setCurrentReminder(null)
    }
  }

  const handleReminderClose = () => {
    setCurrentReminder(null)
  }

  const handleConfirmationSubmit = (confirmed: boolean) => {
    if (confirmationReminder) {
      const notificationService = getNotificationService()
      notificationService.handleReminderConfirmation(confirmationReminder.id, confirmed)
      setConfirmationReminder(null)
      setShowConfirmation(false)
    }
  }

  const handleConfirmationClose = () => {
    setShowConfirmation(false)
    setConfirmationReminder(null)
  }

  return (
    <>
      {/* 提醒弹窗 */}
      {currentReminder && (
        <ReminderPopup
          notification={currentReminder}
          onConfirm={handleReminderConfirm}
          onClose={handleReminderClose}
        />
      )}

      {/* 确认对话框 */}
      {showConfirmation && confirmationReminder && (
        <ConfirmationDialog
          notification={confirmationReminder}
          onConfirm={handleConfirmationSubmit}
          onClose={handleConfirmationClose}
        />
      )}
    </>
  )
}
